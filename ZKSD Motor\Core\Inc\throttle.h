#ifndef __THROTTLE_H
#define __THROTTLE_H
#include <stdint.h>
#ifdef __cplusplus
extern "C" {
#endif
// 初始化油门采集与PWM输出模块
void Throttle_Init(void);
// 油门采集与PWM输出主循环（需在主循环中周期调用）
void Throttle_Loop(void);
void Throttle_SetThreshold(uint16_t low, uint16_t high);
float Throttle_GetPercent(void);
void Throttle_SetDuty(float percent);
void Throttle_TestPWM(void);
void Throttle_SetFixedDuty(float duty);
uint16_t Throttle_GetADCValue(void);
extern volatile uint16_t g_throttle_adc_value;
#ifdef __cplusplus
}
#endif
#endif // __THROTTLE_H 