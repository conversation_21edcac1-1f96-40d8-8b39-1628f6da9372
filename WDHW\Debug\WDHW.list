
WDHW.elf:     file format elf32-littlearm

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .isr_vector   00000188  08000000  08000000  00001000  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  1 .text         00003a5c  08000188  08000188  00001188  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, CODE
  2 .rodata       00000030  08003be4  08003be4  00004be4  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  3 .ARM.extab    00000000  08003c14  08003c14  0000500c  2**0
                  CONTENTS, READONLY
  4 .ARM          00000000  08003c14  08003c14  0000500c  2**0
                  CONTENTS, READONLY
  5 .preinit_array 00000000  08003c14  08003c14  0000500c  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  6 .init_array   00000004  08003c14  08003c14  00004c14  2**2
                  CONTENTS, ALLOC, LOAD, REA<PERSON><PERSON><PERSON><PERSON>, DATA
  7 .fini_array   00000004  08003c18  08003c18  00004c18  2**2
                  CONT<PERSON>TS, ALLOC, LOAD, READONLY, DATA
  8 .data         0000000c  20000000  08003c1c  00005000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  9 .ccmram       00000000  10000000  10000000  0000500c  2**0
                  CONTENTS
 10 .bss          000010d4  2000000c  2000000c  0000500c  2**2
                  ALLOC
 11 ._user_heap_stack 00000600  200010e0  200010e0  0000500c  2**0
                  ALLOC
 12 .ARM.attributes 00000030  00000000  00000000  0000500c  2**0
                  CONTENTS, READONLY
 13 .debug_info   0000a011  00000000  00000000  0000503c  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 14 .debug_abbrev 000022e3  00000000  00000000  0000f04d  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 15 .debug_aranges 00000928  00000000  00000000  00011330  2**3
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 16 .debug_rnglists 000006b6  00000000  00000000  00011c58  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 17 .debug_macro  0001d92d  00000000  00000000  0001230e  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 18 .debug_line   0000df58  00000000  00000000  0002fc3b  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 19 .debug_str    000af23d  00000000  00000000  0003db93  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 20 .comment      00000043  00000000  00000000  000ecdd0  2**0
                  CONTENTS, READONLY
 21 .debug_frame  000022b4  00000000  00000000  000ece14  2**2
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 22 .debug_line_str 00000060  00000000  00000000  000ef0c8  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS

Disassembly of section .text:

08000188 <__do_global_dtors_aux>:
 8000188:	b510      	push	{r4, lr}
 800018a:	4c05      	ldr	r4, [pc, #20]	@ (80001a0 <__do_global_dtors_aux+0x18>)
 800018c:	7823      	ldrb	r3, [r4, #0]
 800018e:	b933      	cbnz	r3, 800019e <__do_global_dtors_aux+0x16>
 8000190:	4b04      	ldr	r3, [pc, #16]	@ (80001a4 <__do_global_dtors_aux+0x1c>)
 8000192:	b113      	cbz	r3, 800019a <__do_global_dtors_aux+0x12>
 8000194:	4804      	ldr	r0, [pc, #16]	@ (80001a8 <__do_global_dtors_aux+0x20>)
 8000196:	f3af 8000 	nop.w
 800019a:	2301      	movs	r3, #1
 800019c:	7023      	strb	r3, [r4, #0]
 800019e:	bd10      	pop	{r4, pc}
 80001a0:	2000000c 	.word	0x2000000c
 80001a4:	00000000 	.word	0x00000000
 80001a8:	08003bcc 	.word	0x08003bcc

080001ac <frame_dummy>:
 80001ac:	b508      	push	{r3, lr}
 80001ae:	4b03      	ldr	r3, [pc, #12]	@ (80001bc <frame_dummy+0x10>)
 80001b0:	b11b      	cbz	r3, 80001ba <frame_dummy+0xe>
 80001b2:	4903      	ldr	r1, [pc, #12]	@ (80001c0 <frame_dummy+0x14>)
 80001b4:	4803      	ldr	r0, [pc, #12]	@ (80001c4 <frame_dummy+0x18>)
 80001b6:	f3af 8000 	nop.w
 80001ba:	bd08      	pop	{r3, pc}
 80001bc:	00000000 	.word	0x00000000
 80001c0:	20000010 	.word	0x20000010
 80001c4:	08003bcc 	.word	0x08003bcc

080001c8 <MX_ADC1_Init>:
ADC_HandleTypeDef hadc1;
DMA_HandleTypeDef hdma_adc1;

/* ADC1 init function */
void MX_ADC1_Init(void)
{
 80001c8:	b580      	push	{r7, lr}
 80001ca:	b08a      	sub	sp, #40	@ 0x28
 80001cc:	af00      	add	r7, sp, #0

  /* USER CODE BEGIN ADC1_Init 0 */

  /* USER CODE END ADC1_Init 0 */

  ADC_MultiModeTypeDef multimode = {0};
 80001ce:	f107 031c 	add.w	r3, r7, #28
 80001d2:	2200      	movs	r2, #0
 80001d4:	601a      	str	r2, [r3, #0]
 80001d6:	605a      	str	r2, [r3, #4]
 80001d8:	609a      	str	r2, [r3, #8]
  ADC_ChannelConfTypeDef sConfig = {0};
 80001da:	1d3b      	adds	r3, r7, #4
 80001dc:	2200      	movs	r2, #0
 80001de:	601a      	str	r2, [r3, #0]
 80001e0:	605a      	str	r2, [r3, #4]
 80001e2:	609a      	str	r2, [r3, #8]
 80001e4:	60da      	str	r2, [r3, #12]
 80001e6:	611a      	str	r2, [r3, #16]
 80001e8:	615a      	str	r2, [r3, #20]

  /* USER CODE END ADC1_Init 1 */

  /** Common config
  */
  hadc1.Instance = ADC1;
 80001ea:	4b2e      	ldr	r3, [pc, #184]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 80001ec:	f04f 42a0 	mov.w	r2, #1342177280	@ 0x50000000
 80001f0:	601a      	str	r2, [r3, #0]
  hadc1.Init.ClockPrescaler = ADC_CLOCK_ASYNC_DIV1;
 80001f2:	4b2c      	ldr	r3, [pc, #176]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 80001f4:	2200      	movs	r2, #0
 80001f6:	605a      	str	r2, [r3, #4]
  hadc1.Init.Resolution = ADC_RESOLUTION_12B;
 80001f8:	4b2a      	ldr	r3, [pc, #168]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 80001fa:	2200      	movs	r2, #0
 80001fc:	609a      	str	r2, [r3, #8]
  hadc1.Init.ScanConvMode = ADC_SCAN_ENABLE;
 80001fe:	4b29      	ldr	r3, [pc, #164]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000200:	2201      	movs	r2, #1
 8000202:	611a      	str	r2, [r3, #16]
  hadc1.Init.ContinuousConvMode = ENABLE;
 8000204:	4b27      	ldr	r3, [pc, #156]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000206:	2201      	movs	r2, #1
 8000208:	765a      	strb	r2, [r3, #25]
  hadc1.Init.DiscontinuousConvMode = DISABLE;
 800020a:	4b26      	ldr	r3, [pc, #152]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 800020c:	2200      	movs	r2, #0
 800020e:	f883 2020 	strb.w	r2, [r3, #32]
  hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
 8000212:	4b24      	ldr	r3, [pc, #144]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000214:	2200      	movs	r2, #0
 8000216:	62da      	str	r2, [r3, #44]	@ 0x2c
  hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
 8000218:	4b22      	ldr	r3, [pc, #136]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 800021a:	2201      	movs	r2, #1
 800021c:	629a      	str	r2, [r3, #40]	@ 0x28
  hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
 800021e:	4b21      	ldr	r3, [pc, #132]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000220:	2200      	movs	r2, #0
 8000222:	60da      	str	r2, [r3, #12]
  hadc1.Init.NbrOfConversion = 1;
 8000224:	4b1f      	ldr	r3, [pc, #124]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000226:	2201      	movs	r2, #1
 8000228:	61da      	str	r2, [r3, #28]
  hadc1.Init.DMAContinuousRequests = ENABLE;
 800022a:	4b1e      	ldr	r3, [pc, #120]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 800022c:	2201      	movs	r2, #1
 800022e:	f883 2030 	strb.w	r2, [r3, #48]	@ 0x30
  hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
 8000232:	4b1c      	ldr	r3, [pc, #112]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000234:	2204      	movs	r2, #4
 8000236:	615a      	str	r2, [r3, #20]
  hadc1.Init.LowPowerAutoWait = DISABLE;
 8000238:	4b1a      	ldr	r3, [pc, #104]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 800023a:	2200      	movs	r2, #0
 800023c:	761a      	strb	r2, [r3, #24]
  hadc1.Init.Overrun = ADC_OVR_DATA_OVERWRITTEN;
 800023e:	4b19      	ldr	r3, [pc, #100]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000240:	2200      	movs	r2, #0
 8000242:	635a      	str	r2, [r3, #52]	@ 0x34
  if (HAL_ADC_Init(&hadc1) != HAL_OK)
 8000244:	4817      	ldr	r0, [pc, #92]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000246:	f000 fbf1 	bl	8000a2c <HAL_ADC_Init>
 800024a:	4603      	mov	r3, r0
 800024c:	2b00      	cmp	r3, #0
 800024e:	d001      	beq.n	8000254 <MX_ADC1_Init+0x8c>
  {
    Error_Handler();
 8000250:	f000 fadc 	bl	800080c <Error_Handler>
  }

  /** Configure the ADC multi-mode
  */
  multimode.Mode = ADC_MODE_INDEPENDENT;
 8000254:	2300      	movs	r3, #0
 8000256:	61fb      	str	r3, [r7, #28]
  if (HAL_ADCEx_MultiModeConfigChannel(&hadc1, &multimode) != HAL_OK)
 8000258:	f107 031c 	add.w	r3, r7, #28
 800025c:	4619      	mov	r1, r3
 800025e:	4811      	ldr	r0, [pc, #68]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 8000260:	f001 f9ca 	bl	80015f8 <HAL_ADCEx_MultiModeConfigChannel>
 8000264:	4603      	mov	r3, r0
 8000266:	2b00      	cmp	r3, #0
 8000268:	d001      	beq.n	800026e <MX_ADC1_Init+0xa6>
  {
    Error_Handler();
 800026a:	f000 facf 	bl	800080c <Error_Handler>
  }

  /** Configure Regular Channel
  */
  sConfig.Channel = ADC_CHANNEL_1;
 800026e:	2301      	movs	r3, #1
 8000270:	607b      	str	r3, [r7, #4]
  sConfig.Rank = ADC_REGULAR_RANK_1;
 8000272:	2301      	movs	r3, #1
 8000274:	60bb      	str	r3, [r7, #8]
  sConfig.SingleDiff = ADC_SINGLE_ENDED;
 8000276:	2300      	movs	r3, #0
 8000278:	613b      	str	r3, [r7, #16]
  sConfig.SamplingTime = ADC_SAMPLETIME_1CYCLE_5;
 800027a:	2300      	movs	r3, #0
 800027c:	60fb      	str	r3, [r7, #12]
  sConfig.OffsetNumber = ADC_OFFSET_NONE;
 800027e:	2300      	movs	r3, #0
 8000280:	617b      	str	r3, [r7, #20]
  sConfig.Offset = 0;
 8000282:	2300      	movs	r3, #0
 8000284:	61bb      	str	r3, [r7, #24]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 8000286:	1d3b      	adds	r3, r7, #4
 8000288:	4619      	mov	r1, r3
 800028a:	4806      	ldr	r0, [pc, #24]	@ (80002a4 <MX_ADC1_Init+0xdc>)
 800028c:	f000 feca 	bl	8001024 <HAL_ADC_ConfigChannel>
 8000290:	4603      	mov	r3, r0
 8000292:	2b00      	cmp	r3, #0
 8000294:	d001      	beq.n	800029a <MX_ADC1_Init+0xd2>
  {
    Error_Handler();
 8000296:	f000 fab9 	bl	800080c <Error_Handler>
  }
  /* USER CODE BEGIN ADC1_Init 2 */

  /* USER CODE END ADC1_Init 2 */

}
 800029a:	bf00      	nop
 800029c:	3728      	adds	r7, #40	@ 0x28
 800029e:	46bd      	mov	sp, r7
 80002a0:	bd80      	pop	{r7, pc}
 80002a2:	bf00      	nop
 80002a4:	20000028 	.word	0x20000028

080002a8 <HAL_ADC_MspInit>:

void HAL_ADC_MspInit(ADC_HandleTypeDef* adcHandle)
{
 80002a8:	b580      	push	{r7, lr}
 80002aa:	b08a      	sub	sp, #40	@ 0x28
 80002ac:	af00      	add	r7, sp, #0
 80002ae:	6078      	str	r0, [r7, #4]

  GPIO_InitTypeDef GPIO_InitStruct = {0};
 80002b0:	f107 0314 	add.w	r3, r7, #20
 80002b4:	2200      	movs	r2, #0
 80002b6:	601a      	str	r2, [r3, #0]
 80002b8:	605a      	str	r2, [r3, #4]
 80002ba:	609a      	str	r2, [r3, #8]
 80002bc:	60da      	str	r2, [r3, #12]
 80002be:	611a      	str	r2, [r3, #16]
  if(adcHandle->Instance==ADC1)
 80002c0:	687b      	ldr	r3, [r7, #4]
 80002c2:	681b      	ldr	r3, [r3, #0]
 80002c4:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 80002c8:	d14d      	bne.n	8000366 <HAL_ADC_MspInit+0xbe>
  {
  /* USER CODE BEGIN ADC1_MspInit 0 */

  /* USER CODE END ADC1_MspInit 0 */
    /* ADC1 clock enable */
    __HAL_RCC_ADC12_CLK_ENABLE();
 80002ca:	4b29      	ldr	r3, [pc, #164]	@ (8000370 <HAL_ADC_MspInit+0xc8>)
 80002cc:	695b      	ldr	r3, [r3, #20]
 80002ce:	4a28      	ldr	r2, [pc, #160]	@ (8000370 <HAL_ADC_MspInit+0xc8>)
 80002d0:	f043 5380 	orr.w	r3, r3, #268435456	@ 0x10000000
 80002d4:	6153      	str	r3, [r2, #20]
 80002d6:	4b26      	ldr	r3, [pc, #152]	@ (8000370 <HAL_ADC_MspInit+0xc8>)
 80002d8:	695b      	ldr	r3, [r3, #20]
 80002da:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 80002de:	613b      	str	r3, [r7, #16]
 80002e0:	693b      	ldr	r3, [r7, #16]

    __HAL_RCC_GPIOA_CLK_ENABLE();
 80002e2:	4b23      	ldr	r3, [pc, #140]	@ (8000370 <HAL_ADC_MspInit+0xc8>)
 80002e4:	695b      	ldr	r3, [r3, #20]
 80002e6:	4a22      	ldr	r2, [pc, #136]	@ (8000370 <HAL_ADC_MspInit+0xc8>)
 80002e8:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
 80002ec:	6153      	str	r3, [r2, #20]
 80002ee:	4b20      	ldr	r3, [pc, #128]	@ (8000370 <HAL_ADC_MspInit+0xc8>)
 80002f0:	695b      	ldr	r3, [r3, #20]
 80002f2:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 80002f6:	60fb      	str	r3, [r7, #12]
 80002f8:	68fb      	ldr	r3, [r7, #12]
    /**ADC1 GPIO Configuration
    PA0     ------> ADC1_IN1
    */
    GPIO_InitStruct.Pin = GPIO_PIN_0;
 80002fa:	2301      	movs	r3, #1
 80002fc:	617b      	str	r3, [r7, #20]
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
 80002fe:	2303      	movs	r3, #3
 8000300:	61bb      	str	r3, [r7, #24]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 8000302:	2300      	movs	r3, #0
 8000304:	61fb      	str	r3, [r7, #28]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 8000306:	f107 0314 	add.w	r3, r7, #20
 800030a:	4619      	mov	r1, r3
 800030c:	f04f 4090 	mov.w	r0, #1207959552	@ 0x48000000
 8000310:	f001 ff38 	bl	8002184 <HAL_GPIO_Init>

    /* ADC1 DMA Init */
    /* ADC1 Init */
    hdma_adc1.Instance = DMA1_Channel1;
 8000314:	4b17      	ldr	r3, [pc, #92]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 8000316:	4a18      	ldr	r2, [pc, #96]	@ (8000378 <HAL_ADC_MspInit+0xd0>)
 8000318:	601a      	str	r2, [r3, #0]
    hdma_adc1.Init.Direction = DMA_PERIPH_TO_MEMORY;
 800031a:	4b16      	ldr	r3, [pc, #88]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 800031c:	2200      	movs	r2, #0
 800031e:	605a      	str	r2, [r3, #4]
    hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;
 8000320:	4b14      	ldr	r3, [pc, #80]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 8000322:	2200      	movs	r2, #0
 8000324:	609a      	str	r2, [r3, #8]
    hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;
 8000326:	4b13      	ldr	r3, [pc, #76]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 8000328:	2280      	movs	r2, #128	@ 0x80
 800032a:	60da      	str	r2, [r3, #12]
    hdma_adc1.Init.PeriphDataAlignment = DMA_PDATAALIGN_HALFWORD;
 800032c:	4b11      	ldr	r3, [pc, #68]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 800032e:	f44f 7280 	mov.w	r2, #256	@ 0x100
 8000332:	611a      	str	r2, [r3, #16]
    hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_HALFWORD;
 8000334:	4b0f      	ldr	r3, [pc, #60]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 8000336:	f44f 6280 	mov.w	r2, #1024	@ 0x400
 800033a:	615a      	str	r2, [r3, #20]
    hdma_adc1.Init.Mode = DMA_CIRCULAR;
 800033c:	4b0d      	ldr	r3, [pc, #52]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 800033e:	2220      	movs	r2, #32
 8000340:	619a      	str	r2, [r3, #24]
    hdma_adc1.Init.Priority = DMA_PRIORITY_HIGH;
 8000342:	4b0c      	ldr	r3, [pc, #48]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 8000344:	f44f 5200 	mov.w	r2, #8192	@ 0x2000
 8000348:	61da      	str	r2, [r3, #28]
    if (HAL_DMA_Init(&hdma_adc1) != HAL_OK)
 800034a:	480a      	ldr	r0, [pc, #40]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 800034c:	f001 fd66 	bl	8001e1c <HAL_DMA_Init>
 8000350:	4603      	mov	r3, r0
 8000352:	2b00      	cmp	r3, #0
 8000354:	d001      	beq.n	800035a <HAL_ADC_MspInit+0xb2>
    {
      Error_Handler();
 8000356:	f000 fa59 	bl	800080c <Error_Handler>
    }

    __HAL_LINKDMA(adcHandle,DMA_Handle,hdma_adc1);
 800035a:	687b      	ldr	r3, [r7, #4]
 800035c:	4a05      	ldr	r2, [pc, #20]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 800035e:	639a      	str	r2, [r3, #56]	@ 0x38
 8000360:	4a04      	ldr	r2, [pc, #16]	@ (8000374 <HAL_ADC_MspInit+0xcc>)
 8000362:	687b      	ldr	r3, [r7, #4]
 8000364:	6253      	str	r3, [r2, #36]	@ 0x24

  /* USER CODE BEGIN ADC1_MspInit 1 */

  /* USER CODE END ADC1_MspInit 1 */
  }
}
 8000366:	bf00      	nop
 8000368:	3728      	adds	r7, #40	@ 0x28
 800036a:	46bd      	mov	sp, r7
 800036c:	bd80      	pop	{r7, pc}
 800036e:	bf00      	nop
 8000370:	******** 	.word	0x********
 8000374:	20000078 	.word	0x20000078
 8000378:	40020008 	.word	0x40020008

0800037c <AudioProcess_Init>:

/**
 * @brief 初始化音频处理器
 */
void AudioProcess_Init(void)
{
 800037c:	b480      	push	{r7}
 800037e:	af00      	add	r7, sp, #0
    /* 初始化代码，如果需要的话 */
}
 8000380:	bf00      	nop
 8000382:	46bd      	mov	sp, r7
 8000384:	f85d 7b04 	ldr.w	r7, [sp], #4
 8000388:	4770      	bx	lr
	...

0800038c <MX_DAC_Init>:

DAC_HandleTypeDef hdac;

/* DAC init function */
void MX_DAC_Init(void)
{
 800038c:	b580      	push	{r7, lr}
 800038e:	b084      	sub	sp, #16
 8000390:	af00      	add	r7, sp, #0

  /* USER CODE BEGIN DAC_Init 0 */

  /* USER CODE END DAC_Init 0 */

  DAC_ChannelConfTypeDef sConfig = {0};
 8000392:	1d3b      	adds	r3, r7, #4
 8000394:	2200      	movs	r2, #0
 8000396:	601a      	str	r2, [r3, #0]
 8000398:	605a      	str	r2, [r3, #4]
 800039a:	609a      	str	r2, [r3, #8]

  /* USER CODE END DAC_Init 1 */

  /** DAC Initialization
  */
  hdac.Instance = DAC;
 800039c:	4b0e      	ldr	r3, [pc, #56]	@ (80003d8 <MX_DAC_Init+0x4c>)
 800039e:	4a0f      	ldr	r2, [pc, #60]	@ (80003dc <MX_DAC_Init+0x50>)
 80003a0:	601a      	str	r2, [r3, #0]
  if (HAL_DAC_Init(&hdac) != HAL_OK)
 80003a2:	480d      	ldr	r0, [pc, #52]	@ (80003d8 <MX_DAC_Init+0x4c>)
 80003a4:	f001 fc5b 	bl	8001c5e <HAL_DAC_Init>
 80003a8:	4603      	mov	r3, r0
 80003aa:	2b00      	cmp	r3, #0
 80003ac:	d001      	beq.n	80003b2 <MX_DAC_Init+0x26>
  {
    Error_Handler();
 80003ae:	f000 fa2d 	bl	800080c <Error_Handler>
  }

  /** DAC channel OUT1 config
  */
  sConfig.DAC_Trigger = DAC_TRIGGER_NONE;
 80003b2:	2300      	movs	r3, #0
 80003b4:	607b      	str	r3, [r7, #4]
  sConfig.DAC_OutputBuffer = DAC_OUTPUTBUFFER_ENABLE;
 80003b6:	2300      	movs	r3, #0
 80003b8:	60bb      	str	r3, [r7, #8]
  if (HAL_DAC_ConfigChannel(&hdac, &sConfig, DAC_CHANNEL_1) != HAL_OK)
 80003ba:	1d3b      	adds	r3, r7, #4
 80003bc:	2200      	movs	r2, #0
 80003be:	4619      	mov	r1, r3
 80003c0:	4805      	ldr	r0, [pc, #20]	@ (80003d8 <MX_DAC_Init+0x4c>)
 80003c2:	f001 fcde 	bl	8001d82 <HAL_DAC_ConfigChannel>
 80003c6:	4603      	mov	r3, r0
 80003c8:	2b00      	cmp	r3, #0
 80003ca:	d001      	beq.n	80003d0 <MX_DAC_Init+0x44>
  {
    Error_Handler();
 80003cc:	f000 fa1e 	bl	800080c <Error_Handler>
  }
  /* USER CODE BEGIN DAC_Init 2 */

  /* USER CODE END DAC_Init 2 */

}
 80003d0:	bf00      	nop
 80003d2:	3710      	adds	r7, #16
 80003d4:	46bd      	mov	sp, r7
 80003d6:	bd80      	pop	{r7, pc}
 80003d8:	200000bc 	.word	0x200000bc
 80003dc:	40007400 	.word	0x40007400

080003e0 <HAL_DAC_MspInit>:

void HAL_DAC_MspInit(DAC_HandleTypeDef* dacHandle)
{
 80003e0:	b580      	push	{r7, lr}
 80003e2:	b08a      	sub	sp, #40	@ 0x28
 80003e4:	af00      	add	r7, sp, #0
 80003e6:	6078      	str	r0, [r7, #4]

  GPIO_InitTypeDef GPIO_InitStruct = {0};
 80003e8:	f107 0314 	add.w	r3, r7, #20
 80003ec:	2200      	movs	r2, #0
 80003ee:	601a      	str	r2, [r3, #0]
 80003f0:	605a      	str	r2, [r3, #4]
 80003f2:	609a      	str	r2, [r3, #8]
 80003f4:	60da      	str	r2, [r3, #12]
 80003f6:	611a      	str	r2, [r3, #16]
  if(dacHandle->Instance==DAC)
 80003f8:	687b      	ldr	r3, [r7, #4]
 80003fa:	681b      	ldr	r3, [r3, #0]
 80003fc:	4a15      	ldr	r2, [pc, #84]	@ (8000454 <HAL_DAC_MspInit+0x74>)
 80003fe:	4293      	cmp	r3, r2
 8000400:	d124      	bne.n	800044c <HAL_DAC_MspInit+0x6c>
  {
  /* USER CODE BEGIN DAC_MspInit 0 */

  /* USER CODE END DAC_MspInit 0 */
    /* DAC clock enable */
    __HAL_RCC_DAC1_CLK_ENABLE();
 8000402:	4b15      	ldr	r3, [pc, #84]	@ (8000458 <HAL_DAC_MspInit+0x78>)
 8000404:	69db      	ldr	r3, [r3, #28]
 8000406:	4a14      	ldr	r2, [pc, #80]	@ (8000458 <HAL_DAC_MspInit+0x78>)
 8000408:	f043 5300 	orr.w	r3, r3, #536870912	@ 0x20000000
 800040c:	61d3      	str	r3, [r2, #28]
 800040e:	4b12      	ldr	r3, [pc, #72]	@ (8000458 <HAL_DAC_MspInit+0x78>)
 8000410:	69db      	ldr	r3, [r3, #28]
 8000412:	f003 5300 	and.w	r3, r3, #536870912	@ 0x20000000
 8000416:	613b      	str	r3, [r7, #16]
 8000418:	693b      	ldr	r3, [r7, #16]
    __HAL_RCC_GPIOA_CLK_ENABLE();
 800041a:	4b0f      	ldr	r3, [pc, #60]	@ (8000458 <HAL_DAC_MspInit+0x78>)
 800041c:	695b      	ldr	r3, [r3, #20]
 800041e:	4a0e      	ldr	r2, [pc, #56]	@ (8000458 <HAL_DAC_MspInit+0x78>)
 8000420:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
 8000424:	6153      	str	r3, [r2, #20]
 8000426:	4b0c      	ldr	r3, [pc, #48]	@ (8000458 <HAL_DAC_MspInit+0x78>)
 8000428:	695b      	ldr	r3, [r3, #20]
 800042a:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 800042e:	60fb      	str	r3, [r7, #12]
 8000430:	68fb      	ldr	r3, [r7, #12]

    /**DAC GPIO Configuration
    PA4     ------> DAC_OUT1
    */
    GPIO_InitStruct.Pin = GPIO_PIN_4;
 8000432:	2310      	movs	r3, #16
 8000434:	617b      	str	r3, [r7, #20]
    GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
 8000436:	2303      	movs	r3, #3
 8000438:	61bb      	str	r3, [r7, #24]
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 800043a:	2300      	movs	r3, #0
 800043c:	61fb      	str	r3, [r7, #28]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 800043e:	f107 0314 	add.w	r3, r7, #20
 8000442:	4619      	mov	r1, r3
 8000444:	f04f 4090 	mov.w	r0, #1207959552	@ 0x48000000
 8000448:	f001 fe9c 	bl	8002184 <HAL_GPIO_Init>

  /* USER CODE BEGIN DAC_MspInit 1 */

  /* USER CODE END DAC_MspInit 1 */
  }
}
 800044c:	bf00      	nop
 800044e:	3728      	adds	r7, #40	@ 0x28
 8000450:	46bd      	mov	sp, r7
 8000452:	bd80      	pop	{r7, pc}
 8000454:	40007400 	.word	0x40007400
 8000458:	******** 	.word	0x********

0800045c <MX_DMA_Init>:

/**
  * Enable DMA controller clock
  */
void MX_DMA_Init(void)
{
 800045c:	b580      	push	{r7, lr}
 800045e:	b082      	sub	sp, #8
 8000460:	af00      	add	r7, sp, #0

  /* DMA controller clock enable */
  __HAL_RCC_DMA1_CLK_ENABLE();
 8000462:	4b0c      	ldr	r3, [pc, #48]	@ (8000494 <MX_DMA_Init+0x38>)
 8000464:	695b      	ldr	r3, [r3, #20]
 8000466:	4a0b      	ldr	r2, [pc, #44]	@ (8000494 <MX_DMA_Init+0x38>)
 8000468:	f043 0301 	orr.w	r3, r3, #1
 800046c:	6153      	str	r3, [r2, #20]
 800046e:	4b09      	ldr	r3, [pc, #36]	@ (8000494 <MX_DMA_Init+0x38>)
 8000470:	695b      	ldr	r3, [r3, #20]
 8000472:	f003 0301 	and.w	r3, r3, #1
 8000476:	607b      	str	r3, [r7, #4]
 8000478:	687b      	ldr	r3, [r7, #4]

  /* DMA interrupt init */
  /* DMA1_Channel1_IRQn interrupt configuration */
  HAL_NVIC_SetPriority(DMA1_Channel1_IRQn, 0, 0);
 800047a:	2200      	movs	r2, #0
 800047c:	2100      	movs	r1, #0
 800047e:	200b      	movs	r0, #11
 8000480:	f001 fbb7 	bl	8001bf2 <HAL_NVIC_SetPriority>
  HAL_NVIC_EnableIRQ(DMA1_Channel1_IRQn);
 8000484:	200b      	movs	r0, #11
 8000486:	f001 fbd0 	bl	8001c2a <HAL_NVIC_EnableIRQ>

}
 800048a:	bf00      	nop
 800048c:	3708      	adds	r7, #8
 800048e:	46bd      	mov	sp, r7
 8000490:	bd80      	pop	{r7, pc}
 8000492:	bf00      	nop
 8000494:	******** 	.word	0x********

08000498 <MX_GPIO_Init>:
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{
 8000498:	b480      	push	{r7}
 800049a:	b085      	sub	sp, #20
 800049c:	af00      	add	r7, sp, #0

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
 800049e:	4b15      	ldr	r3, [pc, #84]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004a0:	695b      	ldr	r3, [r3, #20]
 80004a2:	4a14      	ldr	r2, [pc, #80]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004a4:	f443 2300 	orr.w	r3, r3, #524288	@ 0x80000
 80004a8:	6153      	str	r3, [r2, #20]
 80004aa:	4b12      	ldr	r3, [pc, #72]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004ac:	695b      	ldr	r3, [r3, #20]
 80004ae:	f403 2300 	and.w	r3, r3, #524288	@ 0x80000
 80004b2:	60fb      	str	r3, [r7, #12]
 80004b4:	68fb      	ldr	r3, [r7, #12]
  __HAL_RCC_GPIOF_CLK_ENABLE();
 80004b6:	4b0f      	ldr	r3, [pc, #60]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004b8:	695b      	ldr	r3, [r3, #20]
 80004ba:	4a0e      	ldr	r2, [pc, #56]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004bc:	f443 0380 	orr.w	r3, r3, #4194304	@ 0x400000
 80004c0:	6153      	str	r3, [r2, #20]
 80004c2:	4b0c      	ldr	r3, [pc, #48]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004c4:	695b      	ldr	r3, [r3, #20]
 80004c6:	f403 0380 	and.w	r3, r3, #4194304	@ 0x400000
 80004ca:	60bb      	str	r3, [r7, #8]
 80004cc:	68bb      	ldr	r3, [r7, #8]
  __HAL_RCC_GPIOA_CLK_ENABLE();
 80004ce:	4b09      	ldr	r3, [pc, #36]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004d0:	695b      	ldr	r3, [r3, #20]
 80004d2:	4a08      	ldr	r2, [pc, #32]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004d4:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
 80004d8:	6153      	str	r3, [r2, #20]
 80004da:	4b06      	ldr	r3, [pc, #24]	@ (80004f4 <MX_GPIO_Init+0x5c>)
 80004dc:	695b      	ldr	r3, [r3, #20]
 80004de:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 80004e2:	607b      	str	r3, [r7, #4]
 80004e4:	687b      	ldr	r3, [r7, #4]

}
 80004e6:	bf00      	nop
 80004e8:	3714      	adds	r7, #20
 80004ea:	46bd      	mov	sp, r7
 80004ec:	f85d 7b04 	ldr.w	r7, [sp], #4
 80004f0:	4770      	bx	lr
 80004f2:	bf00      	nop
 80004f4:	******** 	.word	0x********

080004f8 <main>:
#define LED_PORT GPIOC

#define AUDIO_GAIN 1.65f  // 增加增益让声音更清晰

int main(void)
{
 80004f8:	b580      	push	{r7, lr}
 80004fa:	b086      	sub	sp, #24
 80004fc:	af00      	add	r7, sp, #0
  HAL_Init();
 80004fe:	f000 fa25 	bl	800094c <HAL_Init>
  SystemClock_Config();
 8000502:	f000 f849 	bl	8000598 <SystemClock_Config>
  MX_GPIO_Init();
 8000506:	f7ff ffc7 	bl	8000498 <MX_GPIO_Init>
  MX_DMA_Init();
 800050a:	f7ff ffa7 	bl	800045c <MX_DMA_Init>
  MX_ADC1_Init();
 800050e:	f7ff fe5b 	bl	80001c8 <MX_ADC1_Init>
  MX_DAC_Init();
 8000512:	f7ff ff3b 	bl	800038c <MX_DAC_Init>
  
  /* 初始化音频处理器 */
  AudioProcess_Init();
 8000516:	f7ff ff31 	bl	800037c <AudioProcess_Init>

  /* 配置LED用于调试 */
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 800051a:	1d3b      	adds	r3, r7, #4
 800051c:	2200      	movs	r2, #0
 800051e:	601a      	str	r2, [r3, #0]
 8000520:	605a      	str	r2, [r3, #4]
 8000522:	609a      	str	r2, [r3, #8]
 8000524:	60da      	str	r2, [r3, #12]
 8000526:	611a      	str	r2, [r3, #16]
  __HAL_RCC_GPIOC_CLK_ENABLE();
 8000528:	4b16      	ldr	r3, [pc, #88]	@ (8000584 <main+0x8c>)
 800052a:	695b      	ldr	r3, [r3, #20]
 800052c:	4a15      	ldr	r2, [pc, #84]	@ (8000584 <main+0x8c>)
 800052e:	f443 2300 	orr.w	r3, r3, #524288	@ 0x80000
 8000532:	6153      	str	r3, [r2, #20]
 8000534:	4b13      	ldr	r3, [pc, #76]	@ (8000584 <main+0x8c>)
 8000536:	695b      	ldr	r3, [r3, #20]
 8000538:	f403 2300 	and.w	r3, r3, #524288	@ 0x80000
 800053c:	603b      	str	r3, [r7, #0]
 800053e:	683b      	ldr	r3, [r7, #0]
  GPIO_InitStruct.Pin = LED_PIN;
 8000540:	f44f 5300 	mov.w	r3, #8192	@ 0x2000
 8000544:	607b      	str	r3, [r7, #4]
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 8000546:	2301      	movs	r3, #1
 8000548:	60bb      	str	r3, [r7, #8]
  GPIO_InitStruct.Pull = GPIO_NOPULL;
 800054a:	2300      	movs	r3, #0
 800054c:	60fb      	str	r3, [r7, #12]
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 800054e:	2300      	movs	r3, #0
 8000550:	613b      	str	r3, [r7, #16]
  HAL_GPIO_Init(LED_PORT, &GPIO_InitStruct);
 8000552:	1d3b      	adds	r3, r7, #4
 8000554:	4619      	mov	r1, r3
 8000556:	480c      	ldr	r0, [pc, #48]	@ (8000588 <main+0x90>)
 8000558:	f001 fe14 	bl	8002184 <HAL_GPIO_Init>

  /* 启动DAC并设置初始值 */
  HAL_DAC_Start(&hdac, DAC_CHANNEL_1);
 800055c:	2100      	movs	r1, #0
 800055e:	480b      	ldr	r0, [pc, #44]	@ (800058c <main+0x94>)
 8000560:	f001 fbc4 	bl	8001cec <HAL_DAC_Start>
  HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, 2048);
 8000564:	f44f 6300 	mov.w	r3, #2048	@ 0x800
 8000568:	2200      	movs	r2, #0
 800056a:	2100      	movs	r1, #0
 800056c:	4807      	ldr	r0, [pc, #28]	@ (800058c <main+0x94>)
 800056e:	f001 fb98 	bl	8001ca2 <HAL_DAC_SetValue>
  
  /* 启动ADC的DMA模式 */
  HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_buffer, AUDIO_BUFFER_SIZE);
 8000572:	f44f 6280 	mov.w	r2, #1024	@ 0x400
 8000576:	4906      	ldr	r1, [pc, #24]	@ (8000590 <main+0x98>)
 8000578:	4806      	ldr	r0, [pc, #24]	@ (8000594 <main+0x9c>)
 800057a:	f000 fc37 	bl	8000dec <HAL_ADC_Start_DMA>

  while (1)
 800057e:	bf00      	nop
 8000580:	e7fd      	b.n	800057e <main+0x86>
 8000582:	bf00      	nop
 8000584:	******** 	.word	0x********
 8000588:	48000800 	.word	0x48000800
 800058c:	200000bc 	.word	0x200000bc
 8000590:	200000d0 	.word	0x200000d0
 8000594:	20000028 	.word	0x20000028

08000598 <SystemClock_Config>:
    /* 主循环无需处理，全部由DMA和回调完成 */
  }
}

void SystemClock_Config(void)
{
 8000598:	b580      	push	{r7, lr}
 800059a:	b09e      	sub	sp, #120	@ 0x78
 800059c:	af00      	add	r7, sp, #0
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 800059e:	f107 0350 	add.w	r3, r7, #80	@ 0x50
 80005a2:	2228      	movs	r2, #40	@ 0x28
 80005a4:	2100      	movs	r1, #0
 80005a6:	4618      	mov	r0, r3
 80005a8:	f003 fae4 	bl	8003b74 <memset>
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 80005ac:	f107 033c 	add.w	r3, r7, #60	@ 0x3c
 80005b0:	2200      	movs	r2, #0
 80005b2:	601a      	str	r2, [r3, #0]
 80005b4:	605a      	str	r2, [r3, #4]
 80005b6:	609a      	str	r2, [r3, #8]
 80005b8:	60da      	str	r2, [r3, #12]
 80005ba:	611a      	str	r2, [r3, #16]
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};
 80005bc:	463b      	mov	r3, r7
 80005be:	223c      	movs	r2, #60	@ 0x3c
 80005c0:	2100      	movs	r1, #0
 80005c2:	4618      	mov	r0, r3
 80005c4:	f003 fad6 	bl	8003b74 <memset>

  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
 80005c8:	2301      	movs	r3, #1
 80005ca:	653b      	str	r3, [r7, #80]	@ 0x50
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
 80005cc:	f44f 3380 	mov.w	r3, #65536	@ 0x10000
 80005d0:	657b      	str	r3, [r7, #84]	@ 0x54
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
 80005d2:	2300      	movs	r3, #0
 80005d4:	65bb      	str	r3, [r7, #88]	@ 0x58
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
 80005d6:	2301      	movs	r3, #1
 80005d8:	663b      	str	r3, [r7, #96]	@ 0x60
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 80005da:	2302      	movs	r3, #2
 80005dc:	66fb      	str	r3, [r7, #108]	@ 0x6c
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 80005de:	f44f 3380 	mov.w	r3, #65536	@ 0x10000
 80005e2:	673b      	str	r3, [r7, #112]	@ 0x70
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
 80005e4:	f44f 13e0 	mov.w	r3, #1835008	@ 0x1c0000
 80005e8:	677b      	str	r3, [r7, #116]	@ 0x74
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 80005ea:	f107 0350 	add.w	r3, r7, #80	@ 0x50
 80005ee:	4618      	mov	r0, r3
 80005f0:	f001 ff42 	bl	8002478 <HAL_RCC_OscConfig>
 80005f4:	4603      	mov	r3, r0
 80005f6:	2b00      	cmp	r3, #0
 80005f8:	d001      	beq.n	80005fe <SystemClock_Config+0x66>
  {
    Error_Handler();
 80005fa:	f000 f907 	bl	800080c <Error_Handler>
  }

  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 80005fe:	230f      	movs	r3, #15
 8000600:	63fb      	str	r3, [r7, #60]	@ 0x3c
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
 8000602:	2302      	movs	r3, #2
 8000604:	643b      	str	r3, [r7, #64]	@ 0x40
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
 8000606:	2300      	movs	r3, #0
 8000608:	647b      	str	r3, [r7, #68]	@ 0x44
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
 800060a:	f44f 6380 	mov.w	r3, #1024	@ 0x400
 800060e:	64bb      	str	r3, [r7, #72]	@ 0x48
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
 8000610:	2300      	movs	r3, #0
 8000612:	64fb      	str	r3, [r7, #76]	@ 0x4c

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
 8000614:	f107 033c 	add.w	r3, r7, #60	@ 0x3c
 8000618:	2102      	movs	r1, #2
 800061a:	4618      	mov	r0, r3
 800061c:	f002 ff3a 	bl	8003494 <HAL_RCC_ClockConfig>
 8000620:	4603      	mov	r3, r0
 8000622:	2b00      	cmp	r3, #0
 8000624:	d001      	beq.n	800062a <SystemClock_Config+0x92>
  {
    Error_Handler();
 8000626:	f000 f8f1 	bl	800080c <Error_Handler>
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_ADC12;
 800062a:	2380      	movs	r3, #128	@ 0x80
 800062c:	603b      	str	r3, [r7, #0]
  PeriphClkInit.Adc12ClockSelection = RCC_ADC12PLLCLK_DIV1;
 800062e:	f44f 7380 	mov.w	r3, #256	@ 0x100
 8000632:	627b      	str	r3, [r7, #36]	@ 0x24
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
 8000634:	463b      	mov	r3, r7
 8000636:	4618      	mov	r0, r3
 8000638:	f003 f8ee 	bl	8003818 <HAL_RCCEx_PeriphCLKConfig>
 800063c:	4603      	mov	r3, r0
 800063e:	2b00      	cmp	r3, #0
 8000640:	d001      	beq.n	8000646 <SystemClock_Config+0xae>
  {
    Error_Handler();
 8000642:	f000 f8e3 	bl	800080c <Error_Handler>
  }
}
 8000646:	bf00      	nop
 8000648:	3778      	adds	r7, #120	@ 0x78
 800064a:	46bd      	mov	sp, r7
 800064c:	bd80      	pop	{r7, pc}
	...

08000650 <HAL_ADC_ConvCpltCallback>:

/* ADC DMA回调函数 */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
 8000650:	b580      	push	{r7, lr}
 8000652:	b086      	sub	sp, #24
 8000654:	af00      	add	r7, sp, #0
 8000656:	6078      	str	r0, [r7, #4]
    uint16_t half_size = AUDIO_BUFFER_SIZE / 2;
 8000658:	f44f 7300 	mov.w	r3, #512	@ 0x200
 800065c:	81fb      	strh	r3, [r7, #14]
    static int32_t prev = 0;
    for(uint16_t i = 0; i < half_size; i++) {
 800065e:	2300      	movs	r3, #0
 8000660:	82fb      	strh	r3, [r7, #22]
 8000662:	e047      	b.n	80006f4 <HAL_ADC_ConvCpltCallback+0xa4>
        // 将ADC值转换为以1.65V为中心的交流信号
        int32_t sample = (int32_t)adc_buffer[i + half_size] - 2048; // 去直流，得到交流分量
 8000664:	8afa      	ldrh	r2, [r7, #22]
 8000666:	89fb      	ldrh	r3, [r7, #14]
 8000668:	4413      	add	r3, r2
 800066a:	4a2c      	ldr	r2, [pc, #176]	@ (800071c <HAL_ADC_ConvCpltCallback+0xcc>)
 800066c:	f832 3013 	ldrh.w	r3, [r2, r3, lsl #1]
 8000670:	f5a3 6300 	sub.w	r3, r3, #2048	@ 0x800
 8000674:	613b      	str	r3, [r7, #16]
        
        // 轻微滤波（递归平均）
        sample = (sample + prev) / 2;
 8000676:	4b2a      	ldr	r3, [pc, #168]	@ (8000720 <HAL_ADC_ConvCpltCallback+0xd0>)
 8000678:	681a      	ldr	r2, [r3, #0]
 800067a:	693b      	ldr	r3, [r7, #16]
 800067c:	4413      	add	r3, r2
 800067e:	0fda      	lsrs	r2, r3, #31
 8000680:	4413      	add	r3, r2
 8000682:	105b      	asrs	r3, r3, #1
 8000684:	613b      	str	r3, [r7, #16]
        prev = sample;
 8000686:	4a26      	ldr	r2, [pc, #152]	@ (8000720 <HAL_ADC_ConvCpltCallback+0xd0>)
 8000688:	693b      	ldr	r3, [r7, #16]
 800068a:	6013      	str	r3, [r2, #0]
        
        // 增益处理
        sample = sample * AUDIO_GAIN;
 800068c:	693b      	ldr	r3, [r7, #16]
 800068e:	ee07 3a90 	vmov	s15, r3
 8000692:	eef8 7ae7 	vcvt.f32.s32	s15, s15
 8000696:	ed9f 7a23 	vldr	s14, [pc, #140]	@ 8000724 <HAL_ADC_ConvCpltCallback+0xd4>
 800069a:	ee67 7a87 	vmul.f32	s15, s15, s14
 800069e:	eefd 7ae7 	vcvt.s32.f32	s15, s15
 80006a2:	ee17 3a90 	vmov	r3, s15
 80006a6:	613b      	str	r3, [r7, #16]
        
        // 将交流信号重新映射到DAC的1.65V中心点
        sample += 2048; // 加回1.65V偏置
 80006a8:	693b      	ldr	r3, [r7, #16]
 80006aa:	f503 6300 	add.w	r3, r3, #2048	@ 0x800
 80006ae:	613b      	str	r3, [r7, #16]
        
        // 限幅保护
        if(sample < 0) sample = 0;
 80006b0:	693b      	ldr	r3, [r7, #16]
 80006b2:	2b00      	cmp	r3, #0
 80006b4:	da01      	bge.n	80006ba <HAL_ADC_ConvCpltCallback+0x6a>
 80006b6:	2300      	movs	r3, #0
 80006b8:	613b      	str	r3, [r7, #16]
        if(sample > 4095) sample = 4095;
 80006ba:	693b      	ldr	r3, [r7, #16]
 80006bc:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 80006c0:	db02      	blt.n	80006c8 <HAL_ADC_ConvCpltCallback+0x78>
 80006c2:	f640 73ff 	movw	r3, #4095	@ 0xfff
 80006c6:	613b      	str	r3, [r7, #16]
        
        dac_buffer[i + half_size] = (uint16_t)sample;
 80006c8:	8afa      	ldrh	r2, [r7, #22]
 80006ca:	89fb      	ldrh	r3, [r7, #14]
 80006cc:	4413      	add	r3, r2
 80006ce:	693a      	ldr	r2, [r7, #16]
 80006d0:	b291      	uxth	r1, r2
 80006d2:	4a15      	ldr	r2, [pc, #84]	@ (8000728 <HAL_ADC_ConvCpltCallback+0xd8>)
 80006d4:	f822 1013 	strh.w	r1, [r2, r3, lsl #1]
        HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_buffer[i + half_size]);
 80006d8:	8afa      	ldrh	r2, [r7, #22]
 80006da:	89fb      	ldrh	r3, [r7, #14]
 80006dc:	4413      	add	r3, r2
 80006de:	4a12      	ldr	r2, [pc, #72]	@ (8000728 <HAL_ADC_ConvCpltCallback+0xd8>)
 80006e0:	f832 3013 	ldrh.w	r3, [r2, r3, lsl #1]
 80006e4:	2200      	movs	r2, #0
 80006e6:	2100      	movs	r1, #0
 80006e8:	4810      	ldr	r0, [pc, #64]	@ (800072c <HAL_ADC_ConvCpltCallback+0xdc>)
 80006ea:	f001 fada 	bl	8001ca2 <HAL_DAC_SetValue>
    for(uint16_t i = 0; i < half_size; i++) {
 80006ee:	8afb      	ldrh	r3, [r7, #22]
 80006f0:	3301      	adds	r3, #1
 80006f2:	82fb      	strh	r3, [r7, #22]
 80006f4:	8afa      	ldrh	r2, [r7, #22]
 80006f6:	89fb      	ldrh	r3, [r7, #14]
 80006f8:	429a      	cmp	r2, r3
 80006fa:	d3b3      	bcc.n	8000664 <HAL_ADC_ConvCpltCallback+0x14>
    }
    ADC_LATEST = adc_buffer[half_size];
 80006fc:	89fb      	ldrh	r3, [r7, #14]
 80006fe:	4a07      	ldr	r2, [pc, #28]	@ (800071c <HAL_ADC_ConvCpltCallback+0xcc>)
 8000700:	f832 2013 	ldrh.w	r2, [r2, r3, lsl #1]
 8000704:	4b0a      	ldr	r3, [pc, #40]	@ (8000730 <HAL_ADC_ConvCpltCallback+0xe0>)
 8000706:	801a      	strh	r2, [r3, #0]
    DAC_LATEST = dac_buffer[half_size];
 8000708:	89fb      	ldrh	r3, [r7, #14]
 800070a:	4a07      	ldr	r2, [pc, #28]	@ (8000728 <HAL_ADC_ConvCpltCallback+0xd8>)
 800070c:	f832 2013 	ldrh.w	r2, [r2, r3, lsl #1]
 8000710:	4b08      	ldr	r3, [pc, #32]	@ (8000734 <HAL_ADC_ConvCpltCallback+0xe4>)
 8000712:	801a      	strh	r2, [r3, #0]
}
 8000714:	bf00      	nop
 8000716:	3718      	adds	r7, #24
 8000718:	46bd      	mov	sp, r7
 800071a:	bd80      	pop	{r7, pc}
 800071c:	200000d0 	.word	0x200000d0
 8000720:	200010d4 	.word	0x200010d4
 8000724:	3fd33333 	.word	0x3fd33333
 8000728:	200008d0 	.word	0x200008d0
 800072c:	200000bc 	.word	0x200000bc
 8000730:	200010d0 	.word	0x200010d0
 8000734:	200010d2 	.word	0x200010d2

08000738 <HAL_ADC_ConvHalfCpltCallback>:

void HAL_ADC_ConvHalfCpltCallback(ADC_HandleTypeDef* hadc) {
 8000738:	b580      	push	{r7, lr}
 800073a:	b086      	sub	sp, #24
 800073c:	af00      	add	r7, sp, #0
 800073e:	6078      	str	r0, [r7, #4]
    uint16_t half_size = AUDIO_BUFFER_SIZE / 2;
 8000740:	f44f 7300 	mov.w	r3, #512	@ 0x200
 8000744:	81fb      	strh	r3, [r7, #14]
    static int32_t prev = 0;
    for(uint16_t i = 0; i < half_size; i++) {
 8000746:	2300      	movs	r3, #0
 8000748:	82fb      	strh	r3, [r7, #22]
 800074a:	e041      	b.n	80007d0 <HAL_ADC_ConvHalfCpltCallback+0x98>
        // 将ADC值转换为以1.65V为中心的交流信号
        int32_t sample = (int32_t)adc_buffer[i] - 2048; // 去直流，得到交流分量
 800074c:	8afb      	ldrh	r3, [r7, #22]
 800074e:	4a28      	ldr	r2, [pc, #160]	@ (80007f0 <HAL_ADC_ConvHalfCpltCallback+0xb8>)
 8000750:	f832 3013 	ldrh.w	r3, [r2, r3, lsl #1]
 8000754:	f5a3 6300 	sub.w	r3, r3, #2048	@ 0x800
 8000758:	613b      	str	r3, [r7, #16]
        
        // 轻微滤波（递归平均）
        sample = (sample + prev) / 2;
 800075a:	4b26      	ldr	r3, [pc, #152]	@ (80007f4 <HAL_ADC_ConvHalfCpltCallback+0xbc>)
 800075c:	681a      	ldr	r2, [r3, #0]
 800075e:	693b      	ldr	r3, [r7, #16]
 8000760:	4413      	add	r3, r2
 8000762:	0fda      	lsrs	r2, r3, #31
 8000764:	4413      	add	r3, r2
 8000766:	105b      	asrs	r3, r3, #1
 8000768:	613b      	str	r3, [r7, #16]
        prev = sample;
 800076a:	4a22      	ldr	r2, [pc, #136]	@ (80007f4 <HAL_ADC_ConvHalfCpltCallback+0xbc>)
 800076c:	693b      	ldr	r3, [r7, #16]
 800076e:	6013      	str	r3, [r2, #0]
        
        // 增益处理
        sample = sample * AUDIO_GAIN;
 8000770:	693b      	ldr	r3, [r7, #16]
 8000772:	ee07 3a90 	vmov	s15, r3
 8000776:	eef8 7ae7 	vcvt.f32.s32	s15, s15
 800077a:	ed9f 7a1f 	vldr	s14, [pc, #124]	@ 80007f8 <HAL_ADC_ConvHalfCpltCallback+0xc0>
 800077e:	ee67 7a87 	vmul.f32	s15, s15, s14
 8000782:	eefd 7ae7 	vcvt.s32.f32	s15, s15
 8000786:	ee17 3a90 	vmov	r3, s15
 800078a:	613b      	str	r3, [r7, #16]
        
        // 将交流信号重新映射到DAC的1.65V中心点
        sample += 2048; // 加回1.65V偏置
 800078c:	693b      	ldr	r3, [r7, #16]
 800078e:	f503 6300 	add.w	r3, r3, #2048	@ 0x800
 8000792:	613b      	str	r3, [r7, #16]
        
        // 限幅保护
        if(sample < 0) sample = 0;
 8000794:	693b      	ldr	r3, [r7, #16]
 8000796:	2b00      	cmp	r3, #0
 8000798:	da01      	bge.n	800079e <HAL_ADC_ConvHalfCpltCallback+0x66>
 800079a:	2300      	movs	r3, #0
 800079c:	613b      	str	r3, [r7, #16]
        if(sample > 4095) sample = 4095;
 800079e:	693b      	ldr	r3, [r7, #16]
 80007a0:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 80007a4:	db02      	blt.n	80007ac <HAL_ADC_ConvHalfCpltCallback+0x74>
 80007a6:	f640 73ff 	movw	r3, #4095	@ 0xfff
 80007aa:	613b      	str	r3, [r7, #16]
        
        dac_buffer[i] = (uint16_t)sample;
 80007ac:	8afb      	ldrh	r3, [r7, #22]
 80007ae:	693a      	ldr	r2, [r7, #16]
 80007b0:	b291      	uxth	r1, r2
 80007b2:	4a12      	ldr	r2, [pc, #72]	@ (80007fc <HAL_ADC_ConvHalfCpltCallback+0xc4>)
 80007b4:	f822 1013 	strh.w	r1, [r2, r3, lsl #1]
        HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_buffer[i]);
 80007b8:	8afb      	ldrh	r3, [r7, #22]
 80007ba:	4a10      	ldr	r2, [pc, #64]	@ (80007fc <HAL_ADC_ConvHalfCpltCallback+0xc4>)
 80007bc:	f832 3013 	ldrh.w	r3, [r2, r3, lsl #1]
 80007c0:	2200      	movs	r2, #0
 80007c2:	2100      	movs	r1, #0
 80007c4:	480e      	ldr	r0, [pc, #56]	@ (8000800 <HAL_ADC_ConvHalfCpltCallback+0xc8>)
 80007c6:	f001 fa6c 	bl	8001ca2 <HAL_DAC_SetValue>
    for(uint16_t i = 0; i < half_size; i++) {
 80007ca:	8afb      	ldrh	r3, [r7, #22]
 80007cc:	3301      	adds	r3, #1
 80007ce:	82fb      	strh	r3, [r7, #22]
 80007d0:	8afa      	ldrh	r2, [r7, #22]
 80007d2:	89fb      	ldrh	r3, [r7, #14]
 80007d4:	429a      	cmp	r2, r3
 80007d6:	d3b9      	bcc.n	800074c <HAL_ADC_ConvHalfCpltCallback+0x14>
    }
    ADC_LATEST = adc_buffer[0];
 80007d8:	4b05      	ldr	r3, [pc, #20]	@ (80007f0 <HAL_ADC_ConvHalfCpltCallback+0xb8>)
 80007da:	881a      	ldrh	r2, [r3, #0]
 80007dc:	4b09      	ldr	r3, [pc, #36]	@ (8000804 <HAL_ADC_ConvHalfCpltCallback+0xcc>)
 80007de:	801a      	strh	r2, [r3, #0]
    DAC_LATEST = dac_buffer[0];
 80007e0:	4b06      	ldr	r3, [pc, #24]	@ (80007fc <HAL_ADC_ConvHalfCpltCallback+0xc4>)
 80007e2:	881a      	ldrh	r2, [r3, #0]
 80007e4:	4b08      	ldr	r3, [pc, #32]	@ (8000808 <HAL_ADC_ConvHalfCpltCallback+0xd0>)
 80007e6:	801a      	strh	r2, [r3, #0]
}
 80007e8:	bf00      	nop
 80007ea:	3718      	adds	r7, #24
 80007ec:	46bd      	mov	sp, r7
 80007ee:	bd80      	pop	{r7, pc}
 80007f0:	200000d0 	.word	0x200000d0
 80007f4:	200010d8 	.word	0x200010d8
 80007f8:	3fd33333 	.word	0x3fd33333
 80007fc:	200008d0 	.word	0x200008d0
 8000800:	200000bc 	.word	0x200000bc
 8000804:	200010d0 	.word	0x200010d0
 8000808:	200010d2 	.word	0x200010d2

0800080c <Error_Handler>:

void Error_Handler(void)
{
 800080c:	b480      	push	{r7}
 800080e:	af00      	add	r7, sp, #0
  \details Disables IRQ interrupts by setting the I-bit in the CPSR.
           Can only be executed in Privileged modes.
 */
__STATIC_FORCEINLINE void __disable_irq(void)
{
  __ASM volatile ("cpsid i" : : : "memory");
 8000810:	b672      	cpsid	i
}
 8000812:	bf00      	nop
  __disable_irq();
  while (1)
 8000814:	bf00      	nop
 8000816:	e7fd      	b.n	8000814 <Error_Handler+0x8>

08000818 <HAL_MspInit>:
/* USER CODE END 0 */
/**
  * Initializes the Global MSP.
  */
void HAL_MspInit(void)
{
 8000818:	b480      	push	{r7}
 800081a:	b083      	sub	sp, #12
 800081c:	af00      	add	r7, sp, #0

  /* USER CODE BEGIN MspInit 0 */

  /* USER CODE END MspInit 0 */

  __HAL_RCC_SYSCFG_CLK_ENABLE();
 800081e:	4b0f      	ldr	r3, [pc, #60]	@ (800085c <HAL_MspInit+0x44>)
 8000820:	699b      	ldr	r3, [r3, #24]
 8000822:	4a0e      	ldr	r2, [pc, #56]	@ (800085c <HAL_MspInit+0x44>)
 8000824:	f043 0301 	orr.w	r3, r3, #1
 8000828:	6193      	str	r3, [r2, #24]
 800082a:	4b0c      	ldr	r3, [pc, #48]	@ (800085c <HAL_MspInit+0x44>)
 800082c:	699b      	ldr	r3, [r3, #24]
 800082e:	f003 0301 	and.w	r3, r3, #1
 8000832:	607b      	str	r3, [r7, #4]
 8000834:	687b      	ldr	r3, [r7, #4]
  __HAL_RCC_PWR_CLK_ENABLE();
 8000836:	4b09      	ldr	r3, [pc, #36]	@ (800085c <HAL_MspInit+0x44>)
 8000838:	69db      	ldr	r3, [r3, #28]
 800083a:	4a08      	ldr	r2, [pc, #32]	@ (800085c <HAL_MspInit+0x44>)
 800083c:	f043 5380 	orr.w	r3, r3, #268435456	@ 0x10000000
 8000840:	61d3      	str	r3, [r2, #28]
 8000842:	4b06      	ldr	r3, [pc, #24]	@ (800085c <HAL_MspInit+0x44>)
 8000844:	69db      	ldr	r3, [r3, #28]
 8000846:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 800084a:	603b      	str	r3, [r7, #0]
 800084c:	683b      	ldr	r3, [r7, #0]
  /* System interrupt init*/

  /* USER CODE BEGIN MspInit 1 */

  /* USER CODE END MspInit 1 */
}
 800084e:	bf00      	nop
 8000850:	370c      	adds	r7, #12
 8000852:	46bd      	mov	sp, r7
 8000854:	f85d 7b04 	ldr.w	r7, [sp], #4
 8000858:	4770      	bx	lr
 800085a:	bf00      	nop
 800085c:	******** 	.word	0x********

08000860 <NMI_Handler>:
/******************************************************************************/
/**
  * @brief This function handles Non maskable interrupt.
  */
void NMI_Handler(void)
{
 8000860:	b480      	push	{r7}
 8000862:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */

  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
   while (1)
 8000864:	bf00      	nop
 8000866:	e7fd      	b.n	8000864 <NMI_Handler+0x4>

08000868 <HardFault_Handler>:

/**
  * @brief This function handles Hard fault interrupt.
  */
void HardFault_Handler(void)
{
 8000868:	b480      	push	{r7}
 800086a:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN HardFault_IRQn 0 */

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
 800086c:	bf00      	nop
 800086e:	e7fd      	b.n	800086c <HardFault_Handler+0x4>

08000870 <MemManage_Handler>:

/**
  * @brief This function handles Memory management fault.
  */
void MemManage_Handler(void)
{
 8000870:	b480      	push	{r7}
 8000872:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
 8000874:	bf00      	nop
 8000876:	e7fd      	b.n	8000874 <MemManage_Handler+0x4>

08000878 <BusFault_Handler>:

/**
  * @brief This function handles Pre-fetch fault, memory access fault.
  */
void BusFault_Handler(void)
{
 8000878:	b480      	push	{r7}
 800087a:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
 800087c:	bf00      	nop
 800087e:	e7fd      	b.n	800087c <BusFault_Handler+0x4>

08000880 <UsageFault_Handler>:

/**
  * @brief This function handles Undefined instruction or illegal state.
  */
void UsageFault_Handler(void)
{
 8000880:	b480      	push	{r7}
 8000882:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
 8000884:	bf00      	nop
 8000886:	e7fd      	b.n	8000884 <UsageFault_Handler+0x4>

08000888 <SVC_Handler>:

/**
  * @brief This function handles System service call via SWI instruction.
  */
void SVC_Handler(void)
{
 8000888:	b480      	push	{r7}
 800088a:	af00      	add	r7, sp, #0

  /* USER CODE END SVCall_IRQn 0 */
  /* USER CODE BEGIN SVCall_IRQn 1 */

  /* USER CODE END SVCall_IRQn 1 */
}
 800088c:	bf00      	nop
 800088e:	46bd      	mov	sp, r7
 8000890:	f85d 7b04 	ldr.w	r7, [sp], #4
 8000894:	4770      	bx	lr

08000896 <DebugMon_Handler>:

/**
  * @brief This function handles Debug monitor.
  */
void DebugMon_Handler(void)
{
 8000896:	b480      	push	{r7}
 8000898:	af00      	add	r7, sp, #0

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}
 800089a:	bf00      	nop
 800089c:	46bd      	mov	sp, r7
 800089e:	f85d 7b04 	ldr.w	r7, [sp], #4
 80008a2:	4770      	bx	lr

080008a4 <PendSV_Handler>:

/**
  * @brief This function handles Pendable request for system service.
  */
void PendSV_Handler(void)
{
 80008a4:	b480      	push	{r7}
 80008a6:	af00      	add	r7, sp, #0

  /* USER CODE END PendSV_IRQn 0 */
  /* USER CODE BEGIN PendSV_IRQn 1 */

  /* USER CODE END PendSV_IRQn 1 */
}
 80008a8:	bf00      	nop
 80008aa:	46bd      	mov	sp, r7
 80008ac:	f85d 7b04 	ldr.w	r7, [sp], #4
 80008b0:	4770      	bx	lr

080008b2 <SysTick_Handler>:

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
 80008b2:	b580      	push	{r7, lr}
 80008b4:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN SysTick_IRQn 0 */

  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();
 80008b6:	f000 f88f 	bl	80009d8 <HAL_IncTick>
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}
 80008ba:	bf00      	nop
 80008bc:	bd80      	pop	{r7, pc}
	...

080008c0 <DMA1_Channel1_IRQHandler>:

/**
  * @brief This function handles DMA1 channel1 global interrupt.
  */
void DMA1_Channel1_IRQHandler(void)
{
 80008c0:	b580      	push	{r7, lr}
 80008c2:	af00      	add	r7, sp, #0
  /* USER CODE BEGIN DMA1_Channel1_IRQn 0 */

  /* USER CODE END DMA1_Channel1_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_adc1);
 80008c4:	4802      	ldr	r0, [pc, #8]	@ (80008d0 <DMA1_Channel1_IRQHandler+0x10>)
 80008c6:	f001 fb4f 	bl	8001f68 <HAL_DMA_IRQHandler>
  /* USER CODE BEGIN DMA1_Channel1_IRQn 1 */

  /* USER CODE END DMA1_Channel1_IRQn 1 */
}
 80008ca:	bf00      	nop
 80008cc:	bd80      	pop	{r7, pc}
 80008ce:	bf00      	nop
 80008d0:	20000078 	.word	0x20000078

080008d4 <SystemInit>:
  * @brief  Setup the microcontroller system
  * @param  None
  * @retval None
  */
void SystemInit(void)
{
 80008d4:	b480      	push	{r7}
 80008d6:	af00      	add	r7, sp, #0
/* FPU settings --------------------------------------------------------------*/
#if (__FPU_PRESENT == 1) && (__FPU_USED == 1)
  SCB->CPACR |= ((3UL << 10*2)|(3UL << 11*2));  /* set CP10 and CP11 Full Access */
 80008d8:	4b06      	ldr	r3, [pc, #24]	@ (80008f4 <SystemInit+0x20>)
 80008da:	f8d3 3088 	ldr.w	r3, [r3, #136]	@ 0x88
 80008de:	4a05      	ldr	r2, [pc, #20]	@ (80008f4 <SystemInit+0x20>)
 80008e0:	f443 0370 	orr.w	r3, r3, #15728640	@ 0xf00000
 80008e4:	f8c2 3088 	str.w	r3, [r2, #136]	@ 0x88

  /* Configure the Vector Table location -------------------------------------*/
#if defined(USER_VECT_TAB_ADDRESS)
  SCB->VTOR = VECT_TAB_BASE_ADDRESS | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal SRAM */
#endif /* USER_VECT_TAB_ADDRESS */
}
 80008e8:	bf00      	nop
 80008ea:	46bd      	mov	sp, r7
 80008ec:	f85d 7b04 	ldr.w	r7, [sp], #4
 80008f0:	4770      	bx	lr
 80008f2:	bf00      	nop
 80008f4:	e000ed00 	.word	0xe000ed00

080008f8 <Reset_Handler>:

    .section	.text.Reset_Handler
	.weak	Reset_Handler
	.type	Reset_Handler, %function
Reset_Handler:
  ldr   sp, =_estack    /* Atollic update: set stack pointer */
 80008f8:	f8df d034 	ldr.w	sp, [pc, #52]	@ 8000930 <LoopForever+0x2>
  
/* Call the clock system initialization function.*/
    bl  SystemInit
 80008fc:	f7ff ffea 	bl	80008d4 <SystemInit>

/* Copy the data segment initializers from flash to SRAM */
  ldr r0, =_sdata
 8000900:	480c      	ldr	r0, [pc, #48]	@ (8000934 <LoopForever+0x6>)
  ldr r1, =_edata
 8000902:	490d      	ldr	r1, [pc, #52]	@ (8000938 <LoopForever+0xa>)
  ldr r2, =_sidata
 8000904:	4a0d      	ldr	r2, [pc, #52]	@ (800093c <LoopForever+0xe>)
  movs r3, #0
 8000906:	2300      	movs	r3, #0
  b LoopCopyDataInit
 8000908:	e002      	b.n	8000910 <LoopCopyDataInit>

0800090a <CopyDataInit>:

CopyDataInit:
  ldr r4, [r2, r3]
 800090a:	58d4      	ldr	r4, [r2, r3]
  str r4, [r0, r3]
 800090c:	50c4      	str	r4, [r0, r3]
  adds r3, r3, #4
 800090e:	3304      	adds	r3, #4

08000910 <LoopCopyDataInit>:

LoopCopyDataInit:
  adds r4, r0, r3
 8000910:	18c4      	adds	r4, r0, r3
  cmp r4, r1
 8000912:	428c      	cmp	r4, r1
  bcc CopyDataInit
 8000914:	d3f9      	bcc.n	800090a <CopyDataInit>
  
/* Zero fill the bss segment. */
  ldr r2, =_sbss
 8000916:	4a0a      	ldr	r2, [pc, #40]	@ (8000940 <LoopForever+0x12>)
  ldr r4, =_ebss
 8000918:	4c0a      	ldr	r4, [pc, #40]	@ (8000944 <LoopForever+0x16>)
  movs r3, #0
 800091a:	2300      	movs	r3, #0
  b LoopFillZerobss
 800091c:	e001      	b.n	8000922 <LoopFillZerobss>

0800091e <FillZerobss>:

FillZerobss:
  str  r3, [r2]
 800091e:	6013      	str	r3, [r2, #0]
  adds r2, r2, #4
 8000920:	3204      	adds	r2, #4

08000922 <LoopFillZerobss>:

LoopFillZerobss:
  cmp r2, r4
 8000922:	42a2      	cmp	r2, r4
  bcc FillZerobss
 8000924:	d3fb      	bcc.n	800091e <FillZerobss>

/* Call static constructors */
    bl __libc_init_array
 8000926:	f003 f92d 	bl	8003b84 <__libc_init_array>
/* Call the application's entry point.*/
	bl	main
 800092a:	f7ff fde5 	bl	80004f8 <main>

0800092e <LoopForever>:

LoopForever:
    b LoopForever
 800092e:	e7fe      	b.n	800092e <LoopForever>
  ldr   sp, =_estack    /* Atollic update: set stack pointer */
 8000930:	2000a000 	.word	0x2000a000
  ldr r0, =_sdata
 8000934:	20000000 	.word	0x20000000
  ldr r1, =_edata
 8000938:	2000000c 	.word	0x2000000c
  ldr r2, =_sidata
 800093c:	08003c1c 	.word	0x08003c1c
  ldr r2, =_sbss
 8000940:	2000000c 	.word	0x2000000c
  ldr r4, =_ebss
 8000944:	200010e0 	.word	0x200010e0

08000948 <ADC1_2_IRQHandler>:
 * @retval : None
*/
    .section	.text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
	b	Infinite_Loop
 8000948:	e7fe      	b.n	8000948 <ADC1_2_IRQHandler>
	...

0800094c <HAL_Init>:
  *         In the default implementation,Systick is used as source of time base.
  *       The tick variable is incremented each 1ms in its ISR.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_Init(void)
{
 800094c:	b580      	push	{r7, lr}
 800094e:	af00      	add	r7, sp, #0
  /* Configure Flash prefetch */
#if (PREFETCH_ENABLE != 0U)
  __HAL_FLASH_PREFETCH_BUFFER_ENABLE();
 8000950:	4b08      	ldr	r3, [pc, #32]	@ (8000974 <HAL_Init+0x28>)
 8000952:	681b      	ldr	r3, [r3, #0]
 8000954:	4a07      	ldr	r2, [pc, #28]	@ (8000974 <HAL_Init+0x28>)
 8000956:	f043 0310 	orr.w	r3, r3, #16
 800095a:	6013      	str	r3, [r2, #0]
#endif /* PREFETCH_ENABLE */

  /* Set Interrupt Group Priority */
  HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
 800095c:	2003      	movs	r0, #3
 800095e:	f001 f93d 	bl	8001bdc <HAL_NVIC_SetPriorityGrouping>

  /* Enable systick and configure 1ms tick (default clock after Reset is HSI) */
  HAL_InitTick(TICK_INT_PRIORITY);
 8000962:	200f      	movs	r0, #15
 8000964:	f000 f808 	bl	8000978 <HAL_InitTick>

  /* Init the low level hardware */
  HAL_MspInit();
 8000968:	f7ff ff56 	bl	8000818 <HAL_MspInit>

  /* Return function status */
  return HAL_OK;
 800096c:	2300      	movs	r3, #0
}
 800096e:	4618      	mov	r0, r3
 8000970:	bd80      	pop	{r7, pc}
 8000972:	bf00      	nop
 8000974:	******** 	.word	0x********

08000978 <HAL_InitTick>:
  *         implementation  in user file.
  * @param TickPriority Tick interrupt priority.
  * @retval HAL status
  */
__weak HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
{
 8000978:	b580      	push	{r7, lr}
 800097a:	b082      	sub	sp, #8
 800097c:	af00      	add	r7, sp, #0
 800097e:	6078      	str	r0, [r7, #4]
  /* Configure the SysTick to have interrupt in 1ms time basis*/
  if (HAL_SYSTICK_Config(SystemCoreClock / (1000U / uwTickFreq)) > 0U)
 8000980:	4b12      	ldr	r3, [pc, #72]	@ (80009cc <HAL_InitTick+0x54>)
 8000982:	681a      	ldr	r2, [r3, #0]
 8000984:	4b12      	ldr	r3, [pc, #72]	@ (80009d0 <HAL_InitTick+0x58>)
 8000986:	781b      	ldrb	r3, [r3, #0]
 8000988:	4619      	mov	r1, r3
 800098a:	f44f 737a 	mov.w	r3, #1000	@ 0x3e8
 800098e:	fbb3 f3f1 	udiv	r3, r3, r1
 8000992:	fbb2 f3f3 	udiv	r3, r2, r3
 8000996:	4618      	mov	r0, r3
 8000998:	f001 f955 	bl	8001c46 <HAL_SYSTICK_Config>
 800099c:	4603      	mov	r3, r0
 800099e:	2b00      	cmp	r3, #0
 80009a0:	d001      	beq.n	80009a6 <HAL_InitTick+0x2e>
  {
    return HAL_ERROR;
 80009a2:	2301      	movs	r3, #1
 80009a4:	e00e      	b.n	80009c4 <HAL_InitTick+0x4c>
  }

  /* Configure the SysTick IRQ priority */
  if (TickPriority < (1UL << __NVIC_PRIO_BITS))
 80009a6:	687b      	ldr	r3, [r7, #4]
 80009a8:	2b0f      	cmp	r3, #15
 80009aa:	d80a      	bhi.n	80009c2 <HAL_InitTick+0x4a>
  {
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
 80009ac:	2200      	movs	r2, #0
 80009ae:	6879      	ldr	r1, [r7, #4]
 80009b0:	f04f 30ff 	mov.w	r0, #4294967295
 80009b4:	f001 f91d 	bl	8001bf2 <HAL_NVIC_SetPriority>
    uwTickPrio = TickPriority;
 80009b8:	4a06      	ldr	r2, [pc, #24]	@ (80009d4 <HAL_InitTick+0x5c>)
 80009ba:	687b      	ldr	r3, [r7, #4]
 80009bc:	6013      	str	r3, [r2, #0]
  else
  {
    return HAL_ERROR;
  }
   /* Return function status */
  return HAL_OK;
 80009be:	2300      	movs	r3, #0
 80009c0:	e000      	b.n	80009c4 <HAL_InitTick+0x4c>
    return HAL_ERROR;
 80009c2:	2301      	movs	r3, #1
}
 80009c4:	4618      	mov	r0, r3
 80009c6:	3708      	adds	r7, #8
 80009c8:	46bd      	mov	sp, r7
 80009ca:	bd80      	pop	{r7, pc}
 80009cc:	20000000 	.word	0x20000000
 80009d0:	20000008 	.word	0x20000008
 80009d4:	20000004 	.word	0x20000004

080009d8 <HAL_IncTick>:
  * @note This function is declared as __weak to be overwritten in case of other 
  *         implementations  in user file.
  * @retval None
  */
__weak void HAL_IncTick(void)
{
 80009d8:	b480      	push	{r7}
 80009da:	af00      	add	r7, sp, #0
  uwTick += uwTickFreq;
 80009dc:	4b06      	ldr	r3, [pc, #24]	@ (80009f8 <HAL_IncTick+0x20>)
 80009de:	781b      	ldrb	r3, [r3, #0]
 80009e0:	461a      	mov	r2, r3
 80009e2:	4b06      	ldr	r3, [pc, #24]	@ (80009fc <HAL_IncTick+0x24>)
 80009e4:	681b      	ldr	r3, [r3, #0]
 80009e6:	4413      	add	r3, r2
 80009e8:	4a04      	ldr	r2, [pc, #16]	@ (80009fc <HAL_IncTick+0x24>)
 80009ea:	6013      	str	r3, [r2, #0]
}
 80009ec:	bf00      	nop
 80009ee:	46bd      	mov	sp, r7
 80009f0:	f85d 7b04 	ldr.w	r7, [sp], #4
 80009f4:	4770      	bx	lr
 80009f6:	bf00      	nop
 80009f8:	20000008 	.word	0x20000008
 80009fc:	200010dc 	.word	0x200010dc

08000a00 <HAL_GetTick>:
  * @note   The function is declared as __Weak  to be overwritten  in case of other 
  *         implementations  in user file.
  * @retval tick value
  */
__weak uint32_t HAL_GetTick(void)
{
 8000a00:	b480      	push	{r7}
 8000a02:	af00      	add	r7, sp, #0
  return uwTick;  
 8000a04:	4b03      	ldr	r3, [pc, #12]	@ (8000a14 <HAL_GetTick+0x14>)
 8000a06:	681b      	ldr	r3, [r3, #0]
}
 8000a08:	4618      	mov	r0, r3
 8000a0a:	46bd      	mov	sp, r7
 8000a0c:	f85d 7b04 	ldr.w	r7, [sp], #4
 8000a10:	4770      	bx	lr
 8000a12:	bf00      	nop
 8000a14:	200010dc 	.word	0x200010dc

08000a18 <HAL_ADC_ErrorCallback>:
  *        (ADC conversion with interruption or transfer by DMA)
  * @param  hadc ADC handle
  * @retval None
  */
__weak void HAL_ADC_ErrorCallback(ADC_HandleTypeDef *hadc)
{
 8000a18:	b480      	push	{r7}
 8000a1a:	b083      	sub	sp, #12
 8000a1c:	af00      	add	r7, sp, #0
 8000a1e:	6078      	str	r0, [r7, #4]
  UNUSED(hadc);

  /* NOTE : This function should not be modified. When the callback is needed,
            function HAL_ADC_ErrorCallback must be implemented in the user file.
  */
}
 8000a20:	bf00      	nop
 8000a22:	370c      	adds	r7, #12
 8000a24:	46bd      	mov	sp, r7
 8000a26:	f85d 7b04 	ldr.w	r7, [sp], #4
 8000a2a:	4770      	bx	lr

08000a2c <HAL_ADC_Init>:
  *         without  disabling the other ADCs sharing the same common group.
  * @param  hadc ADC handle
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_ADC_Init(ADC_HandleTypeDef* hadc)
{
 8000a2c:	b580      	push	{r7, lr}
 8000a2e:	b09a      	sub	sp, #104	@ 0x68
 8000a30:	af00      	add	r7, sp, #0
 8000a32:	6078      	str	r0, [r7, #4]
  HAL_StatusTypeDef tmp_hal_status = HAL_OK;
 8000a34:	2300      	movs	r3, #0
 8000a36:	f887 3067 	strb.w	r3, [r7, #103]	@ 0x67
  ADC_Common_TypeDef *tmpADC_Common;
  ADC_HandleTypeDef tmphadcSharingSameCommonRegister;
  uint32_t tmpCFGR = 0U;
 8000a3a:	2300      	movs	r3, #0
 8000a3c:	663b      	str	r3, [r7, #96]	@ 0x60
  __IO uint32_t wait_loop_index = 0U;
 8000a3e:	2300      	movs	r3, #0
 8000a40:	60bb      	str	r3, [r7, #8]
  
  /* Check ADC handle */
  if(hadc == NULL)
 8000a42:	687b      	ldr	r3, [r7, #4]
 8000a44:	2b00      	cmp	r3, #0
 8000a46:	d101      	bne.n	8000a4c <HAL_ADC_Init+0x20>
  {
    return HAL_ERROR;
 8000a48:	2301      	movs	r3, #1
 8000a4a:	e1c9      	b.n	8000de0 <HAL_ADC_Init+0x3b4>
  assert_param(IS_FUNCTIONAL_STATE(hadc->Init.DMAContinuousRequests));
  assert_param(IS_ADC_EOC_SELECTION(hadc->Init.EOCSelection));
  assert_param(IS_ADC_OVERRUN(hadc->Init.Overrun));
  assert_param(IS_FUNCTIONAL_STATE(hadc->Init.LowPowerAutoWait));
  
  if(hadc->Init.ScanConvMode != ADC_SCAN_DISABLE)
 8000a4c:	687b      	ldr	r3, [r7, #4]
 8000a4e:	691b      	ldr	r3, [r3, #16]
 8000a50:	2b00      	cmp	r3, #0
      assert_param(IS_ADC_REGULAR_DISCONT_NUMBER(hadc->Init.NbrOfDiscConversion));
    }
  }
    
  /* Configuration of ADC core parameters and ADC MSP related parameters */
  if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL))
 8000a52:	687b      	ldr	r3, [r7, #4]
 8000a54:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000a56:	f003 0310 	and.w	r3, r3, #16
 8000a5a:	2b00      	cmp	r3, #0
 8000a5c:	d176      	bne.n	8000b4c <HAL_ADC_Init+0x120>
    /* procedure.                                                             */
    
    /* Actions performed only if ADC is coming from state reset:              */
    /* - Initialization of ADC MSP                                            */
    /* - ADC voltage regulator enable                                         */
    if (hadc->State == HAL_ADC_STATE_RESET)
 8000a5e:	687b      	ldr	r3, [r7, #4]
 8000a60:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000a62:	2b00      	cmp	r3, #0
 8000a64:	d152      	bne.n	8000b0c <HAL_ADC_Init+0xe0>
    {
      /* Initialize ADC error code */
      ADC_CLEAR_ERRORCODE(hadc);
 8000a66:	687b      	ldr	r3, [r7, #4]
 8000a68:	2200      	movs	r2, #0
 8000a6a:	645a      	str	r2, [r3, #68]	@ 0x44
      
      /* Initialize HAL ADC API internal variables */
      hadc->InjectionConfig.ChannelCount = 0U;
 8000a6c:	687b      	ldr	r3, [r7, #4]
 8000a6e:	2200      	movs	r2, #0
 8000a70:	64da      	str	r2, [r3, #76]	@ 0x4c
      hadc->InjectionConfig.ContextQueue = 0U;
 8000a72:	687b      	ldr	r3, [r7, #4]
 8000a74:	2200      	movs	r2, #0
 8000a76:	649a      	str	r2, [r3, #72]	@ 0x48
      
      /* Allocate lock resource and initialize it */
      hadc->Lock = HAL_UNLOCKED;
 8000a78:	687b      	ldr	r3, [r7, #4]
 8000a7a:	2200      	movs	r2, #0
 8000a7c:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
    
    /* Init the low level hardware */
    hadc->MspInitCallback(hadc);
#else
    /* Init the low level hardware */
    HAL_ADC_MspInit(hadc);
 8000a80:	6878      	ldr	r0, [r7, #4]
 8000a82:	f7ff fc11 	bl	80002a8 <HAL_ADC_MspInit>
#endif /* USE_HAL_ADC_REGISTER_CALLBACKS */
      
      /* Enable voltage regulator (if disabled at this step) */
      if (HAL_IS_BIT_CLR(hadc->Instance->CR, ADC_CR_ADVREGEN_0))
 8000a86:	687b      	ldr	r3, [r7, #4]
 8000a88:	681b      	ldr	r3, [r3, #0]
 8000a8a:	689b      	ldr	r3, [r3, #8]
 8000a8c:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8000a90:	2b00      	cmp	r3, #0
 8000a92:	d13b      	bne.n	8000b0c <HAL_ADC_Init+0xe0>
        /*       enabling the ADC. This temporization must be implemented by  */ 
        /*       software and is equal to 10 us in the worst case             */
        /*       process/temperature/power supply.                            */
        
        /* Disable the ADC (if not already disabled) */
        tmp_hal_status = ADC_Disable(hadc);
 8000a94:	6878      	ldr	r0, [r7, #4]
 8000a96:	f000 ff6b 	bl	8001970 <ADC_Disable>
 8000a9a:	4603      	mov	r3, r0
 8000a9c:	f887 3067 	strb.w	r3, [r7, #103]	@ 0x67
        
        /* Check if ADC is effectively disabled */
        /* Configuration of ADC parameters if previous preliminary actions    */ 
        /* are correctly completed.                                           */
        if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL) &&
 8000aa0:	687b      	ldr	r3, [r7, #4]
 8000aa2:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000aa4:	f003 0310 	and.w	r3, r3, #16
 8000aa8:	2b00      	cmp	r3, #0
 8000aaa:	d12f      	bne.n	8000b0c <HAL_ADC_Init+0xe0>
 8000aac:	f897 3067 	ldrb.w	r3, [r7, #103]	@ 0x67
 8000ab0:	2b00      	cmp	r3, #0
 8000ab2:	d12b      	bne.n	8000b0c <HAL_ADC_Init+0xe0>
            (tmp_hal_status == HAL_OK)                                  )
        {
          /* Set ADC state */
          ADC_STATE_CLR_SET(hadc->State,
 8000ab4:	687b      	ldr	r3, [r7, #4]
 8000ab6:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000ab8:	f423 5388 	bic.w	r3, r3, #4352	@ 0x1100
 8000abc:	f023 0302 	bic.w	r3, r3, #2
 8000ac0:	f043 0202 	orr.w	r2, r3, #2
 8000ac4:	687b      	ldr	r3, [r7, #4]
 8000ac6:	641a      	str	r2, [r3, #64]	@ 0x40
                            HAL_ADC_STATE_REG_BUSY | HAL_ADC_STATE_INJ_BUSY,
                            HAL_ADC_STATE_BUSY_INTERNAL);
          
          /* Set the intermediate state before moving the ADC voltage         */
          /* regulator to state enable.                                       */
          CLEAR_BIT(hadc->Instance->CR, (ADC_CR_ADVREGEN_1 | ADC_CR_ADVREGEN_0));
 8000ac8:	687b      	ldr	r3, [r7, #4]
 8000aca:	681b      	ldr	r3, [r3, #0]
 8000acc:	689a      	ldr	r2, [r3, #8]
 8000ace:	687b      	ldr	r3, [r7, #4]
 8000ad0:	681b      	ldr	r3, [r3, #0]
 8000ad2:	f022 5240 	bic.w	r2, r2, #805306368	@ 0x30000000
 8000ad6:	609a      	str	r2, [r3, #8]
          /* Set ADVREGEN bits to 0x01U */
          SET_BIT(hadc->Instance->CR, ADC_CR_ADVREGEN_0);
 8000ad8:	687b      	ldr	r3, [r7, #4]
 8000ada:	681b      	ldr	r3, [r3, #0]
 8000adc:	689a      	ldr	r2, [r3, #8]
 8000ade:	687b      	ldr	r3, [r7, #4]
 8000ae0:	681b      	ldr	r3, [r3, #0]
 8000ae2:	f042 5280 	orr.w	r2, r2, #268435456	@ 0x10000000
 8000ae6:	609a      	str	r2, [r3, #8]
          
          /* Delay for ADC stabilization time.                                */
          /* Compute number of CPU cycles to wait for */
          wait_loop_index = (ADC_STAB_DELAY_US * (SystemCoreClock / 1000000U));
 8000ae8:	4b86      	ldr	r3, [pc, #536]	@ (8000d04 <HAL_ADC_Init+0x2d8>)
 8000aea:	681b      	ldr	r3, [r3, #0]
 8000aec:	4a86      	ldr	r2, [pc, #536]	@ (8000d08 <HAL_ADC_Init+0x2dc>)
 8000aee:	fba2 2303 	umull	r2, r3, r2, r3
 8000af2:	0c9a      	lsrs	r2, r3, #18
 8000af4:	4613      	mov	r3, r2
 8000af6:	009b      	lsls	r3, r3, #2
 8000af8:	4413      	add	r3, r2
 8000afa:	005b      	lsls	r3, r3, #1
 8000afc:	60bb      	str	r3, [r7, #8]
          while(wait_loop_index != 0U)
 8000afe:	e002      	b.n	8000b06 <HAL_ADC_Init+0xda>
          {
            wait_loop_index--;
 8000b00:	68bb      	ldr	r3, [r7, #8]
 8000b02:	3b01      	subs	r3, #1
 8000b04:	60bb      	str	r3, [r7, #8]
          while(wait_loop_index != 0U)
 8000b06:	68bb      	ldr	r3, [r7, #8]
 8000b08:	2b00      	cmp	r3, #0
 8000b0a:	d1f9      	bne.n	8000b00 <HAL_ADC_Init+0xd4>
    }
    
    /* Verification that ADC voltage regulator is correctly enabled, whether  */
    /* or not ADC is coming from state reset (if any potential problem of     */
    /* clocking, voltage regulator would not be enabled).                     */
    if (HAL_IS_BIT_CLR(hadc->Instance->CR, ADC_CR_ADVREGEN_0) ||
 8000b0c:	687b      	ldr	r3, [r7, #4]
 8000b0e:	681b      	ldr	r3, [r3, #0]
 8000b10:	689b      	ldr	r3, [r3, #8]
 8000b12:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8000b16:	2b00      	cmp	r3, #0
 8000b18:	d007      	beq.n	8000b2a <HAL_ADC_Init+0xfe>
        HAL_IS_BIT_SET(hadc->Instance->CR, ADC_CR_ADVREGEN_1)   )
 8000b1a:	687b      	ldr	r3, [r7, #4]
 8000b1c:	681b      	ldr	r3, [r3, #0]
 8000b1e:	689b      	ldr	r3, [r3, #8]
 8000b20:	f003 5300 	and.w	r3, r3, #536870912	@ 0x20000000
    if (HAL_IS_BIT_CLR(hadc->Instance->CR, ADC_CR_ADVREGEN_0) ||
 8000b24:	f1b3 5f00 	cmp.w	r3, #536870912	@ 0x20000000
 8000b28:	d110      	bne.n	8000b4c <HAL_ADC_Init+0x120>
    {
      /* Update ADC state machine to error */
      ADC_STATE_CLR_SET(hadc->State,
 8000b2a:	687b      	ldr	r3, [r7, #4]
 8000b2c:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000b2e:	f023 0312 	bic.w	r3, r3, #18
 8000b32:	f043 0210 	orr.w	r2, r3, #16
 8000b36:	687b      	ldr	r3, [r7, #4]
 8000b38:	641a      	str	r2, [r3, #64]	@ 0x40
                        HAL_ADC_STATE_BUSY_INTERNAL,
                        HAL_ADC_STATE_ERROR_INTERNAL);
      
      /* Set ADC error code to ADC IP internal error */
      SET_BIT(hadc->ErrorCode, HAL_ADC_ERROR_INTERNAL);
 8000b3a:	687b      	ldr	r3, [r7, #4]
 8000b3c:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 8000b3e:	f043 0201 	orr.w	r2, r3, #1
 8000b42:	687b      	ldr	r3, [r7, #4]
 8000b44:	645a      	str	r2, [r3, #68]	@ 0x44
      
      tmp_hal_status = HAL_ERROR;
 8000b46:	2301      	movs	r3, #1
 8000b48:	f887 3067 	strb.w	r3, [r7, #103]	@ 0x67
  
  /* Configuration of ADC parameters if previous preliminary actions are      */ 
  /* correctly completed and if there is no conversion on going on regular    */
  /* group (ADC may already be enabled at this point if HAL_ADC_Init() is     */
  /* called to update a parameter on the fly).                                */
  if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL) &&
 8000b4c:	687b      	ldr	r3, [r7, #4]
 8000b4e:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000b50:	f003 0310 	and.w	r3, r3, #16
 8000b54:	2b00      	cmp	r3, #0
 8000b56:	f040 8136 	bne.w	8000dc6 <HAL_ADC_Init+0x39a>
 8000b5a:	f897 3067 	ldrb.w	r3, [r7, #103]	@ 0x67
 8000b5e:	2b00      	cmp	r3, #0
 8000b60:	f040 8131 	bne.w	8000dc6 <HAL_ADC_Init+0x39a>
      (tmp_hal_status == HAL_OK)                                &&
      (ADC_IS_CONVERSION_ONGOING_REGULAR(hadc) == RESET)          )
 8000b64:	687b      	ldr	r3, [r7, #4]
 8000b66:	681b      	ldr	r3, [r3, #0]
 8000b68:	689b      	ldr	r3, [r3, #8]
 8000b6a:	f003 0304 	and.w	r3, r3, #4
      (tmp_hal_status == HAL_OK)                                &&
 8000b6e:	2b00      	cmp	r3, #0
 8000b70:	f040 8129 	bne.w	8000dc6 <HAL_ADC_Init+0x39a>
  {
    /* Set ADC state */
    ADC_STATE_CLR_SET(hadc->State,
 8000b74:	687b      	ldr	r3, [r7, #4]
 8000b76:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000b78:	f423 7381 	bic.w	r3, r3, #258	@ 0x102
 8000b7c:	f043 0202 	orr.w	r2, r3, #2
 8000b80:	687b      	ldr	r3, [r7, #4]
 8000b82:	641a      	str	r2, [r3, #64]	@ 0x40
    /* Configuration of common ADC parameters                                 */
    
    /* Pointer to the common control register to which is belonging hadc      */
    /* (Depending on STM32F3 product, there may be up to 4 ADC and 2 common   */
    /* control registers)                                                     */
    tmpADC_Common = ADC_COMMON_REGISTER(hadc);
 8000b84:	687b      	ldr	r3, [r7, #4]
 8000b86:	681b      	ldr	r3, [r3, #0]
 8000b88:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8000b8c:	d004      	beq.n	8000b98 <HAL_ADC_Init+0x16c>
 8000b8e:	687b      	ldr	r3, [r7, #4]
 8000b90:	681b      	ldr	r3, [r3, #0]
 8000b92:	4a5e      	ldr	r2, [pc, #376]	@ (8000d0c <HAL_ADC_Init+0x2e0>)
 8000b94:	4293      	cmp	r3, r2
 8000b96:	d101      	bne.n	8000b9c <HAL_ADC_Init+0x170>
 8000b98:	4b5d      	ldr	r3, [pc, #372]	@ (8000d10 <HAL_ADC_Init+0x2e4>)
 8000b9a:	e000      	b.n	8000b9e <HAL_ADC_Init+0x172>
 8000b9c:	4b5d      	ldr	r3, [pc, #372]	@ (8000d14 <HAL_ADC_Init+0x2e8>)
 8000b9e:	65fb      	str	r3, [r7, #92]	@ 0x5c
    
    /* Set handle of the other ADC sharing the same common register           */
    ADC_COMMON_ADC_OTHER(hadc, &tmphadcSharingSameCommonRegister);
 8000ba0:	687b      	ldr	r3, [r7, #4]
 8000ba2:	681b      	ldr	r3, [r3, #0]
 8000ba4:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8000ba8:	d102      	bne.n	8000bb0 <HAL_ADC_Init+0x184>
 8000baa:	4b58      	ldr	r3, [pc, #352]	@ (8000d0c <HAL_ADC_Init+0x2e0>)
 8000bac:	60fb      	str	r3, [r7, #12]
 8000bae:	e01a      	b.n	8000be6 <HAL_ADC_Init+0x1ba>
 8000bb0:	687b      	ldr	r3, [r7, #4]
 8000bb2:	681b      	ldr	r3, [r3, #0]
 8000bb4:	4a55      	ldr	r2, [pc, #340]	@ (8000d0c <HAL_ADC_Init+0x2e0>)
 8000bb6:	4293      	cmp	r3, r2
 8000bb8:	d103      	bne.n	8000bc2 <HAL_ADC_Init+0x196>
 8000bba:	f04f 43a0 	mov.w	r3, #1342177280	@ 0x50000000
 8000bbe:	60fb      	str	r3, [r7, #12]
 8000bc0:	e011      	b.n	8000be6 <HAL_ADC_Init+0x1ba>
 8000bc2:	687b      	ldr	r3, [r7, #4]
 8000bc4:	681b      	ldr	r3, [r3, #0]
 8000bc6:	4a54      	ldr	r2, [pc, #336]	@ (8000d18 <HAL_ADC_Init+0x2ec>)
 8000bc8:	4293      	cmp	r3, r2
 8000bca:	d102      	bne.n	8000bd2 <HAL_ADC_Init+0x1a6>
 8000bcc:	4b53      	ldr	r3, [pc, #332]	@ (8000d1c <HAL_ADC_Init+0x2f0>)
 8000bce:	60fb      	str	r3, [r7, #12]
 8000bd0:	e009      	b.n	8000be6 <HAL_ADC_Init+0x1ba>
 8000bd2:	687b      	ldr	r3, [r7, #4]
 8000bd4:	681b      	ldr	r3, [r3, #0]
 8000bd6:	4a51      	ldr	r2, [pc, #324]	@ (8000d1c <HAL_ADC_Init+0x2f0>)
 8000bd8:	4293      	cmp	r3, r2
 8000bda:	d102      	bne.n	8000be2 <HAL_ADC_Init+0x1b6>
 8000bdc:	4b4e      	ldr	r3, [pc, #312]	@ (8000d18 <HAL_ADC_Init+0x2ec>)
 8000bde:	60fb      	str	r3, [r7, #12]
 8000be0:	e001      	b.n	8000be6 <HAL_ADC_Init+0x1ba>
 8000be2:	2300      	movs	r3, #0
 8000be4:	60fb      	str	r3, [r7, #12]
    
    
    /* Parameters update conditioned to ADC state:                            */
    /* Parameters that can be updated only when ADC is disabled:              */
    /*  - Multimode clock configuration                                       */
    if ((ADC_IS_ENABLE(hadc) == RESET)                                   &&
 8000be6:	687b      	ldr	r3, [r7, #4]
 8000be8:	681b      	ldr	r3, [r3, #0]
 8000bea:	689b      	ldr	r3, [r3, #8]
 8000bec:	f003 0303 	and.w	r3, r3, #3
 8000bf0:	2b01      	cmp	r3, #1
 8000bf2:	d108      	bne.n	8000c06 <HAL_ADC_Init+0x1da>
 8000bf4:	687b      	ldr	r3, [r7, #4]
 8000bf6:	681b      	ldr	r3, [r3, #0]
 8000bf8:	681b      	ldr	r3, [r3, #0]
 8000bfa:	f003 0301 	and.w	r3, r3, #1
 8000bfe:	2b01      	cmp	r3, #1
 8000c00:	d101      	bne.n	8000c06 <HAL_ADC_Init+0x1da>
 8000c02:	2301      	movs	r3, #1
 8000c04:	e000      	b.n	8000c08 <HAL_ADC_Init+0x1dc>
 8000c06:	2300      	movs	r3, #0
 8000c08:	2b00      	cmp	r3, #0
 8000c0a:	d11c      	bne.n	8000c46 <HAL_ADC_Init+0x21a>
        ((tmphadcSharingSameCommonRegister.Instance == NULL)         ||
 8000c0c:	68fb      	ldr	r3, [r7, #12]
    if ((ADC_IS_ENABLE(hadc) == RESET)                                   &&
 8000c0e:	2b00      	cmp	r3, #0
 8000c10:	d010      	beq.n	8000c34 <HAL_ADC_Init+0x208>
         (ADC_IS_ENABLE(&tmphadcSharingSameCommonRegister) == RESET)   )   )
 8000c12:	68fb      	ldr	r3, [r7, #12]
 8000c14:	689b      	ldr	r3, [r3, #8]
 8000c16:	f003 0303 	and.w	r3, r3, #3
 8000c1a:	2b01      	cmp	r3, #1
 8000c1c:	d107      	bne.n	8000c2e <HAL_ADC_Init+0x202>
 8000c1e:	68fb      	ldr	r3, [r7, #12]
 8000c20:	681b      	ldr	r3, [r3, #0]
 8000c22:	f003 0301 	and.w	r3, r3, #1
 8000c26:	2b01      	cmp	r3, #1
 8000c28:	d101      	bne.n	8000c2e <HAL_ADC_Init+0x202>
 8000c2a:	2301      	movs	r3, #1
 8000c2c:	e000      	b.n	8000c30 <HAL_ADC_Init+0x204>
 8000c2e:	2300      	movs	r3, #0
        ((tmphadcSharingSameCommonRegister.Instance == NULL)         ||
 8000c30:	2b00      	cmp	r3, #0
 8000c32:	d108      	bne.n	8000c46 <HAL_ADC_Init+0x21a>
      /*     into HAL_ADCEx_MultiModeConfigChannel() )                        */
      /*   - internal measurement paths: Vbat, temperature sensor, Vref       */
      /*     (set into HAL_ADC_ConfigChannel() or                             */
      /*     HAL_ADCEx_InjectedConfigChannel() )                              */
     
      MODIFY_REG(tmpADC_Common->CCR       ,
 8000c34:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8000c36:	689b      	ldr	r3, [r3, #8]
 8000c38:	f423 3240 	bic.w	r2, r3, #196608	@ 0x30000
 8000c3c:	687b      	ldr	r3, [r7, #4]
 8000c3e:	685b      	ldr	r3, [r3, #4]
 8000c40:	431a      	orrs	r2, r3
 8000c42:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8000c44:	609a      	str	r2, [r3, #8]
    /*  - external trigger to start conversion                                */
    /*  - external trigger polarity                                           */
    /*  - continuous conversion mode                                          */
    /*  - overrun                                                             */
    /*  - discontinuous mode                                                  */
    SET_BIT(tmpCFGR, ADC_CFGR_CONTINUOUS((uint32_t)hadc->Init.ContinuousConvMode) |
 8000c46:	687b      	ldr	r3, [r7, #4]
 8000c48:	7e5b      	ldrb	r3, [r3, #25]
 8000c4a:	035b      	lsls	r3, r3, #13
 8000c4c:	687a      	ldr	r2, [r7, #4]
 8000c4e:	6b52      	ldr	r2, [r2, #52]	@ 0x34
 8000c50:	2a01      	cmp	r2, #1
 8000c52:	d002      	beq.n	8000c5a <HAL_ADC_Init+0x22e>
 8000c54:	f44f 5280 	mov.w	r2, #4096	@ 0x1000
 8000c58:	e000      	b.n	8000c5c <HAL_ADC_Init+0x230>
 8000c5a:	2200      	movs	r2, #0
 8000c5c:	431a      	orrs	r2, r3
 8000c5e:	687b      	ldr	r3, [r7, #4]
 8000c60:	68db      	ldr	r3, [r3, #12]
 8000c62:	431a      	orrs	r2, r3
 8000c64:	687b      	ldr	r3, [r7, #4]
 8000c66:	689b      	ldr	r3, [r3, #8]
 8000c68:	4313      	orrs	r3, r2
 8000c6a:	6e3a      	ldr	r2, [r7, #96]	@ 0x60
 8000c6c:	4313      	orrs	r3, r2
 8000c6e:	663b      	str	r3, [r7, #96]	@ 0x60
                     ADC_CFGR_OVERRUN(hadc->Init.Overrun)               |
                     hadc->Init.DataAlign                               |
                     hadc->Init.Resolution                               );
    
    /* Enable discontinuous mode only if continuous mode is disabled */
    if (hadc->Init.DiscontinuousConvMode == ENABLE)
 8000c70:	687b      	ldr	r3, [r7, #4]
 8000c72:	f893 3020 	ldrb.w	r3, [r3, #32]
 8000c76:	2b01      	cmp	r3, #1
 8000c78:	d11b      	bne.n	8000cb2 <HAL_ADC_Init+0x286>
    {
      if (hadc->Init.ContinuousConvMode == DISABLE)
 8000c7a:	687b      	ldr	r3, [r7, #4]
 8000c7c:	7e5b      	ldrb	r3, [r3, #25]
 8000c7e:	2b00      	cmp	r3, #0
 8000c80:	d109      	bne.n	8000c96 <HAL_ADC_Init+0x26a>
      {
        /* Enable the selected ADC regular discontinuous mode */
        /* Set the number of channels to be converted in discontinuous mode */
        SET_BIT(tmpCFGR, ADC_CFGR_DISCEN                                            |
 8000c82:	687b      	ldr	r3, [r7, #4]
 8000c84:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8000c86:	3b01      	subs	r3, #1
 8000c88:	045a      	lsls	r2, r3, #17
 8000c8a:	6e3b      	ldr	r3, [r7, #96]	@ 0x60
 8000c8c:	4313      	orrs	r3, r2
 8000c8e:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 8000c92:	663b      	str	r3, [r7, #96]	@ 0x60
 8000c94:	e00d      	b.n	8000cb2 <HAL_ADC_Init+0x286>
        /* ADC regular group discontinuous was intended to be enabled,        */
        /* but ADC regular group modes continuous and sequencer discontinuous */
        /* cannot be enabled simultaneously.                                  */
        
        /* Update ADC state machine to error */
        ADC_STATE_CLR_SET(hadc->State,
 8000c96:	687b      	ldr	r3, [r7, #4]
 8000c98:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000c9a:	f023 0322 	bic.w	r3, r3, #34	@ 0x22
 8000c9e:	f043 0220 	orr.w	r2, r3, #32
 8000ca2:	687b      	ldr	r3, [r7, #4]
 8000ca4:	641a      	str	r2, [r3, #64]	@ 0x40
                          HAL_ADC_STATE_BUSY_INTERNAL,
                          HAL_ADC_STATE_ERROR_CONFIG);
        
        /* Set ADC error code to ADC IP internal error */
        SET_BIT(hadc->ErrorCode, HAL_ADC_ERROR_INTERNAL);
 8000ca6:	687b      	ldr	r3, [r7, #4]
 8000ca8:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 8000caa:	f043 0201 	orr.w	r2, r3, #1
 8000cae:	687b      	ldr	r3, [r7, #4]
 8000cb0:	645a      	str	r2, [r3, #68]	@ 0x44
    /* Enable external trigger if trigger selection is different of software  */
    /* start.                                                                 */
    /* Note: This configuration keeps the hardware feature of parameter       */
    /*       ExternalTrigConvEdge "trigger edge none" equivalent to           */
    /*       software start.                                                  */
    if (hadc->Init.ExternalTrigConv != ADC_SOFTWARE_START)
 8000cb2:	687b      	ldr	r3, [r7, #4]
 8000cb4:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8000cb6:	2b01      	cmp	r3, #1
 8000cb8:	d03a      	beq.n	8000d30 <HAL_ADC_Init+0x304>
    {
      SET_BIT(tmpCFGR, ADC_CFGR_EXTSEL_SET(hadc, hadc->Init.ExternalTrigConv) |
 8000cba:	687b      	ldr	r3, [r7, #4]
 8000cbc:	681b      	ldr	r3, [r3, #0]
 8000cbe:	4a16      	ldr	r2, [pc, #88]	@ (8000d18 <HAL_ADC_Init+0x2ec>)
 8000cc0:	4293      	cmp	r3, r2
 8000cc2:	d004      	beq.n	8000cce <HAL_ADC_Init+0x2a2>
 8000cc4:	687b      	ldr	r3, [r7, #4]
 8000cc6:	681b      	ldr	r3, [r3, #0]
 8000cc8:	4a14      	ldr	r2, [pc, #80]	@ (8000d1c <HAL_ADC_Init+0x2f0>)
 8000cca:	4293      	cmp	r3, r2
 8000ccc:	d128      	bne.n	8000d20 <HAL_ADC_Init+0x2f4>
 8000cce:	687b      	ldr	r3, [r7, #4]
 8000cd0:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8000cd2:	f5b3 7f30 	cmp.w	r3, #704	@ 0x2c0
 8000cd6:	d012      	beq.n	8000cfe <HAL_ADC_Init+0x2d2>
 8000cd8:	687b      	ldr	r3, [r7, #4]
 8000cda:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8000cdc:	f5b3 7f80 	cmp.w	r3, #256	@ 0x100
 8000ce0:	d00a      	beq.n	8000cf8 <HAL_ADC_Init+0x2cc>
 8000ce2:	687b      	ldr	r3, [r7, #4]
 8000ce4:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8000ce6:	f5b3 7fe0 	cmp.w	r3, #448	@ 0x1c0
 8000cea:	d002      	beq.n	8000cf2 <HAL_ADC_Init+0x2c6>
 8000cec:	687b      	ldr	r3, [r7, #4]
 8000cee:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8000cf0:	e018      	b.n	8000d24 <HAL_ADC_Init+0x2f8>
 8000cf2:	f44f 7380 	mov.w	r3, #256	@ 0x100
 8000cf6:	e015      	b.n	8000d24 <HAL_ADC_Init+0x2f8>
 8000cf8:	f44f 7330 	mov.w	r3, #704	@ 0x2c0
 8000cfc:	e012      	b.n	8000d24 <HAL_ADC_Init+0x2f8>
 8000cfe:	f44f 73e0 	mov.w	r3, #448	@ 0x1c0
 8000d02:	e00f      	b.n	8000d24 <HAL_ADC_Init+0x2f8>
 8000d04:	20000000 	.word	0x20000000
 8000d08:	431bde83 	.word	0x431bde83
 8000d0c:	50000100 	.word	0x50000100
 8000d10:	50000300 	.word	0x50000300
 8000d14:	50000700 	.word	0x50000700
 8000d18:	50000400 	.word	0x50000400
 8000d1c:	50000500 	.word	0x50000500
 8000d20:	687b      	ldr	r3, [r7, #4]
 8000d22:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8000d24:	687a      	ldr	r2, [r7, #4]
 8000d26:	6ad2      	ldr	r2, [r2, #44]	@ 0x2c
 8000d28:	4313      	orrs	r3, r2
 8000d2a:	6e3a      	ldr	r2, [r7, #96]	@ 0x60
 8000d2c:	4313      	orrs	r3, r2
 8000d2e:	663b      	str	r3, [r7, #96]	@ 0x60
    /* Parameters update conditioned to ADC state:                            */
    /* Parameters that can be updated when ADC is disabled or enabled without */
    /* conversion on going on regular and injected groups:                    */
    /*  - DMA continuous request                                              */
    /*  - LowPowerAutoWait feature                                            */
    if (ADC_IS_CONVERSION_ONGOING_REGULAR_INJECTED(hadc) == RESET)
 8000d30:	687b      	ldr	r3, [r7, #4]
 8000d32:	681b      	ldr	r3, [r3, #0]
 8000d34:	689b      	ldr	r3, [r3, #8]
 8000d36:	f003 030c 	and.w	r3, r3, #12
 8000d3a:	2b00      	cmp	r3, #0
 8000d3c:	d114      	bne.n	8000d68 <HAL_ADC_Init+0x33c>
    {
      CLEAR_BIT(hadc->Instance->CFGR, ADC_CFGR_AUTDLY |
 8000d3e:	687b      	ldr	r3, [r7, #4]
 8000d40:	681b      	ldr	r3, [r3, #0]
 8000d42:	68db      	ldr	r3, [r3, #12]
 8000d44:	687a      	ldr	r2, [r7, #4]
 8000d46:	6812      	ldr	r2, [r2, #0]
 8000d48:	f423 4380 	bic.w	r3, r3, #16384	@ 0x4000
 8000d4c:	f023 0302 	bic.w	r3, r3, #2
 8000d50:	60d3      	str	r3, [r2, #12]
                                      ADC_CFGR_DMACFG  );
      
      SET_BIT(tmpCFGR, ADC_CFGR_AUTOWAIT((uint32_t)hadc->Init.LowPowerAutoWait) |
 8000d52:	687b      	ldr	r3, [r7, #4]
 8000d54:	7e1b      	ldrb	r3, [r3, #24]
 8000d56:	039a      	lsls	r2, r3, #14
 8000d58:	687b      	ldr	r3, [r7, #4]
 8000d5a:	f893 3030 	ldrb.w	r3, [r3, #48]	@ 0x30
 8000d5e:	005b      	lsls	r3, r3, #1
 8000d60:	4313      	orrs	r3, r2
 8000d62:	6e3a      	ldr	r2, [r7, #96]	@ 0x60
 8000d64:	4313      	orrs	r3, r2
 8000d66:	663b      	str	r3, [r7, #96]	@ 0x60
                       ADC_CFGR_DMACONTREQ((uint32_t)hadc->Init.DMAContinuousRequests) );
    }
    
    /* Update ADC configuration register with previous settings */
    MODIFY_REG(hadc->Instance->CFGR,
 8000d68:	687b      	ldr	r3, [r7, #4]
 8000d6a:	681b      	ldr	r3, [r3, #0]
 8000d6c:	68da      	ldr	r2, [r3, #12]
 8000d6e:	4b1e      	ldr	r3, [pc, #120]	@ (8000de8 <HAL_ADC_Init+0x3bc>)
 8000d70:	4013      	ands	r3, r2
 8000d72:	687a      	ldr	r2, [r7, #4]
 8000d74:	6812      	ldr	r2, [r2, #0]
 8000d76:	6e39      	ldr	r1, [r7, #96]	@ 0x60
 8000d78:	430b      	orrs	r3, r1
 8000d7a:	60d3      	str	r3, [r2, #12]
    /*   Parameter "NbrOfConversion" is discarded.                            */
    /*   Note: Scan mode is not present by hardware on this device, but       */
    /*   emulated by software for alignment over all STM32 devices.           */
    /* - if scan mode is enabled, regular channels sequence length is set to  */
    /*   parameter "NbrOfConversion"                                          */   
    if (hadc->Init.ScanConvMode == ADC_SCAN_ENABLE)
 8000d7c:	687b      	ldr	r3, [r7, #4]
 8000d7e:	691b      	ldr	r3, [r3, #16]
 8000d80:	2b01      	cmp	r3, #1
 8000d82:	d10c      	bne.n	8000d9e <HAL_ADC_Init+0x372>
    {
      /* Set number of ranks in regular group sequencer */     
      MODIFY_REG(hadc->Instance->SQR1                     ,
 8000d84:	687b      	ldr	r3, [r7, #4]
 8000d86:	681b      	ldr	r3, [r3, #0]
 8000d88:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8000d8a:	f023 010f 	bic.w	r1, r3, #15
 8000d8e:	687b      	ldr	r3, [r7, #4]
 8000d90:	69db      	ldr	r3, [r3, #28]
 8000d92:	1e5a      	subs	r2, r3, #1
 8000d94:	687b      	ldr	r3, [r7, #4]
 8000d96:	681b      	ldr	r3, [r3, #0]
 8000d98:	430a      	orrs	r2, r1
 8000d9a:	631a      	str	r2, [r3, #48]	@ 0x30
 8000d9c:	e007      	b.n	8000dae <HAL_ADC_Init+0x382>
                 ADC_SQR1_L                               ,
                 (hadc->Init.NbrOfConversion - (uint8_t)1U) );  
    }
    else
    {
      CLEAR_BIT(hadc->Instance->SQR1, ADC_SQR1_L);
 8000d9e:	687b      	ldr	r3, [r7, #4]
 8000da0:	681b      	ldr	r3, [r3, #0]
 8000da2:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 8000da4:	687b      	ldr	r3, [r7, #4]
 8000da6:	681b      	ldr	r3, [r3, #0]
 8000da8:	f022 020f 	bic.w	r2, r2, #15
 8000dac:	631a      	str	r2, [r3, #48]	@ 0x30
    }
    
    /* Set ADC error code to none */
    ADC_CLEAR_ERRORCODE(hadc);
 8000dae:	687b      	ldr	r3, [r7, #4]
 8000db0:	2200      	movs	r2, #0
 8000db2:	645a      	str	r2, [r3, #68]	@ 0x44
    
    /* Set the ADC state */
    ADC_STATE_CLR_SET(hadc->State,
 8000db4:	687b      	ldr	r3, [r7, #4]
 8000db6:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000db8:	f023 0303 	bic.w	r3, r3, #3
 8000dbc:	f043 0201 	orr.w	r2, r3, #1
 8000dc0:	687b      	ldr	r3, [r7, #4]
 8000dc2:	641a      	str	r2, [r3, #64]	@ 0x40
 8000dc4:	e00a      	b.n	8000ddc <HAL_ADC_Init+0x3b0>
                      HAL_ADC_STATE_READY);
  }
  else
  {
    /* Update ADC state machine to error */
    ADC_STATE_CLR_SET(hadc->State,
 8000dc6:	687b      	ldr	r3, [r7, #4]
 8000dc8:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000dca:	f023 0312 	bic.w	r3, r3, #18
 8000dce:	f043 0210 	orr.w	r2, r3, #16
 8000dd2:	687b      	ldr	r3, [r7, #4]
 8000dd4:	641a      	str	r2, [r3, #64]	@ 0x40
                      HAL_ADC_STATE_BUSY_INTERNAL,
                      HAL_ADC_STATE_ERROR_INTERNAL);
    
    tmp_hal_status = HAL_ERROR; 
 8000dd6:	2301      	movs	r3, #1
 8000dd8:	f887 3067 	strb.w	r3, [r7, #103]	@ 0x67
  }
  
  
  /* Return function status */
  return tmp_hal_status;
 8000ddc:	f897 3067 	ldrb.w	r3, [r7, #103]	@ 0x67
}
 8000de0:	4618      	mov	r0, r3
 8000de2:	3768      	adds	r7, #104	@ 0x68
 8000de4:	46bd      	mov	sp, r7
 8000de6:	bd80      	pop	{r7, pc}
 8000de8:	fff0c007 	.word	0xfff0c007

08000dec <HAL_ADC_Start_DMA>:
  * @param  pData The destination Buffer address.
  * @param  Length The length of data to be transferred from ADC peripheral to memory.
  * @retval None
  */
HAL_StatusTypeDef HAL_ADC_Start_DMA(ADC_HandleTypeDef* hadc, uint32_t* pData, uint32_t Length)
{
 8000dec:	b580      	push	{r7, lr}
 8000dee:	b086      	sub	sp, #24
 8000df0:	af00      	add	r7, sp, #0
 8000df2:	60f8      	str	r0, [r7, #12]
 8000df4:	60b9      	str	r1, [r7, #8]
 8000df6:	607a      	str	r2, [r7, #4]
  HAL_StatusTypeDef tmp_hal_status = HAL_OK;
 8000df8:	2300      	movs	r3, #0
 8000dfa:	75fb      	strb	r3, [r7, #23]
  
  /* Check the parameters */
  assert_param(IS_ADC_ALL_INSTANCE(hadc->Instance));
  
  /* Perform ADC enable and conversion start if no conversion is on going */
  if (ADC_IS_CONVERSION_ONGOING_REGULAR(hadc) == RESET)
 8000dfc:	68fb      	ldr	r3, [r7, #12]
 8000dfe:	681b      	ldr	r3, [r3, #0]
 8000e00:	689b      	ldr	r3, [r3, #8]
 8000e02:	f003 0304 	and.w	r3, r3, #4
 8000e06:	2b00      	cmp	r3, #0
 8000e08:	f040 80f7 	bne.w	8000ffa <HAL_ADC_Start_DMA+0x20e>
  {
    /* Process locked */
    __HAL_LOCK(hadc);
 8000e0c:	68fb      	ldr	r3, [r7, #12]
 8000e0e:	f893 303c 	ldrb.w	r3, [r3, #60]	@ 0x3c
 8000e12:	2b01      	cmp	r3, #1
 8000e14:	d101      	bne.n	8000e1a <HAL_ADC_Start_DMA+0x2e>
 8000e16:	2302      	movs	r3, #2
 8000e18:	e0f2      	b.n	8001000 <HAL_ADC_Start_DMA+0x214>
 8000e1a:	68fb      	ldr	r3, [r7, #12]
 8000e1c:	2201      	movs	r2, #1
 8000e1e:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
    
    /* Verification if multimode is disabled (for devices with several ADC)   */
    /* If multimode is enabled, dedicated function multimode conversion       */
    /* start DMA must be used.                                                */
    if(ADC_COMMON_CCR_MULTI(hadc) == RESET)
 8000e22:	68fb      	ldr	r3, [r7, #12]
 8000e24:	681b      	ldr	r3, [r3, #0]
 8000e26:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8000e2a:	d004      	beq.n	8000e36 <HAL_ADC_Start_DMA+0x4a>
 8000e2c:	68fb      	ldr	r3, [r7, #12]
 8000e2e:	681b      	ldr	r3, [r3, #0]
 8000e30:	4a75      	ldr	r2, [pc, #468]	@ (8001008 <HAL_ADC_Start_DMA+0x21c>)
 8000e32:	4293      	cmp	r3, r2
 8000e34:	d109      	bne.n	8000e4a <HAL_ADC_Start_DMA+0x5e>
 8000e36:	4b75      	ldr	r3, [pc, #468]	@ (800100c <HAL_ADC_Start_DMA+0x220>)
 8000e38:	689b      	ldr	r3, [r3, #8]
 8000e3a:	f003 031f 	and.w	r3, r3, #31
 8000e3e:	2b00      	cmp	r3, #0
 8000e40:	bf0c      	ite	eq
 8000e42:	2301      	moveq	r3, #1
 8000e44:	2300      	movne	r3, #0
 8000e46:	b2db      	uxtb	r3, r3
 8000e48:	e008      	b.n	8000e5c <HAL_ADC_Start_DMA+0x70>
 8000e4a:	4b71      	ldr	r3, [pc, #452]	@ (8001010 <HAL_ADC_Start_DMA+0x224>)
 8000e4c:	689b      	ldr	r3, [r3, #8]
 8000e4e:	f003 031f 	and.w	r3, r3, #31
 8000e52:	2b00      	cmp	r3, #0
 8000e54:	bf0c      	ite	eq
 8000e56:	2301      	moveq	r3, #1
 8000e58:	2300      	movne	r3, #0
 8000e5a:	b2db      	uxtb	r3, r3
 8000e5c:	2b00      	cmp	r3, #0
 8000e5e:	f000 80c5 	beq.w	8000fec <HAL_ADC_Start_DMA+0x200>
    {
      /* Enable the ADC peripheral */
      tmp_hal_status = ADC_Enable(hadc);
 8000e62:	68f8      	ldr	r0, [r7, #12]
 8000e64:	f000 fd20 	bl	80018a8 <ADC_Enable>
 8000e68:	4603      	mov	r3, r0
 8000e6a:	75fb      	strb	r3, [r7, #23]
      
      /* Start conversion if ADC is effectively enabled */
      if (tmp_hal_status == HAL_OK)
 8000e6c:	7dfb      	ldrb	r3, [r7, #23]
 8000e6e:	2b00      	cmp	r3, #0
 8000e70:	f040 80b7 	bne.w	8000fe2 <HAL_ADC_Start_DMA+0x1f6>
      {
        /* Set ADC state                                                      */
        /* - Clear state bitfield related to regular group conversion results */
        /* - Set state bitfield related to regular operation                  */
        ADC_STATE_CLR_SET(hadc->State,
 8000e74:	68fb      	ldr	r3, [r7, #12]
 8000e76:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000e78:	f423 6370 	bic.w	r3, r3, #3840	@ 0xf00
 8000e7c:	f023 0301 	bic.w	r3, r3, #1
 8000e80:	f443 7280 	orr.w	r2, r3, #256	@ 0x100
 8000e84:	68fb      	ldr	r3, [r7, #12]
 8000e86:	641a      	str	r2, [r3, #64]	@ 0x40
                          HAL_ADC_STATE_REG_BUSY);
        
        /* Set group injected state (from auto-injection) and multimode state */
        /* for all cases of multimode: independent mode, multimode ADC master */
        /* or multimode ADC slave (for devices with several ADCs):            */
        if (ADC_NONMULTIMODE_OR_MULTIMODEMASTER(hadc))
 8000e88:	68fb      	ldr	r3, [r7, #12]
 8000e8a:	681b      	ldr	r3, [r3, #0]
 8000e8c:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8000e90:	d004      	beq.n	8000e9c <HAL_ADC_Start_DMA+0xb0>
 8000e92:	68fb      	ldr	r3, [r7, #12]
 8000e94:	681b      	ldr	r3, [r3, #0]
 8000e96:	4a5c      	ldr	r2, [pc, #368]	@ (8001008 <HAL_ADC_Start_DMA+0x21c>)
 8000e98:	4293      	cmp	r3, r2
 8000e9a:	d106      	bne.n	8000eaa <HAL_ADC_Start_DMA+0xbe>
 8000e9c:	4b5b      	ldr	r3, [pc, #364]	@ (800100c <HAL_ADC_Start_DMA+0x220>)
 8000e9e:	689b      	ldr	r3, [r3, #8]
 8000ea0:	f003 031f 	and.w	r3, r3, #31
 8000ea4:	2b00      	cmp	r3, #0
 8000ea6:	d010      	beq.n	8000eca <HAL_ADC_Start_DMA+0xde>
 8000ea8:	e005      	b.n	8000eb6 <HAL_ADC_Start_DMA+0xca>
 8000eaa:	4b59      	ldr	r3, [pc, #356]	@ (8001010 <HAL_ADC_Start_DMA+0x224>)
 8000eac:	689b      	ldr	r3, [r3, #8]
 8000eae:	f003 031f 	and.w	r3, r3, #31
 8000eb2:	2b00      	cmp	r3, #0
 8000eb4:	d009      	beq.n	8000eca <HAL_ADC_Start_DMA+0xde>
 8000eb6:	68fb      	ldr	r3, [r7, #12]
 8000eb8:	681b      	ldr	r3, [r3, #0]
 8000eba:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8000ebe:	d004      	beq.n	8000eca <HAL_ADC_Start_DMA+0xde>
 8000ec0:	68fb      	ldr	r3, [r7, #12]
 8000ec2:	681b      	ldr	r3, [r3, #0]
 8000ec4:	4a53      	ldr	r2, [pc, #332]	@ (8001014 <HAL_ADC_Start_DMA+0x228>)
 8000ec6:	4293      	cmp	r3, r2
 8000ec8:	d115      	bne.n	8000ef6 <HAL_ADC_Start_DMA+0x10a>
        {
          /* Set ADC state (ADC independent or master) */
          CLEAR_BIT(hadc->State, HAL_ADC_STATE_MULTIMODE_SLAVE);
 8000eca:	68fb      	ldr	r3, [r7, #12]
 8000ecc:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000ece:	f423 1280 	bic.w	r2, r3, #1048576	@ 0x100000
 8000ed2:	68fb      	ldr	r3, [r7, #12]
 8000ed4:	641a      	str	r2, [r3, #64]	@ 0x40
          
          /* If conversions on group regular are also triggering group injected,*/
          /* update ADC state.                                                  */
          if (READ_BIT(hadc->Instance->CFGR, ADC_CFGR_JAUTO) != RESET)
 8000ed6:	68fb      	ldr	r3, [r7, #12]
 8000ed8:	681b      	ldr	r3, [r3, #0]
 8000eda:	68db      	ldr	r3, [r3, #12]
 8000edc:	f003 7300 	and.w	r3, r3, #33554432	@ 0x2000000
 8000ee0:	2b00      	cmp	r3, #0
 8000ee2:	d036      	beq.n	8000f52 <HAL_ADC_Start_DMA+0x166>
          {
            ADC_STATE_CLR_SET(hadc->State, HAL_ADC_STATE_INJ_EOC, HAL_ADC_STATE_INJ_BUSY);  
 8000ee4:	68fb      	ldr	r3, [r7, #12]
 8000ee6:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000ee8:	f423 5340 	bic.w	r3, r3, #12288	@ 0x3000
 8000eec:	f443 5280 	orr.w	r2, r3, #4096	@ 0x1000
 8000ef0:	68fb      	ldr	r3, [r7, #12]
 8000ef2:	641a      	str	r2, [r3, #64]	@ 0x40
          if (READ_BIT(hadc->Instance->CFGR, ADC_CFGR_JAUTO) != RESET)
 8000ef4:	e02d      	b.n	8000f52 <HAL_ADC_Start_DMA+0x166>
          }
        }
        else
        {
          /* Set ADC state (ADC slave) */
          SET_BIT(hadc->State, HAL_ADC_STATE_MULTIMODE_SLAVE);
 8000ef6:	68fb      	ldr	r3, [r7, #12]
 8000ef8:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000efa:	f443 1280 	orr.w	r2, r3, #1048576	@ 0x100000
 8000efe:	68fb      	ldr	r3, [r7, #12]
 8000f00:	641a      	str	r2, [r3, #64]	@ 0x40
          
          /* If conversions on group regular are also triggering group injected,*/
          /* update ADC state.                                                  */
          if (ADC_MULTIMODE_AUTO_INJECTED(hadc))
 8000f02:	68fb      	ldr	r3, [r7, #12]
 8000f04:	681b      	ldr	r3, [r3, #0]
 8000f06:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8000f0a:	d004      	beq.n	8000f16 <HAL_ADC_Start_DMA+0x12a>
 8000f0c:	68fb      	ldr	r3, [r7, #12]
 8000f0e:	681b      	ldr	r3, [r3, #0]
 8000f10:	4a3d      	ldr	r2, [pc, #244]	@ (8001008 <HAL_ADC_Start_DMA+0x21c>)
 8000f12:	4293      	cmp	r3, r2
 8000f14:	d10a      	bne.n	8000f2c <HAL_ADC_Start_DMA+0x140>
 8000f16:	f04f 43a0 	mov.w	r3, #1342177280	@ 0x50000000
 8000f1a:	68db      	ldr	r3, [r3, #12]
 8000f1c:	f003 7300 	and.w	r3, r3, #33554432	@ 0x2000000
 8000f20:	2b00      	cmp	r3, #0
 8000f22:	bf14      	ite	ne
 8000f24:	2301      	movne	r3, #1
 8000f26:	2300      	moveq	r3, #0
 8000f28:	b2db      	uxtb	r3, r3
 8000f2a:	e008      	b.n	8000f3e <HAL_ADC_Start_DMA+0x152>
 8000f2c:	4b39      	ldr	r3, [pc, #228]	@ (8001014 <HAL_ADC_Start_DMA+0x228>)
 8000f2e:	68db      	ldr	r3, [r3, #12]
 8000f30:	f003 7300 	and.w	r3, r3, #33554432	@ 0x2000000
 8000f34:	2b00      	cmp	r3, #0
 8000f36:	bf14      	ite	ne
 8000f38:	2301      	movne	r3, #1
 8000f3a:	2300      	moveq	r3, #0
 8000f3c:	b2db      	uxtb	r3, r3
 8000f3e:	2b00      	cmp	r3, #0
 8000f40:	d007      	beq.n	8000f52 <HAL_ADC_Start_DMA+0x166>
          {
            ADC_STATE_CLR_SET(hadc->State, HAL_ADC_STATE_INJ_EOC, HAL_ADC_STATE_INJ_BUSY);
 8000f42:	68fb      	ldr	r3, [r7, #12]
 8000f44:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000f46:	f423 5340 	bic.w	r3, r3, #12288	@ 0x3000
 8000f4a:	f443 5280 	orr.w	r2, r3, #4096	@ 0x1000
 8000f4e:	68fb      	ldr	r3, [r7, #12]
 8000f50:	641a      	str	r2, [r3, #64]	@ 0x40
          }
        }
        
        /* State machine update: Check if an injected conversion is ongoing */
        if (HAL_IS_BIT_SET(hadc->State, HAL_ADC_STATE_INJ_BUSY))
 8000f52:	68fb      	ldr	r3, [r7, #12]
 8000f54:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000f56:	f403 5380 	and.w	r3, r3, #4096	@ 0x1000
 8000f5a:	f5b3 5f80 	cmp.w	r3, #4096	@ 0x1000
 8000f5e:	d106      	bne.n	8000f6e <HAL_ADC_Start_DMA+0x182>
        {
          /* Reset ADC error code fields related to conversions on group regular*/
          CLEAR_BIT(hadc->ErrorCode, (HAL_ADC_ERROR_OVR | HAL_ADC_ERROR_DMA));         
 8000f60:	68fb      	ldr	r3, [r7, #12]
 8000f62:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 8000f64:	f023 0206 	bic.w	r2, r3, #6
 8000f68:	68fb      	ldr	r3, [r7, #12]
 8000f6a:	645a      	str	r2, [r3, #68]	@ 0x44
 8000f6c:	e002      	b.n	8000f74 <HAL_ADC_Start_DMA+0x188>
        }
        else
        {
          /* Reset ADC all error code fields */
          ADC_CLEAR_ERRORCODE(hadc);
 8000f6e:	68fb      	ldr	r3, [r7, #12]
 8000f70:	2200      	movs	r2, #0
 8000f72:	645a      	str	r2, [r3, #68]	@ 0x44
        }
        
        /* Process unlocked */
        /* Unlock before starting ADC conversions: in case of potential         */
        /* interruption, to let the process to ADC IRQ Handler.                 */
        __HAL_UNLOCK(hadc);
 8000f74:	68fb      	ldr	r3, [r7, #12]
 8000f76:	2200      	movs	r2, #0
 8000f78:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
        
        
        /* Set the DMA transfer complete callback */
        hadc->DMA_Handle->XferCpltCallback = ADC_DMAConvCplt;
 8000f7c:	68fb      	ldr	r3, [r7, #12]
 8000f7e:	6b9b      	ldr	r3, [r3, #56]	@ 0x38
 8000f80:	4a25      	ldr	r2, [pc, #148]	@ (8001018 <HAL_ADC_Start_DMA+0x22c>)
 8000f82:	629a      	str	r2, [r3, #40]	@ 0x28

        /* Set the DMA half transfer complete callback */
        hadc->DMA_Handle->XferHalfCpltCallback = ADC_DMAHalfConvCplt;
 8000f84:	68fb      	ldr	r3, [r7, #12]
 8000f86:	6b9b      	ldr	r3, [r3, #56]	@ 0x38
 8000f88:	4a24      	ldr	r2, [pc, #144]	@ (800101c <HAL_ADC_Start_DMA+0x230>)
 8000f8a:	62da      	str	r2, [r3, #44]	@ 0x2c
        
        /* Set the DMA error callback */
        hadc->DMA_Handle->XferErrorCallback = ADC_DMAError;
 8000f8c:	68fb      	ldr	r3, [r7, #12]
 8000f8e:	6b9b      	ldr	r3, [r3, #56]	@ 0x38
 8000f90:	4a23      	ldr	r2, [pc, #140]	@ (8001020 <HAL_ADC_Start_DMA+0x234>)
 8000f92:	631a      	str	r2, [r3, #48]	@ 0x30
        /* start (in case of SW start):                                       */
        
        /* Clear regular group conversion flag and overrun flag */
        /* (To ensure of no unknown state from potential previous ADC         */
        /* operations)                                                        */
        __HAL_ADC_CLEAR_FLAG(hadc, (ADC_FLAG_EOC | ADC_FLAG_EOS | ADC_FLAG_OVR));
 8000f94:	68fb      	ldr	r3, [r7, #12]
 8000f96:	681b      	ldr	r3, [r3, #0]
 8000f98:	221c      	movs	r2, #28
 8000f9a:	601a      	str	r2, [r3, #0]
        
        /* Enable ADC overrun interrupt */
        __HAL_ADC_ENABLE_IT(hadc, ADC_IT_OVR);
 8000f9c:	68fb      	ldr	r3, [r7, #12]
 8000f9e:	681b      	ldr	r3, [r3, #0]
 8000fa0:	685a      	ldr	r2, [r3, #4]
 8000fa2:	68fb      	ldr	r3, [r7, #12]
 8000fa4:	681b      	ldr	r3, [r3, #0]
 8000fa6:	f042 0210 	orr.w	r2, r2, #16
 8000faa:	605a      	str	r2, [r3, #4]
        
        /* Enable ADC DMA mode */
        SET_BIT(hadc->Instance->CFGR, ADC_CFGR_DMAEN);
 8000fac:	68fb      	ldr	r3, [r7, #12]
 8000fae:	681b      	ldr	r3, [r3, #0]
 8000fb0:	68da      	ldr	r2, [r3, #12]
 8000fb2:	68fb      	ldr	r3, [r7, #12]
 8000fb4:	681b      	ldr	r3, [r3, #0]
 8000fb6:	f042 0201 	orr.w	r2, r2, #1
 8000fba:	60da      	str	r2, [r3, #12]
        
        /* Start the DMA channel */
        HAL_DMA_Start_IT(hadc->DMA_Handle, (uint32_t)&hadc->Instance->DR, (uint32_t)pData, Length);
 8000fbc:	68fb      	ldr	r3, [r7, #12]
 8000fbe:	6b98      	ldr	r0, [r3, #56]	@ 0x38
 8000fc0:	68fb      	ldr	r3, [r7, #12]
 8000fc2:	681b      	ldr	r3, [r3, #0]
 8000fc4:	3340      	adds	r3, #64	@ 0x40
 8000fc6:	4619      	mov	r1, r3
 8000fc8:	68ba      	ldr	r2, [r7, #8]
 8000fca:	687b      	ldr	r3, [r7, #4]
 8000fcc:	f000 ff6d 	bl	8001eaa <HAL_DMA_Start_IT>
                 
        /* Enable conversion of regular group.                                */
        /* If software start has been selected, conversion starts immediately.*/
        /* If external trigger has been selected, conversion will start at    */
        /* next trigger event.                                                */
        SET_BIT(hadc->Instance->CR, ADC_CR_ADSTART);
 8000fd0:	68fb      	ldr	r3, [r7, #12]
 8000fd2:	681b      	ldr	r3, [r3, #0]
 8000fd4:	689a      	ldr	r2, [r3, #8]
 8000fd6:	68fb      	ldr	r3, [r7, #12]
 8000fd8:	681b      	ldr	r3, [r3, #0]
 8000fda:	f042 0204 	orr.w	r2, r2, #4
 8000fde:	609a      	str	r2, [r3, #8]
 8000fe0:	e00d      	b.n	8000ffe <HAL_ADC_Start_DMA+0x212>
        
      }
      else
      {
        /* Process unlocked */
        __HAL_UNLOCK(hadc);
 8000fe2:	68fb      	ldr	r3, [r7, #12]
 8000fe4:	2200      	movs	r2, #0
 8000fe6:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
 8000fea:	e008      	b.n	8000ffe <HAL_ADC_Start_DMA+0x212>
      }
    }
    else
    {
      tmp_hal_status = HAL_ERROR;
 8000fec:	2301      	movs	r3, #1
 8000fee:	75fb      	strb	r3, [r7, #23]
      
      /* Process unlocked */
      __HAL_UNLOCK(hadc);
 8000ff0:	68fb      	ldr	r3, [r7, #12]
 8000ff2:	2200      	movs	r2, #0
 8000ff4:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
 8000ff8:	e001      	b.n	8000ffe <HAL_ADC_Start_DMA+0x212>
    }
  }
  else
  {
    tmp_hal_status = HAL_BUSY;
 8000ffa:	2302      	movs	r3, #2
 8000ffc:	75fb      	strb	r3, [r7, #23]
  }
  
  /* Return function status */
  return tmp_hal_status;
 8000ffe:	7dfb      	ldrb	r3, [r7, #23]
}
 8001000:	4618      	mov	r0, r3
 8001002:	3718      	adds	r7, #24
 8001004:	46bd      	mov	sp, r7
 8001006:	bd80      	pop	{r7, pc}
 8001008:	50000100 	.word	0x50000100
 800100c:	50000300 	.word	0x50000300
 8001010:	50000700 	.word	0x50000700
 8001014:	50000400 	.word	0x50000400
 8001018:	080017dd 	.word	0x080017dd
 800101c:	08001857 	.word	0x08001857
 8001020:	08001873 	.word	0x08001873

08001024 <HAL_ADC_ConfigChannel>:
  * @param  hadc ADC handle
  * @param  sConfig Structure ADC channel for regular group.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_ADC_ConfigChannel(ADC_HandleTypeDef* hadc, ADC_ChannelConfTypeDef* sConfig)
{
 8001024:	b480      	push	{r7}
 8001026:	b09b      	sub	sp, #108	@ 0x6c
 8001028:	af00      	add	r7, sp, #0
 800102a:	6078      	str	r0, [r7, #4]
 800102c:	6039      	str	r1, [r7, #0]
  HAL_StatusTypeDef tmp_hal_status = HAL_OK;
 800102e:	2300      	movs	r3, #0
 8001030:	f887 3067 	strb.w	r3, [r7, #103]	@ 0x67
  ADC_Common_TypeDef *tmpADC_Common;
  ADC_HandleTypeDef tmphadcSharingSameCommonRegister;
  uint32_t tmpOffsetShifted;
  __IO uint32_t wait_loop_index = 0U;
 8001034:	2300      	movs	r3, #0
 8001036:	60bb      	str	r3, [r7, #8]
  {
    assert_param(IS_ADC_DIFF_CHANNEL(sConfig->Channel));
  }
  
  /* Process locked */
  __HAL_LOCK(hadc);
 8001038:	687b      	ldr	r3, [r7, #4]
 800103a:	f893 303c 	ldrb.w	r3, [r3, #60]	@ 0x3c
 800103e:	2b01      	cmp	r3, #1
 8001040:	d101      	bne.n	8001046 <HAL_ADC_ConfigChannel+0x22>
 8001042:	2302      	movs	r3, #2
 8001044:	e2c8      	b.n	80015d8 <HAL_ADC_ConfigChannel+0x5b4>
 8001046:	687b      	ldr	r3, [r7, #4]
 8001048:	2201      	movs	r2, #1
 800104a:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
  /* Parameters update conditioned to ADC state:                              */
  /* Parameters that can be updated when ADC is disabled or enabled without   */
  /* conversion on going on regular group:                                    */
  /*  - Channel number                                                        */
  /*  - Channel rank                                                          */
  if (ADC_IS_CONVERSION_ONGOING_REGULAR(hadc) == RESET)
 800104e:	687b      	ldr	r3, [r7, #4]
 8001050:	681b      	ldr	r3, [r3, #0]
 8001052:	689b      	ldr	r3, [r3, #8]
 8001054:	f003 0304 	and.w	r3, r3, #4
 8001058:	2b00      	cmp	r3, #0
 800105a:	f040 82ac 	bne.w	80015b6 <HAL_ADC_ConfigChannel+0x592>
  {
    /* Regular sequence configuration */
    /* For Rank 1 to 4U */
    if (sConfig->Rank < 5U)
 800105e:	683b      	ldr	r3, [r7, #0]
 8001060:	685b      	ldr	r3, [r3, #4]
 8001062:	2b04      	cmp	r3, #4
 8001064:	d81c      	bhi.n	80010a0 <HAL_ADC_ConfigChannel+0x7c>
    {
      MODIFY_REG(hadc->Instance->SQR1,
 8001066:	687b      	ldr	r3, [r7, #4]
 8001068:	681b      	ldr	r3, [r3, #0]
 800106a:	6b19      	ldr	r1, [r3, #48]	@ 0x30
 800106c:	683b      	ldr	r3, [r7, #0]
 800106e:	685a      	ldr	r2, [r3, #4]
 8001070:	4613      	mov	r3, r2
 8001072:	005b      	lsls	r3, r3, #1
 8001074:	4413      	add	r3, r2
 8001076:	005b      	lsls	r3, r3, #1
 8001078:	461a      	mov	r2, r3
 800107a:	231f      	movs	r3, #31
 800107c:	4093      	lsls	r3, r2
 800107e:	43db      	mvns	r3, r3
 8001080:	4019      	ands	r1, r3
 8001082:	683b      	ldr	r3, [r7, #0]
 8001084:	6818      	ldr	r0, [r3, #0]
 8001086:	683b      	ldr	r3, [r7, #0]
 8001088:	685a      	ldr	r2, [r3, #4]
 800108a:	4613      	mov	r3, r2
 800108c:	005b      	lsls	r3, r3, #1
 800108e:	4413      	add	r3, r2
 8001090:	005b      	lsls	r3, r3, #1
 8001092:	fa00 f203 	lsl.w	r2, r0, r3
 8001096:	687b      	ldr	r3, [r7, #4]
 8001098:	681b      	ldr	r3, [r3, #0]
 800109a:	430a      	orrs	r2, r1
 800109c:	631a      	str	r2, [r3, #48]	@ 0x30
 800109e:	e063      	b.n	8001168 <HAL_ADC_ConfigChannel+0x144>
                 ADC_SQR1_RK(ADC_SQR2_SQ5, sConfig->Rank)    ,
                 ADC_SQR1_RK(sConfig->Channel, sConfig->Rank) );
    }
    /* For Rank 5 to 9U */
    else if (sConfig->Rank < 10U)
 80010a0:	683b      	ldr	r3, [r7, #0]
 80010a2:	685b      	ldr	r3, [r3, #4]
 80010a4:	2b09      	cmp	r3, #9
 80010a6:	d81e      	bhi.n	80010e6 <HAL_ADC_ConfigChannel+0xc2>
    {
      MODIFY_REG(hadc->Instance->SQR2,
 80010a8:	687b      	ldr	r3, [r7, #4]
 80010aa:	681b      	ldr	r3, [r3, #0]
 80010ac:	6b59      	ldr	r1, [r3, #52]	@ 0x34
 80010ae:	683b      	ldr	r3, [r7, #0]
 80010b0:	685a      	ldr	r2, [r3, #4]
 80010b2:	4613      	mov	r3, r2
 80010b4:	005b      	lsls	r3, r3, #1
 80010b6:	4413      	add	r3, r2
 80010b8:	005b      	lsls	r3, r3, #1
 80010ba:	3b1e      	subs	r3, #30
 80010bc:	221f      	movs	r2, #31
 80010be:	fa02 f303 	lsl.w	r3, r2, r3
 80010c2:	43db      	mvns	r3, r3
 80010c4:	4019      	ands	r1, r3
 80010c6:	683b      	ldr	r3, [r7, #0]
 80010c8:	6818      	ldr	r0, [r3, #0]
 80010ca:	683b      	ldr	r3, [r7, #0]
 80010cc:	685a      	ldr	r2, [r3, #4]
 80010ce:	4613      	mov	r3, r2
 80010d0:	005b      	lsls	r3, r3, #1
 80010d2:	4413      	add	r3, r2
 80010d4:	005b      	lsls	r3, r3, #1
 80010d6:	3b1e      	subs	r3, #30
 80010d8:	fa00 f203 	lsl.w	r2, r0, r3
 80010dc:	687b      	ldr	r3, [r7, #4]
 80010de:	681b      	ldr	r3, [r3, #0]
 80010e0:	430a      	orrs	r2, r1
 80010e2:	635a      	str	r2, [r3, #52]	@ 0x34
 80010e4:	e040      	b.n	8001168 <HAL_ADC_ConfigChannel+0x144>
                 ADC_SQR2_RK(ADC_SQR2_SQ5, sConfig->Rank)    ,
                 ADC_SQR2_RK(sConfig->Channel, sConfig->Rank) );
    }
    /* For Rank 10 to 14U */
    else if (sConfig->Rank < 15U)
 80010e6:	683b      	ldr	r3, [r7, #0]
 80010e8:	685b      	ldr	r3, [r3, #4]
 80010ea:	2b0e      	cmp	r3, #14
 80010ec:	d81e      	bhi.n	800112c <HAL_ADC_ConfigChannel+0x108>
    {
      MODIFY_REG(hadc->Instance->SQR3                        ,
 80010ee:	687b      	ldr	r3, [r7, #4]
 80010f0:	681b      	ldr	r3, [r3, #0]
 80010f2:	6b99      	ldr	r1, [r3, #56]	@ 0x38
 80010f4:	683b      	ldr	r3, [r7, #0]
 80010f6:	685a      	ldr	r2, [r3, #4]
 80010f8:	4613      	mov	r3, r2
 80010fa:	005b      	lsls	r3, r3, #1
 80010fc:	4413      	add	r3, r2
 80010fe:	005b      	lsls	r3, r3, #1
 8001100:	3b3c      	subs	r3, #60	@ 0x3c
 8001102:	221f      	movs	r2, #31
 8001104:	fa02 f303 	lsl.w	r3, r2, r3
 8001108:	43db      	mvns	r3, r3
 800110a:	4019      	ands	r1, r3
 800110c:	683b      	ldr	r3, [r7, #0]
 800110e:	6818      	ldr	r0, [r3, #0]
 8001110:	683b      	ldr	r3, [r7, #0]
 8001112:	685a      	ldr	r2, [r3, #4]
 8001114:	4613      	mov	r3, r2
 8001116:	005b      	lsls	r3, r3, #1
 8001118:	4413      	add	r3, r2
 800111a:	005b      	lsls	r3, r3, #1
 800111c:	3b3c      	subs	r3, #60	@ 0x3c
 800111e:	fa00 f203 	lsl.w	r2, r0, r3
 8001122:	687b      	ldr	r3, [r7, #4]
 8001124:	681b      	ldr	r3, [r3, #0]
 8001126:	430a      	orrs	r2, r1
 8001128:	639a      	str	r2, [r3, #56]	@ 0x38
 800112a:	e01d      	b.n	8001168 <HAL_ADC_ConfigChannel+0x144>
                 ADC_SQR3_RK(sConfig->Channel, sConfig->Rank) );
    }
    /* For Rank 15 to 16U */
    else
    {   
      MODIFY_REG(hadc->Instance->SQR4                        ,
 800112c:	687b      	ldr	r3, [r7, #4]
 800112e:	681b      	ldr	r3, [r3, #0]
 8001130:	6bd9      	ldr	r1, [r3, #60]	@ 0x3c
 8001132:	683b      	ldr	r3, [r7, #0]
 8001134:	685a      	ldr	r2, [r3, #4]
 8001136:	4613      	mov	r3, r2
 8001138:	005b      	lsls	r3, r3, #1
 800113a:	4413      	add	r3, r2
 800113c:	005b      	lsls	r3, r3, #1
 800113e:	3b5a      	subs	r3, #90	@ 0x5a
 8001140:	221f      	movs	r2, #31
 8001142:	fa02 f303 	lsl.w	r3, r2, r3
 8001146:	43db      	mvns	r3, r3
 8001148:	4019      	ands	r1, r3
 800114a:	683b      	ldr	r3, [r7, #0]
 800114c:	6818      	ldr	r0, [r3, #0]
 800114e:	683b      	ldr	r3, [r7, #0]
 8001150:	685a      	ldr	r2, [r3, #4]
 8001152:	4613      	mov	r3, r2
 8001154:	005b      	lsls	r3, r3, #1
 8001156:	4413      	add	r3, r2
 8001158:	005b      	lsls	r3, r3, #1
 800115a:	3b5a      	subs	r3, #90	@ 0x5a
 800115c:	fa00 f203 	lsl.w	r2, r0, r3
 8001160:	687b      	ldr	r3, [r7, #4]
 8001162:	681b      	ldr	r3, [r3, #0]
 8001164:	430a      	orrs	r2, r1
 8001166:	63da      	str	r2, [r3, #60]	@ 0x3c
  /* Parameters update conditioned to ADC state:                              */
  /* Parameters that can be updated when ADC is disabled or enabled without   */
  /* conversion on going on regular group:                                    */
  /*  - Channel sampling time                                                 */
  /*  - Channel offset                                                        */
  if (ADC_IS_CONVERSION_ONGOING_REGULAR_INJECTED(hadc) == RESET)
 8001168:	687b      	ldr	r3, [r7, #4]
 800116a:	681b      	ldr	r3, [r3, #0]
 800116c:	689b      	ldr	r3, [r3, #8]
 800116e:	f003 030c 	and.w	r3, r3, #12
 8001172:	2b00      	cmp	r3, #0
 8001174:	f040 80e5 	bne.w	8001342 <HAL_ADC_ConfigChannel+0x31e>
  {
    /* Channel sampling time configuration */
    /* For channels 10 to 18U */
    if (sConfig->Channel >= ADC_CHANNEL_10)
 8001178:	683b      	ldr	r3, [r7, #0]
 800117a:	681b      	ldr	r3, [r3, #0]
 800117c:	2b09      	cmp	r3, #9
 800117e:	d91c      	bls.n	80011ba <HAL_ADC_ConfigChannel+0x196>
    {
      MODIFY_REG(hadc->Instance->SMPR2                             ,
 8001180:	687b      	ldr	r3, [r7, #4]
 8001182:	681b      	ldr	r3, [r3, #0]
 8001184:	6999      	ldr	r1, [r3, #24]
 8001186:	683b      	ldr	r3, [r7, #0]
 8001188:	681a      	ldr	r2, [r3, #0]
 800118a:	4613      	mov	r3, r2
 800118c:	005b      	lsls	r3, r3, #1
 800118e:	4413      	add	r3, r2
 8001190:	3b1e      	subs	r3, #30
 8001192:	2207      	movs	r2, #7
 8001194:	fa02 f303 	lsl.w	r3, r2, r3
 8001198:	43db      	mvns	r3, r3
 800119a:	4019      	ands	r1, r3
 800119c:	683b      	ldr	r3, [r7, #0]
 800119e:	6898      	ldr	r0, [r3, #8]
 80011a0:	683b      	ldr	r3, [r7, #0]
 80011a2:	681a      	ldr	r2, [r3, #0]
 80011a4:	4613      	mov	r3, r2
 80011a6:	005b      	lsls	r3, r3, #1
 80011a8:	4413      	add	r3, r2
 80011aa:	3b1e      	subs	r3, #30
 80011ac:	fa00 f203 	lsl.w	r2, r0, r3
 80011b0:	687b      	ldr	r3, [r7, #4]
 80011b2:	681b      	ldr	r3, [r3, #0]
 80011b4:	430a      	orrs	r2, r1
 80011b6:	619a      	str	r2, [r3, #24]
 80011b8:	e019      	b.n	80011ee <HAL_ADC_ConfigChannel+0x1ca>
                 ADC_SMPR2(ADC_SMPR2_SMP10, sConfig->Channel)      ,
                 ADC_SMPR2(sConfig->SamplingTime, sConfig->Channel) );
    }
    else /* For channels 1 to 9U */
    {
      MODIFY_REG(hadc->Instance->SMPR1                             ,
 80011ba:	687b      	ldr	r3, [r7, #4]
 80011bc:	681b      	ldr	r3, [r3, #0]
 80011be:	6959      	ldr	r1, [r3, #20]
 80011c0:	683b      	ldr	r3, [r7, #0]
 80011c2:	681a      	ldr	r2, [r3, #0]
 80011c4:	4613      	mov	r3, r2
 80011c6:	005b      	lsls	r3, r3, #1
 80011c8:	4413      	add	r3, r2
 80011ca:	2207      	movs	r2, #7
 80011cc:	fa02 f303 	lsl.w	r3, r2, r3
 80011d0:	43db      	mvns	r3, r3
 80011d2:	4019      	ands	r1, r3
 80011d4:	683b      	ldr	r3, [r7, #0]
 80011d6:	6898      	ldr	r0, [r3, #8]
 80011d8:	683b      	ldr	r3, [r7, #0]
 80011da:	681a      	ldr	r2, [r3, #0]
 80011dc:	4613      	mov	r3, r2
 80011de:	005b      	lsls	r3, r3, #1
 80011e0:	4413      	add	r3, r2
 80011e2:	fa00 f203 	lsl.w	r2, r0, r3
 80011e6:	687b      	ldr	r3, [r7, #4]
 80011e8:	681b      	ldr	r3, [r3, #0]
 80011ea:	430a      	orrs	r2, r1
 80011ec:	615a      	str	r2, [r3, #20]
    /* Configure the offset: offset enable/disable, channel, offset value */

    /* Shift the offset in function of the selected ADC resolution. */
    /* Offset has to be left-aligned on bit 11U, the LSB (right bits) are set  */
    /* to 0.                                                                  */
    tmpOffsetShifted = ADC_OFFSET_SHIFT_RESOLUTION(hadc, sConfig->Offset);
 80011ee:	683b      	ldr	r3, [r7, #0]
 80011f0:	695a      	ldr	r2, [r3, #20]
 80011f2:	687b      	ldr	r3, [r7, #4]
 80011f4:	681b      	ldr	r3, [r3, #0]
 80011f6:	68db      	ldr	r3, [r3, #12]
 80011f8:	08db      	lsrs	r3, r3, #3
 80011fa:	f003 0303 	and.w	r3, r3, #3
 80011fe:	005b      	lsls	r3, r3, #1
 8001200:	fa02 f303 	lsl.w	r3, r2, r3
 8001204:	663b      	str	r3, [r7, #96]	@ 0x60
    
    /* Configure the selected offset register:                                */
    /* - Enable offset                                                        */
    /* - Set channel number                                                   */
    /* - Set offset value                                                     */
    switch (sConfig->OffsetNumber)
 8001206:	683b      	ldr	r3, [r7, #0]
 8001208:	691b      	ldr	r3, [r3, #16]
 800120a:	3b01      	subs	r3, #1
 800120c:	2b03      	cmp	r3, #3
 800120e:	d84f      	bhi.n	80012b0 <HAL_ADC_ConfigChannel+0x28c>
 8001210:	a201      	add	r2, pc, #4	@ (adr r2, 8001218 <HAL_ADC_ConfigChannel+0x1f4>)
 8001212:	f852 f023 	ldr.w	pc, [r2, r3, lsl #2]
 8001216:	bf00      	nop
 8001218:	08001229 	.word	0x08001229
 800121c:	0800124b 	.word	0x0800124b
 8001220:	0800126d 	.word	0x0800126d
 8001224:	0800128f 	.word	0x0800128f
    {
    case ADC_OFFSET_1:
      /* Configure offset register 1U */
      MODIFY_REG(hadc->Instance->OFR1               ,
 8001228:	687b      	ldr	r3, [r7, #4]
 800122a:	681b      	ldr	r3, [r3, #0]
 800122c:	6e1a      	ldr	r2, [r3, #96]	@ 0x60
 800122e:	4b99      	ldr	r3, [pc, #612]	@ (8001494 <HAL_ADC_ConfigChannel+0x470>)
 8001230:	4013      	ands	r3, r2
 8001232:	683a      	ldr	r2, [r7, #0]
 8001234:	6812      	ldr	r2, [r2, #0]
 8001236:	0691      	lsls	r1, r2, #26
 8001238:	6e3a      	ldr	r2, [r7, #96]	@ 0x60
 800123a:	430a      	orrs	r2, r1
 800123c:	431a      	orrs	r2, r3
 800123e:	687b      	ldr	r3, [r7, #4]
 8001240:	681b      	ldr	r3, [r3, #0]
 8001242:	f042 4200 	orr.w	r2, r2, #2147483648	@ 0x80000000
 8001246:	661a      	str	r2, [r3, #96]	@ 0x60
                 ADC_OFR1_OFFSET1_CH |
                 ADC_OFR1_OFFSET1                   ,
                 ADC_OFR1_OFFSET1_EN               |
                 ADC_OFR_CHANNEL(sConfig->Channel) |
                 tmpOffsetShifted                    );
      break;
 8001248:	e07b      	b.n	8001342 <HAL_ADC_ConfigChannel+0x31e>
    
    case ADC_OFFSET_2:
      /* Configure offset register 2U */
      MODIFY_REG(hadc->Instance->OFR2               ,
 800124a:	687b      	ldr	r3, [r7, #4]
 800124c:	681b      	ldr	r3, [r3, #0]
 800124e:	6e5a      	ldr	r2, [r3, #100]	@ 0x64
 8001250:	4b90      	ldr	r3, [pc, #576]	@ (8001494 <HAL_ADC_ConfigChannel+0x470>)
 8001252:	4013      	ands	r3, r2
 8001254:	683a      	ldr	r2, [r7, #0]
 8001256:	6812      	ldr	r2, [r2, #0]
 8001258:	0691      	lsls	r1, r2, #26
 800125a:	6e3a      	ldr	r2, [r7, #96]	@ 0x60
 800125c:	430a      	orrs	r2, r1
 800125e:	431a      	orrs	r2, r3
 8001260:	687b      	ldr	r3, [r7, #4]
 8001262:	681b      	ldr	r3, [r3, #0]
 8001264:	f042 4200 	orr.w	r2, r2, #2147483648	@ 0x80000000
 8001268:	665a      	str	r2, [r3, #100]	@ 0x64
                 ADC_OFR2_OFFSET2_CH |
                 ADC_OFR2_OFFSET2                   ,
                 ADC_OFR2_OFFSET2_EN               |
                 ADC_OFR_CHANNEL(sConfig->Channel) |
                 tmpOffsetShifted                    );
      break;
 800126a:	e06a      	b.n	8001342 <HAL_ADC_ConfigChannel+0x31e>
        
    case ADC_OFFSET_3:
      /* Configure offset register 3U */
      MODIFY_REG(hadc->Instance->OFR3               ,
 800126c:	687b      	ldr	r3, [r7, #4]
 800126e:	681b      	ldr	r3, [r3, #0]
 8001270:	6e9a      	ldr	r2, [r3, #104]	@ 0x68
 8001272:	4b88      	ldr	r3, [pc, #544]	@ (8001494 <HAL_ADC_ConfigChannel+0x470>)
 8001274:	4013      	ands	r3, r2
 8001276:	683a      	ldr	r2, [r7, #0]
 8001278:	6812      	ldr	r2, [r2, #0]
 800127a:	0691      	lsls	r1, r2, #26
 800127c:	6e3a      	ldr	r2, [r7, #96]	@ 0x60
 800127e:	430a      	orrs	r2, r1
 8001280:	431a      	orrs	r2, r3
 8001282:	687b      	ldr	r3, [r7, #4]
 8001284:	681b      	ldr	r3, [r3, #0]
 8001286:	f042 4200 	orr.w	r2, r2, #2147483648	@ 0x80000000
 800128a:	669a      	str	r2, [r3, #104]	@ 0x68
                 ADC_OFR3_OFFSET3_CH |
                 ADC_OFR3_OFFSET3                   ,
                 ADC_OFR3_OFFSET3_EN               |
                 ADC_OFR_CHANNEL(sConfig->Channel) |
                 tmpOffsetShifted                    );
      break;
 800128c:	e059      	b.n	8001342 <HAL_ADC_ConfigChannel+0x31e>
    
    case ADC_OFFSET_4:
      /* Configure offset register 4U */
      MODIFY_REG(hadc->Instance->OFR4               ,
 800128e:	687b      	ldr	r3, [r7, #4]
 8001290:	681b      	ldr	r3, [r3, #0]
 8001292:	6eda      	ldr	r2, [r3, #108]	@ 0x6c
 8001294:	4b7f      	ldr	r3, [pc, #508]	@ (8001494 <HAL_ADC_ConfigChannel+0x470>)
 8001296:	4013      	ands	r3, r2
 8001298:	683a      	ldr	r2, [r7, #0]
 800129a:	6812      	ldr	r2, [r2, #0]
 800129c:	0691      	lsls	r1, r2, #26
 800129e:	6e3a      	ldr	r2, [r7, #96]	@ 0x60
 80012a0:	430a      	orrs	r2, r1
 80012a2:	431a      	orrs	r2, r3
 80012a4:	687b      	ldr	r3, [r7, #4]
 80012a6:	681b      	ldr	r3, [r3, #0]
 80012a8:	f042 4200 	orr.w	r2, r2, #2147483648	@ 0x80000000
 80012ac:	66da      	str	r2, [r3, #108]	@ 0x6c
                 ADC_OFR4_OFFSET4_CH |
                 ADC_OFR4_OFFSET4                   ,
                 ADC_OFR4_OFFSET4_EN               |
                 ADC_OFR_CHANNEL(sConfig->Channel) |
                 tmpOffsetShifted                    );
      break;
 80012ae:	e048      	b.n	8001342 <HAL_ADC_ConfigChannel+0x31e>
    
    /* Case ADC_OFFSET_NONE */
    default :
    /* Scan OFR1, OFR2, OFR3, OFR4 to check if the selected channel is        */
    /* enabled. If this is the case, offset OFRx is disabled.                 */
      if (((hadc->Instance->OFR1) & ADC_OFR1_OFFSET1_CH) == ADC_OFR_CHANNEL(sConfig->Channel))
 80012b0:	687b      	ldr	r3, [r7, #4]
 80012b2:	681b      	ldr	r3, [r3, #0]
 80012b4:	6e1b      	ldr	r3, [r3, #96]	@ 0x60
 80012b6:	f003 42f8 	and.w	r2, r3, #2080374784	@ 0x7c000000
 80012ba:	683b      	ldr	r3, [r7, #0]
 80012bc:	681b      	ldr	r3, [r3, #0]
 80012be:	069b      	lsls	r3, r3, #26
 80012c0:	429a      	cmp	r2, r3
 80012c2:	d107      	bne.n	80012d4 <HAL_ADC_ConfigChannel+0x2b0>
      {
        /* Disable offset OFR1*/
        CLEAR_BIT(hadc->Instance->OFR1, ADC_OFR1_OFFSET1_EN);
 80012c4:	687b      	ldr	r3, [r7, #4]
 80012c6:	681b      	ldr	r3, [r3, #0]
 80012c8:	6e1a      	ldr	r2, [r3, #96]	@ 0x60
 80012ca:	687b      	ldr	r3, [r7, #4]
 80012cc:	681b      	ldr	r3, [r3, #0]
 80012ce:	f022 4200 	bic.w	r2, r2, #2147483648	@ 0x80000000
 80012d2:	661a      	str	r2, [r3, #96]	@ 0x60
      }
      if (((hadc->Instance->OFR2) & ADC_OFR2_OFFSET2_CH) == ADC_OFR_CHANNEL(sConfig->Channel))
 80012d4:	687b      	ldr	r3, [r7, #4]
 80012d6:	681b      	ldr	r3, [r3, #0]
 80012d8:	6e5b      	ldr	r3, [r3, #100]	@ 0x64
 80012da:	f003 42f8 	and.w	r2, r3, #2080374784	@ 0x7c000000
 80012de:	683b      	ldr	r3, [r7, #0]
 80012e0:	681b      	ldr	r3, [r3, #0]
 80012e2:	069b      	lsls	r3, r3, #26
 80012e4:	429a      	cmp	r2, r3
 80012e6:	d107      	bne.n	80012f8 <HAL_ADC_ConfigChannel+0x2d4>
      {
        /* Disable offset OFR2*/
        CLEAR_BIT(hadc->Instance->OFR2, ADC_OFR2_OFFSET2_EN); 
 80012e8:	687b      	ldr	r3, [r7, #4]
 80012ea:	681b      	ldr	r3, [r3, #0]
 80012ec:	6e5a      	ldr	r2, [r3, #100]	@ 0x64
 80012ee:	687b      	ldr	r3, [r7, #4]
 80012f0:	681b      	ldr	r3, [r3, #0]
 80012f2:	f022 4200 	bic.w	r2, r2, #2147483648	@ 0x80000000
 80012f6:	665a      	str	r2, [r3, #100]	@ 0x64
      }
      if (((hadc->Instance->OFR3) & ADC_OFR3_OFFSET3_CH) == ADC_OFR_CHANNEL(sConfig->Channel))
 80012f8:	687b      	ldr	r3, [r7, #4]
 80012fa:	681b      	ldr	r3, [r3, #0]
 80012fc:	6e9b      	ldr	r3, [r3, #104]	@ 0x68
 80012fe:	f003 42f8 	and.w	r2, r3, #2080374784	@ 0x7c000000
 8001302:	683b      	ldr	r3, [r7, #0]
 8001304:	681b      	ldr	r3, [r3, #0]
 8001306:	069b      	lsls	r3, r3, #26
 8001308:	429a      	cmp	r2, r3
 800130a:	d107      	bne.n	800131c <HAL_ADC_ConfigChannel+0x2f8>
      {
        /* Disable offset OFR3*/
        CLEAR_BIT(hadc->Instance->OFR3, ADC_OFR3_OFFSET3_EN);
 800130c:	687b      	ldr	r3, [r7, #4]
 800130e:	681b      	ldr	r3, [r3, #0]
 8001310:	6e9a      	ldr	r2, [r3, #104]	@ 0x68
 8001312:	687b      	ldr	r3, [r7, #4]
 8001314:	681b      	ldr	r3, [r3, #0]
 8001316:	f022 4200 	bic.w	r2, r2, #2147483648	@ 0x80000000
 800131a:	669a      	str	r2, [r3, #104]	@ 0x68
      }
      if (((hadc->Instance->OFR4) & ADC_OFR4_OFFSET4_CH) == ADC_OFR_CHANNEL(sConfig->Channel))
 800131c:	687b      	ldr	r3, [r7, #4]
 800131e:	681b      	ldr	r3, [r3, #0]
 8001320:	6edb      	ldr	r3, [r3, #108]	@ 0x6c
 8001322:	f003 42f8 	and.w	r2, r3, #2080374784	@ 0x7c000000
 8001326:	683b      	ldr	r3, [r7, #0]
 8001328:	681b      	ldr	r3, [r3, #0]
 800132a:	069b      	lsls	r3, r3, #26
 800132c:	429a      	cmp	r2, r3
 800132e:	d107      	bne.n	8001340 <HAL_ADC_ConfigChannel+0x31c>
      {
        /* Disable offset OFR4*/
        CLEAR_BIT(hadc->Instance->OFR4, ADC_OFR4_OFFSET4_EN);
 8001330:	687b      	ldr	r3, [r7, #4]
 8001332:	681b      	ldr	r3, [r3, #0]
 8001334:	6eda      	ldr	r2, [r3, #108]	@ 0x6c
 8001336:	687b      	ldr	r3, [r7, #4]
 8001338:	681b      	ldr	r3, [r3, #0]
 800133a:	f022 4200 	bic.w	r2, r2, #2147483648	@ 0x80000000
 800133e:	66da      	str	r2, [r3, #108]	@ 0x6c
      }
      break;
 8001340:	bf00      	nop

  /* Parameters update conditioned to ADC state:                              */
  /* Parameters that can be updated only when ADC is disabled:                */
  /*  - Single or differential mode                                           */
  /*  - Internal measurement channels: Vbat/VrefInt/TempSensor                */
  if (ADC_IS_ENABLE(hadc) == RESET)
 8001342:	687b      	ldr	r3, [r7, #4]
 8001344:	681b      	ldr	r3, [r3, #0]
 8001346:	689b      	ldr	r3, [r3, #8]
 8001348:	f003 0303 	and.w	r3, r3, #3
 800134c:	2b01      	cmp	r3, #1
 800134e:	d108      	bne.n	8001362 <HAL_ADC_ConfigChannel+0x33e>
 8001350:	687b      	ldr	r3, [r7, #4]
 8001352:	681b      	ldr	r3, [r3, #0]
 8001354:	681b      	ldr	r3, [r3, #0]
 8001356:	f003 0301 	and.w	r3, r3, #1
 800135a:	2b01      	cmp	r3, #1
 800135c:	d101      	bne.n	8001362 <HAL_ADC_ConfigChannel+0x33e>
 800135e:	2301      	movs	r3, #1
 8001360:	e000      	b.n	8001364 <HAL_ADC_ConfigChannel+0x340>
 8001362:	2300      	movs	r3, #0
 8001364:	2b00      	cmp	r3, #0
 8001366:	f040 8131 	bne.w	80015cc <HAL_ADC_ConfigChannel+0x5a8>
  {
    /* Configuration of differential mode */
    if (sConfig->SingleDiff != ADC_DIFFERENTIAL_ENDED)
 800136a:	683b      	ldr	r3, [r7, #0]
 800136c:	68db      	ldr	r3, [r3, #12]
 800136e:	2b01      	cmp	r3, #1
 8001370:	d00f      	beq.n	8001392 <HAL_ADC_ConfigChannel+0x36e>
    {
      /* Disable differential mode (default mode: single-ended) */
      CLEAR_BIT(hadc->Instance->DIFSEL, ADC_DIFSEL_CHANNEL(sConfig->Channel));
 8001372:	687b      	ldr	r3, [r7, #4]
 8001374:	681b      	ldr	r3, [r3, #0]
 8001376:	f8d3 10b0 	ldr.w	r1, [r3, #176]	@ 0xb0
 800137a:	683b      	ldr	r3, [r7, #0]
 800137c:	681b      	ldr	r3, [r3, #0]
 800137e:	2201      	movs	r2, #1
 8001380:	fa02 f303 	lsl.w	r3, r2, r3
 8001384:	43da      	mvns	r2, r3
 8001386:	687b      	ldr	r3, [r7, #4]
 8001388:	681b      	ldr	r3, [r3, #0]
 800138a:	400a      	ands	r2, r1
 800138c:	f8c3 20b0 	str.w	r2, [r3, #176]	@ 0xb0
 8001390:	e049      	b.n	8001426 <HAL_ADC_ConfigChannel+0x402>
    }
    else
    {
      /* Enable differential mode */
      SET_BIT(hadc->Instance->DIFSEL, ADC_DIFSEL_CHANNEL(sConfig->Channel));
 8001392:	687b      	ldr	r3, [r7, #4]
 8001394:	681b      	ldr	r3, [r3, #0]
 8001396:	f8d3 10b0 	ldr.w	r1, [r3, #176]	@ 0xb0
 800139a:	683b      	ldr	r3, [r7, #0]
 800139c:	681b      	ldr	r3, [r3, #0]
 800139e:	2201      	movs	r2, #1
 80013a0:	409a      	lsls	r2, r3
 80013a2:	687b      	ldr	r3, [r7, #4]
 80013a4:	681b      	ldr	r3, [r3, #0]
 80013a6:	430a      	orrs	r2, r1
 80013a8:	f8c3 20b0 	str.w	r2, [r3, #176]	@ 0xb0
      
      /* Channel sampling time configuration (channel ADC_INx +1              */
      /* corresponding to differential negative input).                       */
      /* For channels 10 to 18U */
      if (sConfig->Channel >= ADC_CHANNEL_10)
 80013ac:	683b      	ldr	r3, [r7, #0]
 80013ae:	681b      	ldr	r3, [r3, #0]
 80013b0:	2b09      	cmp	r3, #9
 80013b2:	d91c      	bls.n	80013ee <HAL_ADC_ConfigChannel+0x3ca>
      {
        MODIFY_REG(hadc->Instance->SMPR2,
 80013b4:	687b      	ldr	r3, [r7, #4]
 80013b6:	681b      	ldr	r3, [r3, #0]
 80013b8:	6999      	ldr	r1, [r3, #24]
 80013ba:	683b      	ldr	r3, [r7, #0]
 80013bc:	681a      	ldr	r2, [r3, #0]
 80013be:	4613      	mov	r3, r2
 80013c0:	005b      	lsls	r3, r3, #1
 80013c2:	4413      	add	r3, r2
 80013c4:	3b1b      	subs	r3, #27
 80013c6:	2207      	movs	r2, #7
 80013c8:	fa02 f303 	lsl.w	r3, r2, r3
 80013cc:	43db      	mvns	r3, r3
 80013ce:	4019      	ands	r1, r3
 80013d0:	683b      	ldr	r3, [r7, #0]
 80013d2:	6898      	ldr	r0, [r3, #8]
 80013d4:	683b      	ldr	r3, [r7, #0]
 80013d6:	681a      	ldr	r2, [r3, #0]
 80013d8:	4613      	mov	r3, r2
 80013da:	005b      	lsls	r3, r3, #1
 80013dc:	4413      	add	r3, r2
 80013de:	3b1b      	subs	r3, #27
 80013e0:	fa00 f203 	lsl.w	r2, r0, r3
 80013e4:	687b      	ldr	r3, [r7, #4]
 80013e6:	681b      	ldr	r3, [r3, #0]
 80013e8:	430a      	orrs	r2, r1
 80013ea:	619a      	str	r2, [r3, #24]
 80013ec:	e01b      	b.n	8001426 <HAL_ADC_ConfigChannel+0x402>
                   ADC_SMPR2(ADC_SMPR2_SMP10, sConfig->Channel +1U)      ,
                   ADC_SMPR2(sConfig->SamplingTime, sConfig->Channel +1U) );
      }
      else /* For channels 1 to 9U */
      {
        MODIFY_REG(hadc->Instance->SMPR1,
 80013ee:	687b      	ldr	r3, [r7, #4]
 80013f0:	681b      	ldr	r3, [r3, #0]
 80013f2:	6959      	ldr	r1, [r3, #20]
 80013f4:	683b      	ldr	r3, [r7, #0]
 80013f6:	681b      	ldr	r3, [r3, #0]
 80013f8:	1c5a      	adds	r2, r3, #1
 80013fa:	4613      	mov	r3, r2
 80013fc:	005b      	lsls	r3, r3, #1
 80013fe:	4413      	add	r3, r2
 8001400:	2207      	movs	r2, #7
 8001402:	fa02 f303 	lsl.w	r3, r2, r3
 8001406:	43db      	mvns	r3, r3
 8001408:	4019      	ands	r1, r3
 800140a:	683b      	ldr	r3, [r7, #0]
 800140c:	6898      	ldr	r0, [r3, #8]
 800140e:	683b      	ldr	r3, [r7, #0]
 8001410:	681b      	ldr	r3, [r3, #0]
 8001412:	1c5a      	adds	r2, r3, #1
 8001414:	4613      	mov	r3, r2
 8001416:	005b      	lsls	r3, r3, #1
 8001418:	4413      	add	r3, r2
 800141a:	fa00 f203 	lsl.w	r2, r0, r3
 800141e:	687b      	ldr	r3, [r7, #4]
 8001420:	681b      	ldr	r3, [r3, #0]
 8001422:	430a      	orrs	r2, r1
 8001424:	615a      	str	r2, [r3, #20]
       
    /* Configuration of common ADC parameters                                 */
    /* Pointer to the common control register to which is belonging hadc      */
    /* (Depending on STM32F3 product, there may be up to 4 ADC and 2 common   */
    /* control registers)                                                     */
    tmpADC_Common = ADC_COMMON_REGISTER(hadc);
 8001426:	687b      	ldr	r3, [r7, #4]
 8001428:	681b      	ldr	r3, [r3, #0]
 800142a:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 800142e:	d004      	beq.n	800143a <HAL_ADC_ConfigChannel+0x416>
 8001430:	687b      	ldr	r3, [r7, #4]
 8001432:	681b      	ldr	r3, [r3, #0]
 8001434:	4a18      	ldr	r2, [pc, #96]	@ (8001498 <HAL_ADC_ConfigChannel+0x474>)
 8001436:	4293      	cmp	r3, r2
 8001438:	d101      	bne.n	800143e <HAL_ADC_ConfigChannel+0x41a>
 800143a:	4b18      	ldr	r3, [pc, #96]	@ (800149c <HAL_ADC_ConfigChannel+0x478>)
 800143c:	e000      	b.n	8001440 <HAL_ADC_ConfigChannel+0x41c>
 800143e:	4b18      	ldr	r3, [pc, #96]	@ (80014a0 <HAL_ADC_ConfigChannel+0x47c>)
 8001440:	65fb      	str	r3, [r7, #92]	@ 0x5c
  
    /* If the requested internal measurement path has already been enabled,   */
    /* bypass the configuration processing.                                   */
    if (( (sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) &&
 8001442:	683b      	ldr	r3, [r7, #0]
 8001444:	681b      	ldr	r3, [r3, #0]
 8001446:	2b10      	cmp	r3, #16
 8001448:	d105      	bne.n	8001456 <HAL_ADC_ConfigChannel+0x432>
          (HAL_IS_BIT_CLR(tmpADC_Common->CCR, ADC_CCR_TSEN))            ) ||
 800144a:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 800144c:	689b      	ldr	r3, [r3, #8]
 800144e:	f403 0300 	and.w	r3, r3, #8388608	@ 0x800000
    if (( (sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) &&
 8001452:	2b00      	cmp	r3, #0
 8001454:	d015      	beq.n	8001482 <HAL_ADC_ConfigChannel+0x45e>
        ( (sConfig->Channel == ADC_CHANNEL_VBAT)       &&
 8001456:	683b      	ldr	r3, [r7, #0]
 8001458:	681b      	ldr	r3, [r3, #0]
          (HAL_IS_BIT_CLR(tmpADC_Common->CCR, ADC_CCR_TSEN))            ) ||
 800145a:	2b11      	cmp	r3, #17
 800145c:	d105      	bne.n	800146a <HAL_ADC_ConfigChannel+0x446>
          (HAL_IS_BIT_CLR(tmpADC_Common->CCR, ADC_CCR_VBATEN))          ) ||
 800145e:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8001460:	689b      	ldr	r3, [r3, #8]
 8001462:	f003 7380 	and.w	r3, r3, #16777216	@ 0x1000000
        ( (sConfig->Channel == ADC_CHANNEL_VBAT)       &&
 8001466:	2b00      	cmp	r3, #0
 8001468:	d00b      	beq.n	8001482 <HAL_ADC_ConfigChannel+0x45e>
        ( (sConfig->Channel == ADC_CHANNEL_VREFINT)    &&
 800146a:	683b      	ldr	r3, [r7, #0]
 800146c:	681b      	ldr	r3, [r3, #0]
          (HAL_IS_BIT_CLR(tmpADC_Common->CCR, ADC_CCR_VBATEN))          ) ||
 800146e:	2b12      	cmp	r3, #18
 8001470:	f040 80ac 	bne.w	80015cc <HAL_ADC_ConfigChannel+0x5a8>
          (HAL_IS_BIT_CLR(tmpADC_Common->CCR, ADC_CCR_VREFEN)))
 8001474:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8001476:	689b      	ldr	r3, [r3, #8]
 8001478:	f403 0380 	and.w	r3, r3, #4194304	@ 0x400000
        ( (sConfig->Channel == ADC_CHANNEL_VREFINT)    &&
 800147c:	2b00      	cmp	r3, #0
 800147e:	f040 80a5 	bne.w	80015cc <HAL_ADC_ConfigChannel+0x5a8>
       )
    {
      /* Configuration of common ADC parameters (continuation)                */
      /* Set handle of the other ADC sharing the same common register         */
      ADC_COMMON_ADC_OTHER(hadc, &tmphadcSharingSameCommonRegister);
 8001482:	687b      	ldr	r3, [r7, #4]
 8001484:	681b      	ldr	r3, [r3, #0]
 8001486:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 800148a:	d10b      	bne.n	80014a4 <HAL_ADC_ConfigChannel+0x480>
 800148c:	4b02      	ldr	r3, [pc, #8]	@ (8001498 <HAL_ADC_ConfigChannel+0x474>)
 800148e:	60fb      	str	r3, [r7, #12]
 8001490:	e023      	b.n	80014da <HAL_ADC_ConfigChannel+0x4b6>
 8001492:	bf00      	nop
 8001494:	83fff000 	.word	0x83fff000
 8001498:	50000100 	.word	0x50000100
 800149c:	50000300 	.word	0x50000300
 80014a0:	50000700 	.word	0x50000700
 80014a4:	687b      	ldr	r3, [r7, #4]
 80014a6:	681b      	ldr	r3, [r3, #0]
 80014a8:	4a4e      	ldr	r2, [pc, #312]	@ (80015e4 <HAL_ADC_ConfigChannel+0x5c0>)
 80014aa:	4293      	cmp	r3, r2
 80014ac:	d103      	bne.n	80014b6 <HAL_ADC_ConfigChannel+0x492>
 80014ae:	f04f 43a0 	mov.w	r3, #1342177280	@ 0x50000000
 80014b2:	60fb      	str	r3, [r7, #12]
 80014b4:	e011      	b.n	80014da <HAL_ADC_ConfigChannel+0x4b6>
 80014b6:	687b      	ldr	r3, [r7, #4]
 80014b8:	681b      	ldr	r3, [r3, #0]
 80014ba:	4a4b      	ldr	r2, [pc, #300]	@ (80015e8 <HAL_ADC_ConfigChannel+0x5c4>)
 80014bc:	4293      	cmp	r3, r2
 80014be:	d102      	bne.n	80014c6 <HAL_ADC_ConfigChannel+0x4a2>
 80014c0:	4b4a      	ldr	r3, [pc, #296]	@ (80015ec <HAL_ADC_ConfigChannel+0x5c8>)
 80014c2:	60fb      	str	r3, [r7, #12]
 80014c4:	e009      	b.n	80014da <HAL_ADC_ConfigChannel+0x4b6>
 80014c6:	687b      	ldr	r3, [r7, #4]
 80014c8:	681b      	ldr	r3, [r3, #0]
 80014ca:	4a48      	ldr	r2, [pc, #288]	@ (80015ec <HAL_ADC_ConfigChannel+0x5c8>)
 80014cc:	4293      	cmp	r3, r2
 80014ce:	d102      	bne.n	80014d6 <HAL_ADC_ConfigChannel+0x4b2>
 80014d0:	4b45      	ldr	r3, [pc, #276]	@ (80015e8 <HAL_ADC_ConfigChannel+0x5c4>)
 80014d2:	60fb      	str	r3, [r7, #12]
 80014d4:	e001      	b.n	80014da <HAL_ADC_ConfigChannel+0x4b6>
 80014d6:	2300      	movs	r3, #0
 80014d8:	60fb      	str	r3, [r7, #12]
      
      /* Software is allowed to change common parameters only when all ADCs   */
      /* of the common group are disabled.                                    */
      if ((ADC_IS_ENABLE(hadc) == RESET)                                    &&
 80014da:	687b      	ldr	r3, [r7, #4]
 80014dc:	681b      	ldr	r3, [r3, #0]
 80014de:	689b      	ldr	r3, [r3, #8]
 80014e0:	f003 0303 	and.w	r3, r3, #3
 80014e4:	2b01      	cmp	r3, #1
 80014e6:	d108      	bne.n	80014fa <HAL_ADC_ConfigChannel+0x4d6>
 80014e8:	687b      	ldr	r3, [r7, #4]
 80014ea:	681b      	ldr	r3, [r3, #0]
 80014ec:	681b      	ldr	r3, [r3, #0]
 80014ee:	f003 0301 	and.w	r3, r3, #1
 80014f2:	2b01      	cmp	r3, #1
 80014f4:	d101      	bne.n	80014fa <HAL_ADC_ConfigChannel+0x4d6>
 80014f6:	2301      	movs	r3, #1
 80014f8:	e000      	b.n	80014fc <HAL_ADC_ConfigChannel+0x4d8>
 80014fa:	2300      	movs	r3, #0
 80014fc:	2b00      	cmp	r3, #0
 80014fe:	d150      	bne.n	80015a2 <HAL_ADC_ConfigChannel+0x57e>
          ( (tmphadcSharingSameCommonRegister.Instance == NULL)         ||
 8001500:	68fb      	ldr	r3, [r7, #12]
      if ((ADC_IS_ENABLE(hadc) == RESET)                                    &&
 8001502:	2b00      	cmp	r3, #0
 8001504:	d010      	beq.n	8001528 <HAL_ADC_ConfigChannel+0x504>
            (ADC_IS_ENABLE(&tmphadcSharingSameCommonRegister) == RESET)   )   )
 8001506:	68fb      	ldr	r3, [r7, #12]
 8001508:	689b      	ldr	r3, [r3, #8]
 800150a:	f003 0303 	and.w	r3, r3, #3
 800150e:	2b01      	cmp	r3, #1
 8001510:	d107      	bne.n	8001522 <HAL_ADC_ConfigChannel+0x4fe>
 8001512:	68fb      	ldr	r3, [r7, #12]
 8001514:	681b      	ldr	r3, [r3, #0]
 8001516:	f003 0301 	and.w	r3, r3, #1
 800151a:	2b01      	cmp	r3, #1
 800151c:	d101      	bne.n	8001522 <HAL_ADC_ConfigChannel+0x4fe>
 800151e:	2301      	movs	r3, #1
 8001520:	e000      	b.n	8001524 <HAL_ADC_ConfigChannel+0x500>
 8001522:	2300      	movs	r3, #0
          ( (tmphadcSharingSameCommonRegister.Instance == NULL)         ||
 8001524:	2b00      	cmp	r3, #0
 8001526:	d13c      	bne.n	80015a2 <HAL_ADC_ConfigChannel+0x57e>
      {
        /* If Channel_16 is selected, enable Temp. sensor measurement path    */
        /* Note: Temp. sensor internal channels available on ADC1 only        */
        if ((sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) && (hadc->Instance == ADC1))
 8001528:	683b      	ldr	r3, [r7, #0]
 800152a:	681b      	ldr	r3, [r3, #0]
 800152c:	2b10      	cmp	r3, #16
 800152e:	d11d      	bne.n	800156c <HAL_ADC_ConfigChannel+0x548>
 8001530:	687b      	ldr	r3, [r7, #4]
 8001532:	681b      	ldr	r3, [r3, #0]
 8001534:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8001538:	d118      	bne.n	800156c <HAL_ADC_ConfigChannel+0x548>
        {
          SET_BIT(tmpADC_Common->CCR, ADC_CCR_TSEN);
 800153a:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 800153c:	689b      	ldr	r3, [r3, #8]
 800153e:	f443 0200 	orr.w	r2, r3, #8388608	@ 0x800000
 8001542:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8001544:	609a      	str	r2, [r3, #8]
          
          /* Delay for temperature sensor stabilization time */
          /* Compute number of CPU cycles to wait for */
          wait_loop_index = (ADC_TEMPSENSOR_DELAY_US * (SystemCoreClock / 1000000U));
 8001546:	4b2a      	ldr	r3, [pc, #168]	@ (80015f0 <HAL_ADC_ConfigChannel+0x5cc>)
 8001548:	681b      	ldr	r3, [r3, #0]
 800154a:	4a2a      	ldr	r2, [pc, #168]	@ (80015f4 <HAL_ADC_ConfigChannel+0x5d0>)
 800154c:	fba2 2303 	umull	r2, r3, r2, r3
 8001550:	0c9a      	lsrs	r2, r3, #18
 8001552:	4613      	mov	r3, r2
 8001554:	009b      	lsls	r3, r3, #2
 8001556:	4413      	add	r3, r2
 8001558:	005b      	lsls	r3, r3, #1
 800155a:	60bb      	str	r3, [r7, #8]
          while(wait_loop_index != 0U)
 800155c:	e002      	b.n	8001564 <HAL_ADC_ConfigChannel+0x540>
          {
            wait_loop_index--;
 800155e:	68bb      	ldr	r3, [r7, #8]
 8001560:	3b01      	subs	r3, #1
 8001562:	60bb      	str	r3, [r7, #8]
          while(wait_loop_index != 0U)
 8001564:	68bb      	ldr	r3, [r7, #8]
 8001566:	2b00      	cmp	r3, #0
 8001568:	d1f9      	bne.n	800155e <HAL_ADC_ConfigChannel+0x53a>
        if ((sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) && (hadc->Instance == ADC1))
 800156a:	e02e      	b.n	80015ca <HAL_ADC_ConfigChannel+0x5a6>
          }
        }
        /* If Channel_17 is selected, enable VBAT measurement path            */
        /* Note: VBAT internal channels available on ADC1 only                */
        else if ((sConfig->Channel == ADC_CHANNEL_VBAT) && (hadc->Instance == ADC1))
 800156c:	683b      	ldr	r3, [r7, #0]
 800156e:	681b      	ldr	r3, [r3, #0]
 8001570:	2b11      	cmp	r3, #17
 8001572:	d10b      	bne.n	800158c <HAL_ADC_ConfigChannel+0x568>
 8001574:	687b      	ldr	r3, [r7, #4]
 8001576:	681b      	ldr	r3, [r3, #0]
 8001578:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 800157c:	d106      	bne.n	800158c <HAL_ADC_ConfigChannel+0x568>
        {
          SET_BIT(tmpADC_Common->CCR, ADC_CCR_VBATEN);
 800157e:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8001580:	689b      	ldr	r3, [r3, #8]
 8001582:	f043 7280 	orr.w	r2, r3, #16777216	@ 0x1000000
 8001586:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8001588:	609a      	str	r2, [r3, #8]
        if ((sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) && (hadc->Instance == ADC1))
 800158a:	e01e      	b.n	80015ca <HAL_ADC_ConfigChannel+0x5a6>
        }
        /* If Channel_18 is selected, enable VREFINT measurement path         */
        /* Note: VrefInt internal channels available on all ADCs, but only    */
        /*       one ADC is allowed to be connected to VrefInt at the same    */
        /*       time.                                                        */
        else if (sConfig->Channel == ADC_CHANNEL_VREFINT)
 800158c:	683b      	ldr	r3, [r7, #0]
 800158e:	681b      	ldr	r3, [r3, #0]
 8001590:	2b12      	cmp	r3, #18
 8001592:	d11a      	bne.n	80015ca <HAL_ADC_ConfigChannel+0x5a6>
        {
          SET_BIT(tmpADC_Common->CCR, ADC_CCR_VREFEN);
 8001594:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 8001596:	689b      	ldr	r3, [r3, #8]
 8001598:	f443 0280 	orr.w	r2, r3, #4194304	@ 0x400000
 800159c:	6dfb      	ldr	r3, [r7, #92]	@ 0x5c
 800159e:	609a      	str	r2, [r3, #8]
        if ((sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) && (hadc->Instance == ADC1))
 80015a0:	e013      	b.n	80015ca <HAL_ADC_ConfigChannel+0x5a6>
      /* enabled and other ADC of the common group are enabled, internal      */
      /* measurement paths cannot be enabled.                                 */
      else  
      {
        /* Update ADC state machine to error */
        SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_CONFIG);
 80015a2:	687b      	ldr	r3, [r7, #4]
 80015a4:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 80015a6:	f043 0220 	orr.w	r2, r3, #32
 80015aa:	687b      	ldr	r3, [r7, #4]
 80015ac:	641a      	str	r2, [r3, #64]	@ 0x40
        
        tmp_hal_status = HAL_ERROR;
 80015ae:	2301      	movs	r3, #1
 80015b0:	f887 3067 	strb.w	r3, [r7, #103]	@ 0x67
 80015b4:	e00a      	b.n	80015cc <HAL_ADC_ConfigChannel+0x5a8>
  /* channel could be done on neither of the channel configuration structure  */
  /* parameters.                                                              */
  else
  {
    /* Update ADC state machine to error */
    SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_CONFIG);
 80015b6:	687b      	ldr	r3, [r7, #4]
 80015b8:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 80015ba:	f043 0220 	orr.w	r2, r3, #32
 80015be:	687b      	ldr	r3, [r7, #4]
 80015c0:	641a      	str	r2, [r3, #64]	@ 0x40
    
    tmp_hal_status = HAL_ERROR;
 80015c2:	2301      	movs	r3, #1
 80015c4:	f887 3067 	strb.w	r3, [r7, #103]	@ 0x67
 80015c8:	e000      	b.n	80015cc <HAL_ADC_ConfigChannel+0x5a8>
        if ((sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) && (hadc->Instance == ADC1))
 80015ca:	bf00      	nop
  }
  
  /* Process unlocked */
  __HAL_UNLOCK(hadc);
 80015cc:	687b      	ldr	r3, [r7, #4]
 80015ce:	2200      	movs	r2, #0
 80015d0:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
  
  /* Return function status */
  return tmp_hal_status;
 80015d4:	f897 3067 	ldrb.w	r3, [r7, #103]	@ 0x67
}
 80015d8:	4618      	mov	r0, r3
 80015da:	376c      	adds	r7, #108	@ 0x6c
 80015dc:	46bd      	mov	sp, r7
 80015de:	f85d 7b04 	ldr.w	r7, [sp], #4
 80015e2:	4770      	bx	lr
 80015e4:	50000100 	.word	0x50000100
 80015e8:	50000400 	.word	0x50000400
 80015ec:	50000500 	.word	0x50000500
 80015f0:	20000000 	.word	0x20000000
 80015f4:	431bde83 	.word	0x431bde83

080015f8 <HAL_ADCEx_MultiModeConfigChannel>:
  * @param  hadc ADC handle
  * @param  multimode Structure of ADC multimode configuration
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_ADCEx_MultiModeConfigChannel(ADC_HandleTypeDef* hadc, ADC_MultiModeTypeDef* multimode)
{
 80015f8:	b480      	push	{r7}
 80015fa:	b099      	sub	sp, #100	@ 0x64
 80015fc:	af00      	add	r7, sp, #0
 80015fe:	6078      	str	r0, [r7, #4]
 8001600:	6039      	str	r1, [r7, #0]
  HAL_StatusTypeDef tmp_hal_status = HAL_OK;
 8001602:	2300      	movs	r3, #0
 8001604:	f887 305f 	strb.w	r3, [r7, #95]	@ 0x5f
    assert_param(IS_ADC_DMA_ACCESS_MODE(multimode->DMAAccessMode));
    assert_param(IS_ADC_SAMPLING_DELAY(multimode->TwoSamplingDelay));
  }
  
  /* Set handle of the other ADC sharing the same common register             */
  ADC_COMMON_ADC_OTHER(hadc, &tmphadcSharingSameCommonRegister);
 8001608:	687b      	ldr	r3, [r7, #4]
 800160a:	681b      	ldr	r3, [r3, #0]
 800160c:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8001610:	d102      	bne.n	8001618 <HAL_ADCEx_MultiModeConfigChannel+0x20>
 8001612:	4b6d      	ldr	r3, [pc, #436]	@ (80017c8 <HAL_ADCEx_MultiModeConfigChannel+0x1d0>)
 8001614:	60bb      	str	r3, [r7, #8]
 8001616:	e01a      	b.n	800164e <HAL_ADCEx_MultiModeConfigChannel+0x56>
 8001618:	687b      	ldr	r3, [r7, #4]
 800161a:	681b      	ldr	r3, [r3, #0]
 800161c:	4a6a      	ldr	r2, [pc, #424]	@ (80017c8 <HAL_ADCEx_MultiModeConfigChannel+0x1d0>)
 800161e:	4293      	cmp	r3, r2
 8001620:	d103      	bne.n	800162a <HAL_ADCEx_MultiModeConfigChannel+0x32>
 8001622:	f04f 43a0 	mov.w	r3, #1342177280	@ 0x50000000
 8001626:	60bb      	str	r3, [r7, #8]
 8001628:	e011      	b.n	800164e <HAL_ADCEx_MultiModeConfigChannel+0x56>
 800162a:	687b      	ldr	r3, [r7, #4]
 800162c:	681b      	ldr	r3, [r3, #0]
 800162e:	4a67      	ldr	r2, [pc, #412]	@ (80017cc <HAL_ADCEx_MultiModeConfigChannel+0x1d4>)
 8001630:	4293      	cmp	r3, r2
 8001632:	d102      	bne.n	800163a <HAL_ADCEx_MultiModeConfigChannel+0x42>
 8001634:	4b66      	ldr	r3, [pc, #408]	@ (80017d0 <HAL_ADCEx_MultiModeConfigChannel+0x1d8>)
 8001636:	60bb      	str	r3, [r7, #8]
 8001638:	e009      	b.n	800164e <HAL_ADCEx_MultiModeConfigChannel+0x56>
 800163a:	687b      	ldr	r3, [r7, #4]
 800163c:	681b      	ldr	r3, [r3, #0]
 800163e:	4a64      	ldr	r2, [pc, #400]	@ (80017d0 <HAL_ADCEx_MultiModeConfigChannel+0x1d8>)
 8001640:	4293      	cmp	r3, r2
 8001642:	d102      	bne.n	800164a <HAL_ADCEx_MultiModeConfigChannel+0x52>
 8001644:	4b61      	ldr	r3, [pc, #388]	@ (80017cc <HAL_ADCEx_MultiModeConfigChannel+0x1d4>)
 8001646:	60bb      	str	r3, [r7, #8]
 8001648:	e001      	b.n	800164e <HAL_ADCEx_MultiModeConfigChannel+0x56>
 800164a:	2300      	movs	r3, #0
 800164c:	60bb      	str	r3, [r7, #8]
  if (tmphadcSharingSameCommonRegister.Instance == NULL)
 800164e:	68bb      	ldr	r3, [r7, #8]
 8001650:	2b00      	cmp	r3, #0
 8001652:	d101      	bne.n	8001658 <HAL_ADCEx_MultiModeConfigChannel+0x60>
  {
    /* Return function status */
    return HAL_ERROR;
 8001654:	2301      	movs	r3, #1
 8001656:	e0b0      	b.n	80017ba <HAL_ADCEx_MultiModeConfigChannel+0x1c2>
  }

  /* Process locked */
  __HAL_LOCK(hadc);
 8001658:	687b      	ldr	r3, [r7, #4]
 800165a:	f893 303c 	ldrb.w	r3, [r3, #60]	@ 0x3c
 800165e:	2b01      	cmp	r3, #1
 8001660:	d101      	bne.n	8001666 <HAL_ADCEx_MultiModeConfigChannel+0x6e>
 8001662:	2302      	movs	r3, #2
 8001664:	e0a9      	b.n	80017ba <HAL_ADCEx_MultiModeConfigChannel+0x1c2>
 8001666:	687b      	ldr	r3, [r7, #4]
 8001668:	2201      	movs	r2, #1
 800166a:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
  /* Parameters update conditioned to ADC state:                              */
  /* Parameters that can be updated when ADC is disabled or enabled without   */
  /* conversion on going on regular group:                                    */
  /*  - Multimode DMA configuration                                           */
  /*  - Multimode DMA mode                                                    */
  if ( (ADC_IS_CONVERSION_ONGOING_REGULAR(hadc) == RESET) 
 800166e:	687b      	ldr	r3, [r7, #4]
 8001670:	681b      	ldr	r3, [r3, #0]
 8001672:	689b      	ldr	r3, [r3, #8]
 8001674:	f003 0304 	and.w	r3, r3, #4
 8001678:	2b00      	cmp	r3, #0
 800167a:	f040 808d 	bne.w	8001798 <HAL_ADCEx_MultiModeConfigChannel+0x1a0>
    && (ADC_IS_CONVERSION_ONGOING_REGULAR(&tmphadcSharingSameCommonRegister) == RESET) )
 800167e:	68bb      	ldr	r3, [r7, #8]
 8001680:	689b      	ldr	r3, [r3, #8]
 8001682:	f003 0304 	and.w	r3, r3, #4
 8001686:	2b00      	cmp	r3, #0
 8001688:	f040 8086 	bne.w	8001798 <HAL_ADCEx_MultiModeConfigChannel+0x1a0>
  {
    /* Pointer to the common control register to which is belonging hadc      */
    /* (Depending on STM32F3 product, there may have up to 4 ADC and 2 common */
    /* control registers)                                                     */
    tmpADC_Common = ADC_COMMON_REGISTER(hadc);
 800168c:	687b      	ldr	r3, [r7, #4]
 800168e:	681b      	ldr	r3, [r3, #0]
 8001690:	f1b3 4fa0 	cmp.w	r3, #1342177280	@ 0x50000000
 8001694:	d004      	beq.n	80016a0 <HAL_ADCEx_MultiModeConfigChannel+0xa8>
 8001696:	687b      	ldr	r3, [r7, #4]
 8001698:	681b      	ldr	r3, [r3, #0]
 800169a:	4a4b      	ldr	r2, [pc, #300]	@ (80017c8 <HAL_ADCEx_MultiModeConfigChannel+0x1d0>)
 800169c:	4293      	cmp	r3, r2
 800169e:	d101      	bne.n	80016a4 <HAL_ADCEx_MultiModeConfigChannel+0xac>
 80016a0:	4b4c      	ldr	r3, [pc, #304]	@ (80017d4 <HAL_ADCEx_MultiModeConfigChannel+0x1dc>)
 80016a2:	e000      	b.n	80016a6 <HAL_ADCEx_MultiModeConfigChannel+0xae>
 80016a4:	4b4c      	ldr	r3, [pc, #304]	@ (80017d8 <HAL_ADCEx_MultiModeConfigChannel+0x1e0>)
 80016a6:	65bb      	str	r3, [r7, #88]	@ 0x58
    
    /* If multimode is selected, configure all multimode parameters.          */
    /* Otherwise, reset multimode parameters (can be used in case of          */
    /* transition from multimode to independent mode).                        */
    if(multimode->Mode != ADC_MODE_INDEPENDENT)
 80016a8:	683b      	ldr	r3, [r7, #0]
 80016aa:	681b      	ldr	r3, [r3, #0]
 80016ac:	2b00      	cmp	r3, #0
 80016ae:	d040      	beq.n	8001732 <HAL_ADCEx_MultiModeConfigChannel+0x13a>
    {
      /* Configuration of ADC common group ADC1&ADC2, ADC3&ADC4 if available    */
      /* (ADC2, ADC3, ADC4 availability depends on STM32 product)               */
      /*  - DMA access mode                                                     */
      MODIFY_REG(tmpADC_Common->CCR                                          ,
 80016b0:	6dbb      	ldr	r3, [r7, #88]	@ 0x58
 80016b2:	689b      	ldr	r3, [r3, #8]
 80016b4:	f423 4260 	bic.w	r2, r3, #57344	@ 0xe000
 80016b8:	683b      	ldr	r3, [r7, #0]
 80016ba:	6859      	ldr	r1, [r3, #4]
 80016bc:	687b      	ldr	r3, [r7, #4]
 80016be:	f893 3030 	ldrb.w	r3, [r3, #48]	@ 0x30
 80016c2:	035b      	lsls	r3, r3, #13
 80016c4:	430b      	orrs	r3, r1
 80016c6:	431a      	orrs	r2, r3
 80016c8:	6dbb      	ldr	r3, [r7, #88]	@ 0x58
 80016ca:	609a      	str	r2, [r3, #8]
      /*       parameters, their setting is bypassed without error reporting    */
      /*       (as it can be the expected behaviour in case of intended action  */
      /*       to update parameter above (which fulfills the ADC state          */
      /*       condition: no conversion on going on group regular)              */
      /*       on the fly).                                                     */
      if ((ADC_IS_ENABLE(hadc) == RESET)                              &&
 80016cc:	687b      	ldr	r3, [r7, #4]
 80016ce:	681b      	ldr	r3, [r3, #0]
 80016d0:	689b      	ldr	r3, [r3, #8]
 80016d2:	f003 0303 	and.w	r3, r3, #3
 80016d6:	2b01      	cmp	r3, #1
 80016d8:	d108      	bne.n	80016ec <HAL_ADCEx_MultiModeConfigChannel+0xf4>
 80016da:	687b      	ldr	r3, [r7, #4]
 80016dc:	681b      	ldr	r3, [r3, #0]
 80016de:	681b      	ldr	r3, [r3, #0]
 80016e0:	f003 0301 	and.w	r3, r3, #1
 80016e4:	2b01      	cmp	r3, #1
 80016e6:	d101      	bne.n	80016ec <HAL_ADCEx_MultiModeConfigChannel+0xf4>
 80016e8:	2301      	movs	r3, #1
 80016ea:	e000      	b.n	80016ee <HAL_ADCEx_MultiModeConfigChannel+0xf6>
 80016ec:	2300      	movs	r3, #0
 80016ee:	2b00      	cmp	r3, #0
 80016f0:	d15c      	bne.n	80017ac <HAL_ADCEx_MultiModeConfigChannel+0x1b4>
          (ADC_IS_ENABLE(&tmphadcSharingSameCommonRegister) == RESET)   )
 80016f2:	68bb      	ldr	r3, [r7, #8]
 80016f4:	689b      	ldr	r3, [r3, #8]
 80016f6:	f003 0303 	and.w	r3, r3, #3
 80016fa:	2b01      	cmp	r3, #1
 80016fc:	d107      	bne.n	800170e <HAL_ADCEx_MultiModeConfigChannel+0x116>
 80016fe:	68bb      	ldr	r3, [r7, #8]
 8001700:	681b      	ldr	r3, [r3, #0]
 8001702:	f003 0301 	and.w	r3, r3, #1
 8001706:	2b01      	cmp	r3, #1
 8001708:	d101      	bne.n	800170e <HAL_ADCEx_MultiModeConfigChannel+0x116>
 800170a:	2301      	movs	r3, #1
 800170c:	e000      	b.n	8001710 <HAL_ADCEx_MultiModeConfigChannel+0x118>
 800170e:	2300      	movs	r3, #0
      if ((ADC_IS_ENABLE(hadc) == RESET)                              &&
 8001710:	2b00      	cmp	r3, #0
 8001712:	d14b      	bne.n	80017ac <HAL_ADCEx_MultiModeConfigChannel+0x1b4>
      {
        MODIFY_REG(tmpADC_Common->CCR                                          ,
 8001714:	6dbb      	ldr	r3, [r7, #88]	@ 0x58
 8001716:	689b      	ldr	r3, [r3, #8]
 8001718:	f423 6371 	bic.w	r3, r3, #3856	@ 0xf10
 800171c:	f023 030f 	bic.w	r3, r3, #15
 8001720:	683a      	ldr	r2, [r7, #0]
 8001722:	6811      	ldr	r1, [r2, #0]
 8001724:	683a      	ldr	r2, [r7, #0]
 8001726:	6892      	ldr	r2, [r2, #8]
 8001728:	430a      	orrs	r2, r1
 800172a:	431a      	orrs	r2, r3
 800172c:	6dbb      	ldr	r3, [r7, #88]	@ 0x58
 800172e:	609a      	str	r2, [r3, #8]
    if(multimode->Mode != ADC_MODE_INDEPENDENT)
 8001730:	e03c      	b.n	80017ac <HAL_ADCEx_MultiModeConfigChannel+0x1b4>
                   multimode->TwoSamplingDelay                                  );
      }
    }
    else /* ADC_MODE_INDEPENDENT */
    {
      CLEAR_BIT(tmpADC_Common->CCR, ADC_CCR_MDMA | ADC_CCR_DMACFG);
 8001732:	6dbb      	ldr	r3, [r7, #88]	@ 0x58
 8001734:	689b      	ldr	r3, [r3, #8]
 8001736:	f423 4260 	bic.w	r2, r3, #57344	@ 0xe000
 800173a:	6dbb      	ldr	r3, [r7, #88]	@ 0x58
 800173c:	609a      	str	r2, [r3, #8]
      
      /* Parameters that can be updated only when ADC is disabled:                */
      /*  - Multimode mode selection                                              */
      /*  - Multimode delay                                                       */
      if ((ADC_IS_ENABLE(hadc) == RESET)                              &&
 800173e:	687b      	ldr	r3, [r7, #4]
 8001740:	681b      	ldr	r3, [r3, #0]
 8001742:	689b      	ldr	r3, [r3, #8]
 8001744:	f003 0303 	and.w	r3, r3, #3
 8001748:	2b01      	cmp	r3, #1
 800174a:	d108      	bne.n	800175e <HAL_ADCEx_MultiModeConfigChannel+0x166>
 800174c:	687b      	ldr	r3, [r7, #4]
 800174e:	681b      	ldr	r3, [r3, #0]
 8001750:	681b      	ldr	r3, [r3, #0]
 8001752:	f003 0301 	and.w	r3, r3, #1
 8001756:	2b01      	cmp	r3, #1
 8001758:	d101      	bne.n	800175e <HAL_ADCEx_MultiModeConfigChannel+0x166>
 800175a:	2301      	movs	r3, #1
 800175c:	e000      	b.n	8001760 <HAL_ADCEx_MultiModeConfigChannel+0x168>
 800175e:	2300      	movs	r3, #0
 8001760:	2b00      	cmp	r3, #0
 8001762:	d123      	bne.n	80017ac <HAL_ADCEx_MultiModeConfigChannel+0x1b4>
          (ADC_IS_ENABLE(&tmphadcSharingSameCommonRegister) == RESET)   )
 8001764:	68bb      	ldr	r3, [r7, #8]
 8001766:	689b      	ldr	r3, [r3, #8]
 8001768:	f003 0303 	and.w	r3, r3, #3
 800176c:	2b01      	cmp	r3, #1
 800176e:	d107      	bne.n	8001780 <HAL_ADCEx_MultiModeConfigChannel+0x188>
 8001770:	68bb      	ldr	r3, [r7, #8]
 8001772:	681b      	ldr	r3, [r3, #0]
 8001774:	f003 0301 	and.w	r3, r3, #1
 8001778:	2b01      	cmp	r3, #1
 800177a:	d101      	bne.n	8001780 <HAL_ADCEx_MultiModeConfigChannel+0x188>
 800177c:	2301      	movs	r3, #1
 800177e:	e000      	b.n	8001782 <HAL_ADCEx_MultiModeConfigChannel+0x18a>
 8001780:	2300      	movs	r3, #0
      if ((ADC_IS_ENABLE(hadc) == RESET)                              &&
 8001782:	2b00      	cmp	r3, #0
 8001784:	d112      	bne.n	80017ac <HAL_ADCEx_MultiModeConfigChannel+0x1b4>
      {
        CLEAR_BIT(tmpADC_Common->CCR, ADC_CCR_MULTI | ADC_CCR_DELAY);
 8001786:	6dbb      	ldr	r3, [r7, #88]	@ 0x58
 8001788:	689b      	ldr	r3, [r3, #8]
 800178a:	f423 6371 	bic.w	r3, r3, #3856	@ 0xf10
 800178e:	f023 030f 	bic.w	r3, r3, #15
 8001792:	6dba      	ldr	r2, [r7, #88]	@ 0x58
 8001794:	6093      	str	r3, [r2, #8]
    if(multimode->Mode != ADC_MODE_INDEPENDENT)
 8001796:	e009      	b.n	80017ac <HAL_ADCEx_MultiModeConfigChannel+0x1b4>
  /* If one of the ADC sharing the same common group is enabled, no update    */
  /* could be done on neither of the multimode structure parameters.          */
  else
  {
    /* Update ADC state machine to error */
    SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_CONFIG);
 8001798:	687b      	ldr	r3, [r7, #4]
 800179a:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 800179c:	f043 0220 	orr.w	r2, r3, #32
 80017a0:	687b      	ldr	r3, [r7, #4]
 80017a2:	641a      	str	r2, [r3, #64]	@ 0x40
    
    tmp_hal_status = HAL_ERROR;
 80017a4:	2301      	movs	r3, #1
 80017a6:	f887 305f 	strb.w	r3, [r7, #95]	@ 0x5f
 80017aa:	e000      	b.n	80017ae <HAL_ADCEx_MultiModeConfigChannel+0x1b6>
    if(multimode->Mode != ADC_MODE_INDEPENDENT)
 80017ac:	bf00      	nop
  }
    
    
  /* Process unlocked */
  __HAL_UNLOCK(hadc);
 80017ae:	687b      	ldr	r3, [r7, #4]
 80017b0:	2200      	movs	r2, #0
 80017b2:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
  
  /* Return function status */
  return tmp_hal_status;
 80017b6:	f897 305f 	ldrb.w	r3, [r7, #95]	@ 0x5f
} 
 80017ba:	4618      	mov	r0, r3
 80017bc:	3764      	adds	r7, #100	@ 0x64
 80017be:	46bd      	mov	sp, r7
 80017c0:	f85d 7b04 	ldr.w	r7, [sp], #4
 80017c4:	4770      	bx	lr
 80017c6:	bf00      	nop
 80017c8:	50000100 	.word	0x50000100
 80017cc:	50000400 	.word	0x50000400
 80017d0:	50000500 	.word	0x50000500
 80017d4:	50000300 	.word	0x50000300
 80017d8:	50000700 	.word	0x50000700

080017dc <ADC_DMAConvCplt>:
  * @brief  DMA transfer complete callback. 
  * @param  hdma pointer to DMA handle.
  * @retval None
  */
static void ADC_DMAConvCplt(DMA_HandleTypeDef *hdma)
{
 80017dc:	b580      	push	{r7, lr}
 80017de:	b084      	sub	sp, #16
 80017e0:	af00      	add	r7, sp, #0
 80017e2:	6078      	str	r0, [r7, #4]
  /* Retrieve ADC handle corresponding to current DMA handle */
  ADC_HandleTypeDef* hadc = ( ADC_HandleTypeDef* )((DMA_HandleTypeDef* )hdma)->Parent;
 80017e4:	687b      	ldr	r3, [r7, #4]
 80017e6:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 80017e8:	60fb      	str	r3, [r7, #12]
 
  /* Update state machine on conversion status if not in error state */
  if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL | HAL_ADC_STATE_ERROR_DMA))
 80017ea:	68fb      	ldr	r3, [r7, #12]
 80017ec:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 80017ee:	f003 0350 	and.w	r3, r3, #80	@ 0x50
 80017f2:	2b00      	cmp	r3, #0
 80017f4:	d126      	bne.n	8001844 <ADC_DMAConvCplt+0x68>
  {
    /* Update ADC state machine */
    SET_BIT(hadc->State, HAL_ADC_STATE_REG_EOC);
 80017f6:	68fb      	ldr	r3, [r7, #12]
 80017f8:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 80017fa:	f443 7200 	orr.w	r2, r3, #512	@ 0x200
 80017fe:	68fb      	ldr	r3, [r7, #12]
 8001800:	641a      	str	r2, [r3, #64]	@ 0x40
    /* Determine whether any further conversion upcoming on group regular     */
    /* by external trigger, continuous mode or scan sequence on going.        */
    /* Note: On STM32F3 devices, in case of sequencer enabled                 */
    /*       (several ranks selected), end of conversion flag is raised       */
    /*       at the end of the sequence.                                      */
    if(ADC_IS_SOFTWARE_START_REGULAR(hadc)        && 
 8001802:	68fb      	ldr	r3, [r7, #12]
 8001804:	681b      	ldr	r3, [r3, #0]
 8001806:	68db      	ldr	r3, [r3, #12]
 8001808:	f403 6340 	and.w	r3, r3, #3072	@ 0xc00
 800180c:	2b00      	cmp	r3, #0
 800180e:	d115      	bne.n	800183c <ADC_DMAConvCplt+0x60>
       (hadc->Init.ContinuousConvMode == DISABLE)   )
 8001810:	68fb      	ldr	r3, [r7, #12]
 8001812:	7e5b      	ldrb	r3, [r3, #25]
    if(ADC_IS_SOFTWARE_START_REGULAR(hadc)        && 
 8001814:	2b00      	cmp	r3, #0
 8001816:	d111      	bne.n	800183c <ADC_DMAConvCplt+0x60>
    {
      /* Set ADC state */
      CLEAR_BIT(hadc->State, HAL_ADC_STATE_REG_BUSY);   
 8001818:	68fb      	ldr	r3, [r7, #12]
 800181a:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 800181c:	f423 7280 	bic.w	r2, r3, #256	@ 0x100
 8001820:	68fb      	ldr	r3, [r7, #12]
 8001822:	641a      	str	r2, [r3, #64]	@ 0x40
      
      if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_INJ_BUSY))
 8001824:	68fb      	ldr	r3, [r7, #12]
 8001826:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8001828:	f403 5380 	and.w	r3, r3, #4096	@ 0x1000
 800182c:	2b00      	cmp	r3, #0
 800182e:	d105      	bne.n	800183c <ADC_DMAConvCplt+0x60>
      {
        SET_BIT(hadc->State, HAL_ADC_STATE_READY);
 8001830:	68fb      	ldr	r3, [r7, #12]
 8001832:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8001834:	f043 0201 	orr.w	r2, r3, #1
 8001838:	68fb      	ldr	r3, [r7, #12]
 800183a:	641a      	str	r2, [r3, #64]	@ 0x40
    
    /* Conversion complete callback */
#if (USE_HAL_ADC_REGISTER_CALLBACKS == 1)
      hadc->ConvCpltCallback(hadc);
#else
      HAL_ADC_ConvCpltCallback(hadc);
 800183c:	68f8      	ldr	r0, [r7, #12]
 800183e:	f7fe ff07 	bl	8000650 <HAL_ADC_ConvCpltCallback>
  else
  {
    /* Call DMA error callback */
    hadc->DMA_Handle->XferErrorCallback(hdma);
  }
}
 8001842:	e004      	b.n	800184e <ADC_DMAConvCplt+0x72>
    hadc->DMA_Handle->XferErrorCallback(hdma);
 8001844:	68fb      	ldr	r3, [r7, #12]
 8001846:	6b9b      	ldr	r3, [r3, #56]	@ 0x38
 8001848:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 800184a:	6878      	ldr	r0, [r7, #4]
 800184c:	4798      	blx	r3
}
 800184e:	bf00      	nop
 8001850:	3710      	adds	r7, #16
 8001852:	46bd      	mov	sp, r7
 8001854:	bd80      	pop	{r7, pc}

08001856 <ADC_DMAHalfConvCplt>:
  * @brief  DMA half transfer complete callback. 
  * @param  hdma pointer to DMA handle.
  * @retval None
  */
static void ADC_DMAHalfConvCplt(DMA_HandleTypeDef *hdma)   
{
 8001856:	b580      	push	{r7, lr}
 8001858:	b084      	sub	sp, #16
 800185a:	af00      	add	r7, sp, #0
 800185c:	6078      	str	r0, [r7, #4]
  /* Retrieve ADC handle corresponding to current DMA handle */
  ADC_HandleTypeDef* hadc = ( ADC_HandleTypeDef* )((DMA_HandleTypeDef* )hdma)->Parent;
 800185e:	687b      	ldr	r3, [r7, #4]
 8001860:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8001862:	60fb      	str	r3, [r7, #12]
  
  /* Half conversion callback */
#if (USE_HAL_ADC_REGISTER_CALLBACKS == 1)
    hadc->ConvHalfCpltCallback(hadc);
#else
  HAL_ADC_ConvHalfCpltCallback(hadc);
 8001864:	68f8      	ldr	r0, [r7, #12]
 8001866:	f7fe ff67 	bl	8000738 <HAL_ADC_ConvHalfCpltCallback>
#endif /* USE_HAL_ADC_REGISTER_CALLBACKS */ 
}
 800186a:	bf00      	nop
 800186c:	3710      	adds	r7, #16
 800186e:	46bd      	mov	sp, r7
 8001870:	bd80      	pop	{r7, pc}

08001872 <ADC_DMAError>:
  * @brief  DMA error callback 
  * @param  hdma pointer to DMA handle.
  * @retval None
  */
static void ADC_DMAError(DMA_HandleTypeDef *hdma)   
{
 8001872:	b580      	push	{r7, lr}
 8001874:	b084      	sub	sp, #16
 8001876:	af00      	add	r7, sp, #0
 8001878:	6078      	str	r0, [r7, #4]
  /* Retrieve ADC handle corresponding to current DMA handle */
  ADC_HandleTypeDef* hadc = ( ADC_HandleTypeDef* )((DMA_HandleTypeDef* )hdma)->Parent;
 800187a:	687b      	ldr	r3, [r7, #4]
 800187c:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 800187e:	60fb      	str	r3, [r7, #12]
  
  /* Set ADC state */
  SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_DMA);
 8001880:	68fb      	ldr	r3, [r7, #12]
 8001882:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8001884:	f043 0240 	orr.w	r2, r3, #64	@ 0x40
 8001888:	68fb      	ldr	r3, [r7, #12]
 800188a:	641a      	str	r2, [r3, #64]	@ 0x40
  
  /* Set ADC error code to DMA error */
  SET_BIT(hadc->ErrorCode, HAL_ADC_ERROR_DMA);
 800188c:	68fb      	ldr	r3, [r7, #12]
 800188e:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 8001890:	f043 0204 	orr.w	r2, r3, #4
 8001894:	68fb      	ldr	r3, [r7, #12]
 8001896:	645a      	str	r2, [r3, #68]	@ 0x44
  
  /* Error callback */
#if (USE_HAL_ADC_REGISTER_CALLBACKS == 1)
      hadc->ErrorCallback(hadc);
#else
      HAL_ADC_ErrorCallback(hadc);
 8001898:	68f8      	ldr	r0, [r7, #12]
 800189a:	f7ff f8bd 	bl	8000a18 <HAL_ADC_ErrorCallback>
#endif /* USE_HAL_ADC_REGISTER_CALLBACKS */
}
 800189e:	bf00      	nop
 80018a0:	3710      	adds	r7, #16
 80018a2:	46bd      	mov	sp, r7
 80018a4:	bd80      	pop	{r7, pc}
	...

080018a8 <ADC_Enable>:
  *         and voltage regulator must be enabled (done into HAL_ADC_Init()).
  * @param  hadc ADC handle
  * @retval HAL status.
  */
static HAL_StatusTypeDef ADC_Enable(ADC_HandleTypeDef* hadc)
{
 80018a8:	b580      	push	{r7, lr}
 80018aa:	b084      	sub	sp, #16
 80018ac:	af00      	add	r7, sp, #0
 80018ae:	6078      	str	r0, [r7, #4]
  uint32_t tickstart = 0U;
 80018b0:	2300      	movs	r3, #0
 80018b2:	60fb      	str	r3, [r7, #12]
  
  /* ADC enable and wait for ADC ready (in case of ADC is disabled or         */
  /* enabling phase not yet completed: flag ADC ready not yet set).           */
  /* Timeout implemented to not be stuck if ADC cannot be enabled (possible   */
  /* causes: ADC clock not running, ...).                                     */
  if (ADC_IS_ENABLE(hadc) == RESET)
 80018b4:	687b      	ldr	r3, [r7, #4]
 80018b6:	681b      	ldr	r3, [r3, #0]
 80018b8:	689b      	ldr	r3, [r3, #8]
 80018ba:	f003 0303 	and.w	r3, r3, #3
 80018be:	2b01      	cmp	r3, #1
 80018c0:	d108      	bne.n	80018d4 <ADC_Enable+0x2c>
 80018c2:	687b      	ldr	r3, [r7, #4]
 80018c4:	681b      	ldr	r3, [r3, #0]
 80018c6:	681b      	ldr	r3, [r3, #0]
 80018c8:	f003 0301 	and.w	r3, r3, #1
 80018cc:	2b01      	cmp	r3, #1
 80018ce:	d101      	bne.n	80018d4 <ADC_Enable+0x2c>
 80018d0:	2301      	movs	r3, #1
 80018d2:	e000      	b.n	80018d6 <ADC_Enable+0x2e>
 80018d4:	2300      	movs	r3, #0
 80018d6:	2b00      	cmp	r3, #0
 80018d8:	d143      	bne.n	8001962 <ADC_Enable+0xba>
  {
    /* Check if conditions to enable the ADC are fulfilled */
    if (ADC_ENABLING_CONDITIONS(hadc) == RESET)
 80018da:	687b      	ldr	r3, [r7, #4]
 80018dc:	681b      	ldr	r3, [r3, #0]
 80018de:	689a      	ldr	r2, [r3, #8]
 80018e0:	4b22      	ldr	r3, [pc, #136]	@ (800196c <ADC_Enable+0xc4>)
 80018e2:	4013      	ands	r3, r2
 80018e4:	2b00      	cmp	r3, #0
 80018e6:	d00d      	beq.n	8001904 <ADC_Enable+0x5c>
    {
      /* Update ADC state machine to error */
      SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL);
 80018e8:	687b      	ldr	r3, [r7, #4]
 80018ea:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 80018ec:	f043 0210 	orr.w	r2, r3, #16
 80018f0:	687b      	ldr	r3, [r7, #4]
 80018f2:	641a      	str	r2, [r3, #64]	@ 0x40
      
      /* Set ADC error code to ADC IP internal error */
      SET_BIT(hadc->ErrorCode, HAL_ADC_ERROR_INTERNAL);
 80018f4:	687b      	ldr	r3, [r7, #4]
 80018f6:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 80018f8:	f043 0201 	orr.w	r2, r3, #1
 80018fc:	687b      	ldr	r3, [r7, #4]
 80018fe:	645a      	str	r2, [r3, #68]	@ 0x44
      
      return HAL_ERROR;
 8001900:	2301      	movs	r3, #1
 8001902:	e02f      	b.n	8001964 <ADC_Enable+0xbc>
    }
    
    /* Enable the ADC peripheral */
    __HAL_ADC_ENABLE(hadc);
 8001904:	687b      	ldr	r3, [r7, #4]
 8001906:	681b      	ldr	r3, [r3, #0]
 8001908:	689a      	ldr	r2, [r3, #8]
 800190a:	687b      	ldr	r3, [r7, #4]
 800190c:	681b      	ldr	r3, [r3, #0]
 800190e:	f042 0201 	orr.w	r2, r2, #1
 8001912:	609a      	str	r2, [r3, #8]
    
    /* Wait for ADC effectively enabled */
    tickstart = HAL_GetTick();  
 8001914:	f7ff f874 	bl	8000a00 <HAL_GetTick>
 8001918:	60f8      	str	r0, [r7, #12]
    
    while(__HAL_ADC_GET_FLAG(hadc, ADC_FLAG_RDY) == RESET)
 800191a:	e01b      	b.n	8001954 <ADC_Enable+0xac>
    {
      if((HAL_GetTick() - tickstart) > ADC_ENABLE_TIMEOUT)
 800191c:	f7ff f870 	bl	8000a00 <HAL_GetTick>
 8001920:	4602      	mov	r2, r0
 8001922:	68fb      	ldr	r3, [r7, #12]
 8001924:	1ad3      	subs	r3, r2, r3
 8001926:	2b02      	cmp	r3, #2
 8001928:	d914      	bls.n	8001954 <ADC_Enable+0xac>
      {
        /* New check to avoid false timeout detection in case of preemption */
        if(__HAL_ADC_GET_FLAG(hadc, ADC_FLAG_RDY) == RESET)
 800192a:	687b      	ldr	r3, [r7, #4]
 800192c:	681b      	ldr	r3, [r3, #0]
 800192e:	681b      	ldr	r3, [r3, #0]
 8001930:	f003 0301 	and.w	r3, r3, #1
 8001934:	2b01      	cmp	r3, #1
 8001936:	d00d      	beq.n	8001954 <ADC_Enable+0xac>
        {
          /* Update ADC state machine to error */
          SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL);
 8001938:	687b      	ldr	r3, [r7, #4]
 800193a:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 800193c:	f043 0210 	orr.w	r2, r3, #16
 8001940:	687b      	ldr	r3, [r7, #4]
 8001942:	641a      	str	r2, [r3, #64]	@ 0x40

          /* Set ADC error code to ADC IP internal error */
          SET_BIT(hadc->ErrorCode, HAL_ADC_ERROR_INTERNAL);
 8001944:	687b      	ldr	r3, [r7, #4]
 8001946:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 8001948:	f043 0201 	orr.w	r2, r3, #1
 800194c:	687b      	ldr	r3, [r7, #4]
 800194e:	645a      	str	r2, [r3, #68]	@ 0x44

          return HAL_ERROR;
 8001950:	2301      	movs	r3, #1
 8001952:	e007      	b.n	8001964 <ADC_Enable+0xbc>
    while(__HAL_ADC_GET_FLAG(hadc, ADC_FLAG_RDY) == RESET)
 8001954:	687b      	ldr	r3, [r7, #4]
 8001956:	681b      	ldr	r3, [r3, #0]
 8001958:	681b      	ldr	r3, [r3, #0]
 800195a:	f003 0301 	and.w	r3, r3, #1
 800195e:	2b01      	cmp	r3, #1
 8001960:	d1dc      	bne.n	800191c <ADC_Enable+0x74>
      }
    }
  }
  
  /* Return HAL status */
  return HAL_OK;
 8001962:	2300      	movs	r3, #0
}
 8001964:	4618      	mov	r0, r3
 8001966:	3710      	adds	r7, #16
 8001968:	46bd      	mov	sp, r7
 800196a:	bd80      	pop	{r7, pc}
 800196c:	8000003f 	.word	0x8000003f

08001970 <ADC_Disable>:
  *         stopped.
  * @param  hadc ADC handle
  * @retval HAL status.
  */
static HAL_StatusTypeDef ADC_Disable(ADC_HandleTypeDef* hadc)
{
 8001970:	b580      	push	{r7, lr}
 8001972:	b084      	sub	sp, #16
 8001974:	af00      	add	r7, sp, #0
 8001976:	6078      	str	r0, [r7, #4]
  uint32_t tickstart = 0U;
 8001978:	2300      	movs	r3, #0
 800197a:	60fb      	str	r3, [r7, #12]
  
  /* Verification if ADC is not already disabled:                             */
  /* Note: forbidden to disable ADC (set bit ADC_CR_ADDIS) if ADC is already  */
  /* disabled.                                                                */
  if (ADC_IS_ENABLE(hadc) != RESET )
 800197c:	687b      	ldr	r3, [r7, #4]
 800197e:	681b      	ldr	r3, [r3, #0]
 8001980:	689b      	ldr	r3, [r3, #8]
 8001982:	f003 0303 	and.w	r3, r3, #3
 8001986:	2b01      	cmp	r3, #1
 8001988:	d108      	bne.n	800199c <ADC_Disable+0x2c>
 800198a:	687b      	ldr	r3, [r7, #4]
 800198c:	681b      	ldr	r3, [r3, #0]
 800198e:	681b      	ldr	r3, [r3, #0]
 8001990:	f003 0301 	and.w	r3, r3, #1
 8001994:	2b01      	cmp	r3, #1
 8001996:	d101      	bne.n	800199c <ADC_Disable+0x2c>
 8001998:	2301      	movs	r3, #1
 800199a:	e000      	b.n	800199e <ADC_Disable+0x2e>
 800199c:	2300      	movs	r3, #0
 800199e:	2b00      	cmp	r3, #0
 80019a0:	d047      	beq.n	8001a32 <ADC_Disable+0xc2>
  {
    /* Check if conditions to disable the ADC are fulfilled */
    if (ADC_DISABLING_CONDITIONS(hadc) != RESET)
 80019a2:	687b      	ldr	r3, [r7, #4]
 80019a4:	681b      	ldr	r3, [r3, #0]
 80019a6:	689b      	ldr	r3, [r3, #8]
 80019a8:	f003 030d 	and.w	r3, r3, #13
 80019ac:	2b01      	cmp	r3, #1
 80019ae:	d10f      	bne.n	80019d0 <ADC_Disable+0x60>
    {
      /* Disable the ADC peripheral */
      __HAL_ADC_DISABLE(hadc);
 80019b0:	687b      	ldr	r3, [r7, #4]
 80019b2:	681b      	ldr	r3, [r3, #0]
 80019b4:	689a      	ldr	r2, [r3, #8]
 80019b6:	687b      	ldr	r3, [r7, #4]
 80019b8:	681b      	ldr	r3, [r3, #0]
 80019ba:	f042 0202 	orr.w	r2, r2, #2
 80019be:	609a      	str	r2, [r3, #8]
 80019c0:	687b      	ldr	r3, [r7, #4]
 80019c2:	681b      	ldr	r3, [r3, #0]
 80019c4:	2203      	movs	r2, #3
 80019c6:	601a      	str	r2, [r3, #0]
      
      return HAL_ERROR;
    }
     
    /* Wait for ADC effectively disabled */
    tickstart = HAL_GetTick();
 80019c8:	f7ff f81a 	bl	8000a00 <HAL_GetTick>
 80019cc:	60f8      	str	r0, [r7, #12]
    
    while(HAL_IS_BIT_SET(hadc->Instance->CR, ADC_CR_ADEN))
 80019ce:	e029      	b.n	8001a24 <ADC_Disable+0xb4>
      SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL);
 80019d0:	687b      	ldr	r3, [r7, #4]
 80019d2:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 80019d4:	f043 0210 	orr.w	r2, r3, #16
 80019d8:	687b      	ldr	r3, [r7, #4]
 80019da:	641a      	str	r2, [r3, #64]	@ 0x40
      SET_BIT(hadc->ErrorCode, HAL_ADC_ERROR_INTERNAL);
 80019dc:	687b      	ldr	r3, [r7, #4]
 80019de:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 80019e0:	f043 0201 	orr.w	r2, r3, #1
 80019e4:	687b      	ldr	r3, [r7, #4]
 80019e6:	645a      	str	r2, [r3, #68]	@ 0x44
      return HAL_ERROR;
 80019e8:	2301      	movs	r3, #1
 80019ea:	e023      	b.n	8001a34 <ADC_Disable+0xc4>
    {
      if((HAL_GetTick() - tickstart) > ADC_DISABLE_TIMEOUT)
 80019ec:	f7ff f808 	bl	8000a00 <HAL_GetTick>
 80019f0:	4602      	mov	r2, r0
 80019f2:	68fb      	ldr	r3, [r7, #12]
 80019f4:	1ad3      	subs	r3, r2, r3
 80019f6:	2b02      	cmp	r3, #2
 80019f8:	d914      	bls.n	8001a24 <ADC_Disable+0xb4>
      {
        /* New check to avoid false timeout detection in case of preemption */
        if(HAL_IS_BIT_SET(hadc->Instance->CR, ADC_CR_ADEN))
 80019fa:	687b      	ldr	r3, [r7, #4]
 80019fc:	681b      	ldr	r3, [r3, #0]
 80019fe:	689b      	ldr	r3, [r3, #8]
 8001a00:	f003 0301 	and.w	r3, r3, #1
 8001a04:	2b01      	cmp	r3, #1
 8001a06:	d10d      	bne.n	8001a24 <ADC_Disable+0xb4>
        {
          /* Update ADC state machine to error */
          SET_BIT(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL);
 8001a08:	687b      	ldr	r3, [r7, #4]
 8001a0a:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8001a0c:	f043 0210 	orr.w	r2, r3, #16
 8001a10:	687b      	ldr	r3, [r7, #4]
 8001a12:	641a      	str	r2, [r3, #64]	@ 0x40

          /* Set ADC error code to ADC IP internal error */
          SET_BIT(hadc->ErrorCode, HAL_ADC_ERROR_INTERNAL);
 8001a14:	687b      	ldr	r3, [r7, #4]
 8001a16:	6c5b      	ldr	r3, [r3, #68]	@ 0x44
 8001a18:	f043 0201 	orr.w	r2, r3, #1
 8001a1c:	687b      	ldr	r3, [r7, #4]
 8001a1e:	645a      	str	r2, [r3, #68]	@ 0x44

          return HAL_ERROR;
 8001a20:	2301      	movs	r3, #1
 8001a22:	e007      	b.n	8001a34 <ADC_Disable+0xc4>
    while(HAL_IS_BIT_SET(hadc->Instance->CR, ADC_CR_ADEN))
 8001a24:	687b      	ldr	r3, [r7, #4]
 8001a26:	681b      	ldr	r3, [r3, #0]
 8001a28:	689b      	ldr	r3, [r3, #8]
 8001a2a:	f003 0301 	and.w	r3, r3, #1
 8001a2e:	2b01      	cmp	r3, #1
 8001a30:	d0dc      	beq.n	80019ec <ADC_Disable+0x7c>
      }
    }
  }
  
  /* Return HAL status */
  return HAL_OK;
 8001a32:	2300      	movs	r3, #0
}
 8001a34:	4618      	mov	r0, r3
 8001a36:	3710      	adds	r7, #16
 8001a38:	46bd      	mov	sp, r7
 8001a3a:	bd80      	pop	{r7, pc}

08001a3c <__NVIC_SetPriorityGrouping>:
           In case of a conflict between priority grouping and available
           priority bits (__NVIC_PRIO_BITS), the smallest possible priority group is set.
  \param [in]      PriorityGroup  Priority grouping field.
 */
__STATIC_INLINE void __NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
{
 8001a3c:	b480      	push	{r7}
 8001a3e:	b085      	sub	sp, #20
 8001a40:	af00      	add	r7, sp, #0
 8001a42:	6078      	str	r0, [r7, #4]
  uint32_t reg_value;
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);             /* only values 0..7 are used          */
 8001a44:	687b      	ldr	r3, [r7, #4]
 8001a46:	f003 0307 	and.w	r3, r3, #7
 8001a4a:	60fb      	str	r3, [r7, #12]

  reg_value  =  SCB->AIRCR;                                                   /* read old register configuration    */
 8001a4c:	4b0c      	ldr	r3, [pc, #48]	@ (8001a80 <__NVIC_SetPriorityGrouping+0x44>)
 8001a4e:	68db      	ldr	r3, [r3, #12]
 8001a50:	60bb      	str	r3, [r7, #8]
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
 8001a52:	68ba      	ldr	r2, [r7, #8]
 8001a54:	f64f 03ff 	movw	r3, #63743	@ 0xf8ff
 8001a58:	4013      	ands	r3, r2
 8001a5a:	60bb      	str	r3, [r7, #8]
  reg_value  =  (reg_value                                   |
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
                (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos)  );              /* Insert write key and priority group */
 8001a5c:	68fb      	ldr	r3, [r7, #12]
 8001a5e:	021a      	lsls	r2, r3, #8
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 8001a60:	68bb      	ldr	r3, [r7, #8]
 8001a62:	4313      	orrs	r3, r2
  reg_value  =  (reg_value                                   |
 8001a64:	f043 63bf 	orr.w	r3, r3, #100139008	@ 0x5f80000
 8001a68:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
 8001a6c:	60bb      	str	r3, [r7, #8]
  SCB->AIRCR =  reg_value;
 8001a6e:	4a04      	ldr	r2, [pc, #16]	@ (8001a80 <__NVIC_SetPriorityGrouping+0x44>)
 8001a70:	68bb      	ldr	r3, [r7, #8]
 8001a72:	60d3      	str	r3, [r2, #12]
}
 8001a74:	bf00      	nop
 8001a76:	3714      	adds	r7, #20
 8001a78:	46bd      	mov	sp, r7
 8001a7a:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001a7e:	4770      	bx	lr
 8001a80:	e000ed00 	.word	0xe000ed00

08001a84 <__NVIC_GetPriorityGrouping>:
  \brief   Get Priority Grouping
  \details Reads the priority grouping field from the NVIC Interrupt Controller.
  \return                Priority grouping field (SCB->AIRCR [10:8] PRIGROUP field).
 */
__STATIC_INLINE uint32_t __NVIC_GetPriorityGrouping(void)
{
 8001a84:	b480      	push	{r7}
 8001a86:	af00      	add	r7, sp, #0
  return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
 8001a88:	4b04      	ldr	r3, [pc, #16]	@ (8001a9c <__NVIC_GetPriorityGrouping+0x18>)
 8001a8a:	68db      	ldr	r3, [r3, #12]
 8001a8c:	0a1b      	lsrs	r3, r3, #8
 8001a8e:	f003 0307 	and.w	r3, r3, #7
}
 8001a92:	4618      	mov	r0, r3
 8001a94:	46bd      	mov	sp, r7
 8001a96:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001a9a:	4770      	bx	lr
 8001a9c:	e000ed00 	.word	0xe000ed00

08001aa0 <__NVIC_EnableIRQ>:
  \details Enables a device specific interrupt in the NVIC interrupt controller.
  \param [in]      IRQn  Device specific interrupt number.
  \note    IRQn must not be negative.
 */
__STATIC_INLINE void __NVIC_EnableIRQ(IRQn_Type IRQn)
{
 8001aa0:	b480      	push	{r7}
 8001aa2:	b083      	sub	sp, #12
 8001aa4:	af00      	add	r7, sp, #0
 8001aa6:	4603      	mov	r3, r0
 8001aa8:	71fb      	strb	r3, [r7, #7]
  if ((int32_t)(IRQn) >= 0)
 8001aaa:	f997 3007 	ldrsb.w	r3, [r7, #7]
 8001aae:	2b00      	cmp	r3, #0
 8001ab0:	db0b      	blt.n	8001aca <__NVIC_EnableIRQ+0x2a>
  {
    NVIC->ISER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
 8001ab2:	79fb      	ldrb	r3, [r7, #7]
 8001ab4:	f003 021f 	and.w	r2, r3, #31
 8001ab8:	4907      	ldr	r1, [pc, #28]	@ (8001ad8 <__NVIC_EnableIRQ+0x38>)
 8001aba:	f997 3007 	ldrsb.w	r3, [r7, #7]
 8001abe:	095b      	lsrs	r3, r3, #5
 8001ac0:	2001      	movs	r0, #1
 8001ac2:	fa00 f202 	lsl.w	r2, r0, r2
 8001ac6:	f841 2023 	str.w	r2, [r1, r3, lsl #2]
  }
}
 8001aca:	bf00      	nop
 8001acc:	370c      	adds	r7, #12
 8001ace:	46bd      	mov	sp, r7
 8001ad0:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001ad4:	4770      	bx	lr
 8001ad6:	bf00      	nop
 8001ad8:	e000e100 	.word	0xe000e100

08001adc <__NVIC_SetPriority>:
  \param [in]      IRQn  Interrupt number.
  \param [in]  priority  Priority to set.
  \note    The priority cannot be set for every processor exception.
 */
__STATIC_INLINE void __NVIC_SetPriority(IRQn_Type IRQn, uint32_t priority)
{
 8001adc:	b480      	push	{r7}
 8001ade:	b083      	sub	sp, #12
 8001ae0:	af00      	add	r7, sp, #0
 8001ae2:	4603      	mov	r3, r0
 8001ae4:	6039      	str	r1, [r7, #0]
 8001ae6:	71fb      	strb	r3, [r7, #7]
  if ((int32_t)(IRQn) >= 0)
 8001ae8:	f997 3007 	ldrsb.w	r3, [r7, #7]
 8001aec:	2b00      	cmp	r3, #0
 8001aee:	db0a      	blt.n	8001b06 <__NVIC_SetPriority+0x2a>
  {
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8001af0:	683b      	ldr	r3, [r7, #0]
 8001af2:	b2da      	uxtb	r2, r3
 8001af4:	490c      	ldr	r1, [pc, #48]	@ (8001b28 <__NVIC_SetPriority+0x4c>)
 8001af6:	f997 3007 	ldrsb.w	r3, [r7, #7]
 8001afa:	0112      	lsls	r2, r2, #4
 8001afc:	b2d2      	uxtb	r2, r2
 8001afe:	440b      	add	r3, r1
 8001b00:	f883 2300 	strb.w	r2, [r3, #768]	@ 0x300
  }
  else
  {
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
  }
}
 8001b04:	e00a      	b.n	8001b1c <__NVIC_SetPriority+0x40>
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8001b06:	683b      	ldr	r3, [r7, #0]
 8001b08:	b2da      	uxtb	r2, r3
 8001b0a:	4908      	ldr	r1, [pc, #32]	@ (8001b2c <__NVIC_SetPriority+0x50>)
 8001b0c:	79fb      	ldrb	r3, [r7, #7]
 8001b0e:	f003 030f 	and.w	r3, r3, #15
 8001b12:	3b04      	subs	r3, #4
 8001b14:	0112      	lsls	r2, r2, #4
 8001b16:	b2d2      	uxtb	r2, r2
 8001b18:	440b      	add	r3, r1
 8001b1a:	761a      	strb	r2, [r3, #24]
}
 8001b1c:	bf00      	nop
 8001b1e:	370c      	adds	r7, #12
 8001b20:	46bd      	mov	sp, r7
 8001b22:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001b26:	4770      	bx	lr
 8001b28:	e000e100 	.word	0xe000e100
 8001b2c:	e000ed00 	.word	0xe000ed00

08001b30 <NVIC_EncodePriority>:
  \param [in]   PreemptPriority  Preemptive priority value (starting from 0).
  \param [in]       SubPriority  Subpriority value (starting from 0).
  \return                        Encoded priority. Value can be used in the function \ref NVIC_SetPriority().
 */
__STATIC_INLINE uint32_t NVIC_EncodePriority (uint32_t PriorityGroup, uint32_t PreemptPriority, uint32_t SubPriority)
{
 8001b30:	b480      	push	{r7}
 8001b32:	b089      	sub	sp, #36	@ 0x24
 8001b34:	af00      	add	r7, sp, #0
 8001b36:	60f8      	str	r0, [r7, #12]
 8001b38:	60b9      	str	r1, [r7, #8]
 8001b3a:	607a      	str	r2, [r7, #4]
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used          */
 8001b3c:	68fb      	ldr	r3, [r7, #12]
 8001b3e:	f003 0307 	and.w	r3, r3, #7
 8001b42:	61fb      	str	r3, [r7, #28]
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NVIC_PRIO_BITS) : (uint32_t)(7UL - PriorityGroupTmp);
 8001b44:	69fb      	ldr	r3, [r7, #28]
 8001b46:	f1c3 0307 	rsb	r3, r3, #7
 8001b4a:	2b04      	cmp	r3, #4
 8001b4c:	bf28      	it	cs
 8001b4e:	2304      	movcs	r3, #4
 8001b50:	61bb      	str	r3, [r7, #24]
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8001b52:	69fb      	ldr	r3, [r7, #28]
 8001b54:	3304      	adds	r3, #4
 8001b56:	2b06      	cmp	r3, #6
 8001b58:	d902      	bls.n	8001b60 <NVIC_EncodePriority+0x30>
 8001b5a:	69fb      	ldr	r3, [r7, #28]
 8001b5c:	3b03      	subs	r3, #3
 8001b5e:	e000      	b.n	8001b62 <NVIC_EncodePriority+0x32>
 8001b60:	2300      	movs	r3, #0
 8001b62:	617b      	str	r3, [r7, #20]

  return (
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 8001b64:	f04f 32ff 	mov.w	r2, #4294967295
 8001b68:	69bb      	ldr	r3, [r7, #24]
 8001b6a:	fa02 f303 	lsl.w	r3, r2, r3
 8001b6e:	43da      	mvns	r2, r3
 8001b70:	68bb      	ldr	r3, [r7, #8]
 8001b72:	401a      	ands	r2, r3
 8001b74:	697b      	ldr	r3, [r7, #20]
 8001b76:	409a      	lsls	r2, r3
           ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
 8001b78:	f04f 31ff 	mov.w	r1, #4294967295
 8001b7c:	697b      	ldr	r3, [r7, #20]
 8001b7e:	fa01 f303 	lsl.w	r3, r1, r3
 8001b82:	43d9      	mvns	r1, r3
 8001b84:	687b      	ldr	r3, [r7, #4]
 8001b86:	400b      	ands	r3, r1
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 8001b88:	4313      	orrs	r3, r2
         );
}
 8001b8a:	4618      	mov	r0, r3
 8001b8c:	3724      	adds	r7, #36	@ 0x24
 8001b8e:	46bd      	mov	sp, r7
 8001b90:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001b94:	4770      	bx	lr
	...

08001b98 <SysTick_Config>:
  \note    When the variable <b>__Vendor_SysTickConfig</b> is set to 1, then the
           function <b>SysTick_Config</b> is not included. In this case, the file <b><i>device</i>.h</b>
           must contain a vendor-specific implementation of this function.
 */
__STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks)
{
 8001b98:	b580      	push	{r7, lr}
 8001b9a:	b082      	sub	sp, #8
 8001b9c:	af00      	add	r7, sp, #0
 8001b9e:	6078      	str	r0, [r7, #4]
  if ((ticks - 1UL) > SysTick_LOAD_RELOAD_Msk)
 8001ba0:	687b      	ldr	r3, [r7, #4]
 8001ba2:	3b01      	subs	r3, #1
 8001ba4:	f1b3 7f80 	cmp.w	r3, #16777216	@ 0x1000000
 8001ba8:	d301      	bcc.n	8001bae <SysTick_Config+0x16>
  {
    return (1UL);                                                   /* Reload value impossible */
 8001baa:	2301      	movs	r3, #1
 8001bac:	e00f      	b.n	8001bce <SysTick_Config+0x36>
  }

  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 8001bae:	4a0a      	ldr	r2, [pc, #40]	@ (8001bd8 <SysTick_Config+0x40>)
 8001bb0:	687b      	ldr	r3, [r7, #4]
 8001bb2:	3b01      	subs	r3, #1
 8001bb4:	6053      	str	r3, [r2, #4]
  NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* set Priority for Systick Interrupt */
 8001bb6:	210f      	movs	r1, #15
 8001bb8:	f04f 30ff 	mov.w	r0, #4294967295
 8001bbc:	f7ff ff8e 	bl	8001adc <__NVIC_SetPriority>
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
 8001bc0:	4b05      	ldr	r3, [pc, #20]	@ (8001bd8 <SysTick_Config+0x40>)
 8001bc2:	2200      	movs	r2, #0
 8001bc4:	609a      	str	r2, [r3, #8]
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 8001bc6:	4b04      	ldr	r3, [pc, #16]	@ (8001bd8 <SysTick_Config+0x40>)
 8001bc8:	2207      	movs	r2, #7
 8001bca:	601a      	str	r2, [r3, #0]
                   SysTick_CTRL_TICKINT_Msk   |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
  return (0UL);                                                     /* Function successful */
 8001bcc:	2300      	movs	r3, #0
}
 8001bce:	4618      	mov	r0, r3
 8001bd0:	3708      	adds	r7, #8
 8001bd2:	46bd      	mov	sp, r7
 8001bd4:	bd80      	pop	{r7, pc}
 8001bd6:	bf00      	nop
 8001bd8:	e000e010 	.word	0xe000e010

08001bdc <HAL_NVIC_SetPriorityGrouping>:
  * @note   When the NVIC_PriorityGroup_0 is selected, IRQ pre-emption is no more possible.
  *         The pending IRQ priority will be managed only by the subpriority.
  * @retval None
  */
void HAL_NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
{
 8001bdc:	b580      	push	{r7, lr}
 8001bde:	b082      	sub	sp, #8
 8001be0:	af00      	add	r7, sp, #0
 8001be2:	6078      	str	r0, [r7, #4]
  /* Check the parameters */
  assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));

  /* Set the PRIGROUP[10:8] bits according to the PriorityGroup parameter value */
  NVIC_SetPriorityGrouping(PriorityGroup);
 8001be4:	6878      	ldr	r0, [r7, #4]
 8001be6:	f7ff ff29 	bl	8001a3c <__NVIC_SetPriorityGrouping>
}
 8001bea:	bf00      	nop
 8001bec:	3708      	adds	r7, #8
 8001bee:	46bd      	mov	sp, r7
 8001bf0:	bd80      	pop	{r7, pc}

08001bf2 <HAL_NVIC_SetPriority>:
  *         This parameter can be a value between 0 and 15 as described in the table CORTEX_NVIC_Priority_Table
  *         A lower priority value indicates a higher priority.
  * @retval None
  */
void HAL_NVIC_SetPriority(IRQn_Type IRQn, uint32_t PreemptPriority, uint32_t SubPriority)
{
 8001bf2:	b580      	push	{r7, lr}
 8001bf4:	b086      	sub	sp, #24
 8001bf6:	af00      	add	r7, sp, #0
 8001bf8:	4603      	mov	r3, r0
 8001bfa:	60b9      	str	r1, [r7, #8]
 8001bfc:	607a      	str	r2, [r7, #4]
 8001bfe:	73fb      	strb	r3, [r7, #15]
  uint32_t prioritygroup = 0x00U;
 8001c00:	2300      	movs	r3, #0
 8001c02:	617b      	str	r3, [r7, #20]
  
  /* Check the parameters */
  assert_param(IS_NVIC_SUB_PRIORITY(SubPriority));
  assert_param(IS_NVIC_PREEMPTION_PRIORITY(PreemptPriority));
  
  prioritygroup = NVIC_GetPriorityGrouping();
 8001c04:	f7ff ff3e 	bl	8001a84 <__NVIC_GetPriorityGrouping>
 8001c08:	6178      	str	r0, [r7, #20]
  
  NVIC_SetPriority(IRQn, NVIC_EncodePriority(prioritygroup, PreemptPriority, SubPriority));
 8001c0a:	687a      	ldr	r2, [r7, #4]
 8001c0c:	68b9      	ldr	r1, [r7, #8]
 8001c0e:	6978      	ldr	r0, [r7, #20]
 8001c10:	f7ff ff8e 	bl	8001b30 <NVIC_EncodePriority>
 8001c14:	4602      	mov	r2, r0
 8001c16:	f997 300f 	ldrsb.w	r3, [r7, #15]
 8001c1a:	4611      	mov	r1, r2
 8001c1c:	4618      	mov	r0, r3
 8001c1e:	f7ff ff5d 	bl	8001adc <__NVIC_SetPriority>
}
 8001c22:	bf00      	nop
 8001c24:	3718      	adds	r7, #24
 8001c26:	46bd      	mov	sp, r7
 8001c28:	bd80      	pop	{r7, pc}

08001c2a <HAL_NVIC_EnableIRQ>:
  *         This parameter can be an enumerator of IRQn_Type enumeration
  *         (For the complete STM32 Devices IRQ Channels list, please refer to the appropriate CMSIS device file (stm32f3xxxx.h))
  * @retval None
  */
void HAL_NVIC_EnableIRQ(IRQn_Type IRQn)
{
 8001c2a:	b580      	push	{r7, lr}
 8001c2c:	b082      	sub	sp, #8
 8001c2e:	af00      	add	r7, sp, #0
 8001c30:	4603      	mov	r3, r0
 8001c32:	71fb      	strb	r3, [r7, #7]
  /* Check the parameters */
  assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
  
  /* Enable interrupt */
  NVIC_EnableIRQ(IRQn);
 8001c34:	f997 3007 	ldrsb.w	r3, [r7, #7]
 8001c38:	4618      	mov	r0, r3
 8001c3a:	f7ff ff31 	bl	8001aa0 <__NVIC_EnableIRQ>
}
 8001c3e:	bf00      	nop
 8001c40:	3708      	adds	r7, #8
 8001c42:	46bd      	mov	sp, r7
 8001c44:	bd80      	pop	{r7, pc}

08001c46 <HAL_SYSTICK_Config>:
  * @param  TicksNumb Specifies the ticks Number of ticks between two interrupts.
  * @retval status:  - 0  Function succeeded.
  *                  - 1  Function failed.
  */
uint32_t HAL_SYSTICK_Config(uint32_t TicksNumb)
{
 8001c46:	b580      	push	{r7, lr}
 8001c48:	b082      	sub	sp, #8
 8001c4a:	af00      	add	r7, sp, #0
 8001c4c:	6078      	str	r0, [r7, #4]
   return SysTick_Config(TicksNumb);
 8001c4e:	6878      	ldr	r0, [r7, #4]
 8001c50:	f7ff ffa2 	bl	8001b98 <SysTick_Config>
 8001c54:	4603      	mov	r3, r0
}
 8001c56:	4618      	mov	r0, r3
 8001c58:	3708      	adds	r7, #8
 8001c5a:	46bd      	mov	sp, r7
 8001c5c:	bd80      	pop	{r7, pc}

08001c5e <HAL_DAC_Init>:
  * @param  hdac pointer to a DAC_HandleTypeDef structure that contains
  *         the configuration information for the specified DAC.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DAC_Init(DAC_HandleTypeDef* hdac)
{
 8001c5e:	b580      	push	{r7, lr}
 8001c60:	b082      	sub	sp, #8
 8001c62:	af00      	add	r7, sp, #0
 8001c64:	6078      	str	r0, [r7, #4]
  /* Check DAC handle */
  if(hdac == NULL)
 8001c66:	687b      	ldr	r3, [r7, #4]
 8001c68:	2b00      	cmp	r3, #0
 8001c6a:	d101      	bne.n	8001c70 <HAL_DAC_Init+0x12>
  {
     return HAL_ERROR;
 8001c6c:	2301      	movs	r3, #1
 8001c6e:	e014      	b.n	8001c9a <HAL_DAC_Init+0x3c>
  }
  /* Check the parameters */
  assert_param(IS_DAC_ALL_INSTANCE(hdac->Instance));

  if(hdac->State == HAL_DAC_STATE_RESET)
 8001c70:	687b      	ldr	r3, [r7, #4]
 8001c72:	791b      	ldrb	r3, [r3, #4]
 8001c74:	b2db      	uxtb	r3, r3
 8001c76:	2b00      	cmp	r3, #0
 8001c78:	d105      	bne.n	8001c86 <HAL_DAC_Init+0x28>
    hdac->MspDeInitCallback             = HAL_DAC_MspDeInit;
  }
#endif /* USE_HAL_DAC_REGISTER_CALLBACKS */

    /* Allocate lock resource and initialize it */
    hdac->Lock = HAL_UNLOCKED;
 8001c7a:	687b      	ldr	r3, [r7, #4]
 8001c7c:	2200      	movs	r2, #0
 8001c7e:	715a      	strb	r2, [r3, #5]
#if (USE_HAL_DAC_REGISTER_CALLBACKS == 1)
    /* Init the low level hardware */
    hdac->MspInitCallback(hdac);
#else
    /* Init the low level hardware */
    HAL_DAC_MspInit(hdac);
 8001c80:	6878      	ldr	r0, [r7, #4]
 8001c82:	f7fe fbad 	bl	80003e0 <HAL_DAC_MspInit>
#endif /* USE_HAL_DAC_REGISTER_CALLBACKS */
  }

  /* Initialize the DAC state*/
  hdac->State = HAL_DAC_STATE_BUSY;
 8001c86:	687b      	ldr	r3, [r7, #4]
 8001c88:	2202      	movs	r2, #2
 8001c8a:	711a      	strb	r2, [r3, #4]

  /* Set DAC error code to none */
  hdac->ErrorCode = HAL_DAC_ERROR_NONE;
 8001c8c:	687b      	ldr	r3, [r7, #4]
 8001c8e:	2200      	movs	r2, #0
 8001c90:	611a      	str	r2, [r3, #16]

  /* Initialize the DAC state*/
  hdac->State = HAL_DAC_STATE_READY;
 8001c92:	687b      	ldr	r3, [r7, #4]
 8001c94:	2201      	movs	r2, #1
 8001c96:	711a      	strb	r2, [r3, #4]

  /* Return function status */
  return HAL_OK;
 8001c98:	2300      	movs	r3, #0
}
 8001c9a:	4618      	mov	r0, r3
 8001c9c:	3708      	adds	r7, #8
 8001c9e:	46bd      	mov	sp, r7
 8001ca0:	bd80      	pop	{r7, pc}

08001ca2 <HAL_DAC_SetValue>:
  *            @arg DAC_ALIGN_12B_R: 12bit right data alignment selected
  * @param  Data Data to be loaded in the selected data holding register.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DAC_SetValue(DAC_HandleTypeDef* hdac, uint32_t Channel, uint32_t Alignment, uint32_t Data)
{  
 8001ca2:	b480      	push	{r7}
 8001ca4:	b087      	sub	sp, #28
 8001ca6:	af00      	add	r7, sp, #0
 8001ca8:	60f8      	str	r0, [r7, #12]
 8001caa:	60b9      	str	r1, [r7, #8]
 8001cac:	607a      	str	r2, [r7, #4]
 8001cae:	603b      	str	r3, [r7, #0]
  __IO uint32_t tmp = 0U;
 8001cb0:	2300      	movs	r3, #0
 8001cb2:	617b      	str	r3, [r7, #20]
  /* Check the parameters */
  assert_param(IS_DAC_CHANNEL(Channel));
  assert_param(IS_DAC_ALIGN(Alignment));
  assert_param(IS_DAC_DATA(Data));
   
  tmp = (uint32_t) (hdac->Instance);
 8001cb4:	68fb      	ldr	r3, [r7, #12]
 8001cb6:	681b      	ldr	r3, [r3, #0]
 8001cb8:	617b      	str	r3, [r7, #20]

/* DAC 1 has 1 or 2 channels - no DAC2 */
/* DAC 1 has 2 channels 1U & 2U - DAC 2 has one channel 1U */

  if(Channel == DAC_CHANNEL_1)
 8001cba:	68bb      	ldr	r3, [r7, #8]
 8001cbc:	2b00      	cmp	r3, #0
 8001cbe:	d105      	bne.n	8001ccc <HAL_DAC_SetValue+0x2a>
  {
    tmp += DAC_DHR12R1_ALIGNMENT(Alignment);
 8001cc0:	697a      	ldr	r2, [r7, #20]
 8001cc2:	687b      	ldr	r3, [r7, #4]
 8001cc4:	4413      	add	r3, r2
 8001cc6:	3308      	adds	r3, #8
 8001cc8:	617b      	str	r3, [r7, #20]
 8001cca:	e004      	b.n	8001cd6 <HAL_DAC_SetValue+0x34>
    defined(STM32F303xC) || defined(STM32F358xx)                         || \
    defined(STM32F303x8) || defined(STM32F334x8) || defined(STM32F328xx) || \
    defined(STM32F373xC) || defined(STM32F378xx)
  else /* channel = DAC_CHANNEL_2  */
  {
    tmp += DAC_DHR12R2_ALIGNMENT(Alignment);
 8001ccc:	697a      	ldr	r2, [r7, #20]
 8001cce:	687b      	ldr	r3, [r7, #4]
 8001cd0:	4413      	add	r3, r2
 8001cd2:	3314      	adds	r3, #20
 8001cd4:	617b      	str	r3, [r7, #20]
       /* STM32F303xC || STM32F358xx                || */
       /* STM32F303x8 || STM32F334x8 || STM32F328xx || */
       /* STM32F373xC || STM32F378xx                   */

  /* Set the DAC channel1 selected data holding register */
  *(__IO uint32_t *) tmp = Data;
 8001cd6:	697b      	ldr	r3, [r7, #20]
 8001cd8:	461a      	mov	r2, r3
 8001cda:	683b      	ldr	r3, [r7, #0]
 8001cdc:	6013      	str	r3, [r2, #0]
  
  /* Return function status */
  return HAL_OK;
 8001cde:	2300      	movs	r3, #0
}
 8001ce0:	4618      	mov	r0, r3
 8001ce2:	371c      	adds	r7, #28
 8001ce4:	46bd      	mov	sp, r7
 8001ce6:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001cea:	4770      	bx	lr

08001cec <HAL_DAC_Start>:
  *            @arg DAC_CHANNEL_1: DAC1 Channel1 or DAC2 Channel1 selected
  *            @arg DAC_CHANNEL_2: DAC1 Channel2 selected
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DAC_Start(DAC_HandleTypeDef* hdac, uint32_t Channel)
{
 8001cec:	b480      	push	{r7}
 8001cee:	b083      	sub	sp, #12
 8001cf0:	af00      	add	r7, sp, #0
 8001cf2:	6078      	str	r0, [r7, #4]
 8001cf4:	6039      	str	r1, [r7, #0]
  /* Check the parameters */
  assert_param(IS_DAC_CHANNEL_INSTANCE(hdac->Instance, Channel));
  
  /* Process locked */
  __HAL_LOCK(hdac);
 8001cf6:	687b      	ldr	r3, [r7, #4]
 8001cf8:	795b      	ldrb	r3, [r3, #5]
 8001cfa:	2b01      	cmp	r3, #1
 8001cfc:	d101      	bne.n	8001d02 <HAL_DAC_Start+0x16>
 8001cfe:	2302      	movs	r3, #2
 8001d00:	e039      	b.n	8001d76 <HAL_DAC_Start+0x8a>
 8001d02:	687b      	ldr	r3, [r7, #4]
 8001d04:	2201      	movs	r2, #1
 8001d06:	715a      	strb	r2, [r3, #5]
  
  /* Change DAC state */
  hdac->State = HAL_DAC_STATE_BUSY;
 8001d08:	687b      	ldr	r3, [r7, #4]
 8001d0a:	2202      	movs	r2, #2
 8001d0c:	711a      	strb	r2, [r3, #4]
  
  /* Enable the Peripheral */
  __HAL_DAC_ENABLE(hdac, Channel);
 8001d0e:	687b      	ldr	r3, [r7, #4]
 8001d10:	681b      	ldr	r3, [r3, #0]
 8001d12:	6819      	ldr	r1, [r3, #0]
 8001d14:	2201      	movs	r2, #1
 8001d16:	683b      	ldr	r3, [r7, #0]
 8001d18:	409a      	lsls	r2, r3
 8001d1a:	687b      	ldr	r3, [r7, #4]
 8001d1c:	681b      	ldr	r3, [r3, #0]
 8001d1e:	430a      	orrs	r2, r1
 8001d20:	601a      	str	r2, [r3, #0]
  
  if(Channel == DAC_CHANNEL_1)
 8001d22:	683b      	ldr	r3, [r7, #0]
 8001d24:	2b00      	cmp	r3, #0
 8001d26:	d10f      	bne.n	8001d48 <HAL_DAC_Start+0x5c>
  {
    /* Check if software trigger enabled */
    if((hdac->Instance->CR & (DAC_CR_TEN1 | DAC_CR_TSEL1)) == (DAC_CR_TEN1 | DAC_CR_TSEL1))
 8001d28:	687b      	ldr	r3, [r7, #4]
 8001d2a:	681b      	ldr	r3, [r3, #0]
 8001d2c:	681b      	ldr	r3, [r3, #0]
 8001d2e:	f003 033c 	and.w	r3, r3, #60	@ 0x3c
 8001d32:	2b3c      	cmp	r3, #60	@ 0x3c
 8001d34:	d118      	bne.n	8001d68 <HAL_DAC_Start+0x7c>
    {
      /* Enable the selected DAC software conversion */
      SET_BIT(hdac->Instance->SWTRIGR, DAC_SWTRIGR_SWTRIG1);
 8001d36:	687b      	ldr	r3, [r7, #4]
 8001d38:	681b      	ldr	r3, [r3, #0]
 8001d3a:	685a      	ldr	r2, [r3, #4]
 8001d3c:	687b      	ldr	r3, [r7, #4]
 8001d3e:	681b      	ldr	r3, [r3, #0]
 8001d40:	f042 0201 	orr.w	r2, r2, #1
 8001d44:	605a      	str	r2, [r3, #4]
 8001d46:	e00f      	b.n	8001d68 <HAL_DAC_Start+0x7c>
    }
  }
  else
  {
    /* Check if software trigger enabled */
    if((hdac->Instance->CR & (DAC_CR_TEN2 | DAC_CR_TSEL2)) == (DAC_CR_TEN2 | DAC_CR_TSEL2))
 8001d48:	687b      	ldr	r3, [r7, #4]
 8001d4a:	681b      	ldr	r3, [r3, #0]
 8001d4c:	681b      	ldr	r3, [r3, #0]
 8001d4e:	f403 1370 	and.w	r3, r3, #3932160	@ 0x3c0000
 8001d52:	f5b3 1f70 	cmp.w	r3, #3932160	@ 0x3c0000
 8001d56:	d107      	bne.n	8001d68 <HAL_DAC_Start+0x7c>
    {
      /* Enable the selected DAC software conversion */
      SET_BIT(hdac->Instance->SWTRIGR, DAC_SWTRIGR_SWTRIG2);
 8001d58:	687b      	ldr	r3, [r7, #4]
 8001d5a:	681b      	ldr	r3, [r3, #0]
 8001d5c:	685a      	ldr	r2, [r3, #4]
 8001d5e:	687b      	ldr	r3, [r7, #4]
 8001d60:	681b      	ldr	r3, [r3, #0]
 8001d62:	f042 0202 	orr.w	r2, r2, #2
 8001d66:	605a      	str	r2, [r3, #4]
    }
  }
  
  /* Change DAC state */
  hdac->State = HAL_DAC_STATE_READY;
 8001d68:	687b      	ldr	r3, [r7, #4]
 8001d6a:	2201      	movs	r2, #1
 8001d6c:	711a      	strb	r2, [r3, #4]
  
  /* Process unlocked */
  __HAL_UNLOCK(hdac);
 8001d6e:	687b      	ldr	r3, [r7, #4]
 8001d70:	2200      	movs	r2, #0
 8001d72:	715a      	strb	r2, [r3, #5]
    
  /* Return function status */
  return HAL_OK;
 8001d74:	2300      	movs	r3, #0
}
 8001d76:	4618      	mov	r0, r3
 8001d78:	370c      	adds	r7, #12
 8001d7a:	46bd      	mov	sp, r7
 8001d7c:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001d80:	4770      	bx	lr

08001d82 <HAL_DAC_ConfigChannel>:
  *            @arg DAC_CHANNEL_1: DAC2 Channel1 selected 
  * @retval HAL status
  */

HAL_StatusTypeDef HAL_DAC_ConfigChannel(DAC_HandleTypeDef* hdac, DAC_ChannelConfTypeDef* sConfig, uint32_t Channel)
{
 8001d82:	b480      	push	{r7}
 8001d84:	b087      	sub	sp, #28
 8001d86:	af00      	add	r7, sp, #0
 8001d88:	60f8      	str	r0, [r7, #12]
 8001d8a:	60b9      	str	r1, [r7, #8]
 8001d8c:	607a      	str	r2, [r7, #4]
  uint32_t tmpreg1 = 0U, tmpreg2 = 0U;
 8001d8e:	2300      	movs	r3, #0
 8001d90:	617b      	str	r3, [r7, #20]
 8001d92:	2300      	movs	r3, #0
 8001d94:	613b      	str	r3, [r7, #16]
  assert_param(IS_DAC_OUTPUT_BUFFER_STATE(sConfig->DAC_OutputBuffer));    
#endif /* STM32F303x8 || STM32F334x8 || STM32F328xx || */
  assert_param(IS_DAC_CHANNEL(Channel));   
 
  /* Process locked */
  __HAL_LOCK(hdac);
 8001d96:	68fb      	ldr	r3, [r7, #12]
 8001d98:	795b      	ldrb	r3, [r3, #5]
 8001d9a:	2b01      	cmp	r3, #1
 8001d9c:	d101      	bne.n	8001da2 <HAL_DAC_ConfigChannel+0x20>
 8001d9e:	2302      	movs	r3, #2
 8001da0:	e036      	b.n	8001e10 <HAL_DAC_ConfigChannel+0x8e>
 8001da2:	68fb      	ldr	r3, [r7, #12]
 8001da4:	2201      	movs	r2, #1
 8001da6:	715a      	strb	r2, [r3, #5]
  
  /* Change DAC state */
  hdac->State = HAL_DAC_STATE_BUSY;
 8001da8:	68fb      	ldr	r3, [r7, #12]
 8001daa:	2202      	movs	r2, #2
 8001dac:	711a      	strb	r2, [r3, #4]
  
  /* Get the DAC CR value */
  tmpreg1 = hdac->Instance->CR;
 8001dae:	68fb      	ldr	r3, [r7, #12]
 8001db0:	681b      	ldr	r3, [r3, #0]
 8001db2:	681b      	ldr	r3, [r3, #0]
 8001db4:	617b      	str	r3, [r7, #20]
    /* Output Switch (OUTEN) control */
    tmpreg1 &= ~(((uint32_t)(DAC_CR_MAMP1 | DAC_CR_WAVE1 | DAC_CR_TSEL1 | DAC_CR_TEN1 | DAC_CR_OUTEN1)) << Channel);    
    tmpreg2 = (sConfig->DAC_Trigger | sConfig->DAC_OutputSwitch);    
  }    
#else
  tmpreg1 &= ~(((uint32_t)(DAC_CR_MAMP1 | DAC_CR_WAVE1 | DAC_CR_TSEL1 | DAC_CR_TEN1 | DAC_CR_BOFF1)) << Channel);
 8001db6:	f640 72fe 	movw	r2, #4094	@ 0xffe
 8001dba:	687b      	ldr	r3, [r7, #4]
 8001dbc:	fa02 f303 	lsl.w	r3, r2, r3
 8001dc0:	43db      	mvns	r3, r3
 8001dc2:	697a      	ldr	r2, [r7, #20]
 8001dc4:	4013      	ands	r3, r2
 8001dc6:	617b      	str	r3, [r7, #20]
  tmpreg2 = (sConfig->DAC_Trigger | sConfig->DAC_OutputBuffer);
 8001dc8:	68bb      	ldr	r3, [r7, #8]
 8001dca:	681a      	ldr	r2, [r3, #0]
 8001dcc:	68bb      	ldr	r3, [r7, #8]
 8001dce:	685b      	ldr	r3, [r3, #4]
 8001dd0:	4313      	orrs	r3, r2
 8001dd2:	613b      	str	r3, [r7, #16]
#endif  /* STM32F303x8 || STM32F334x8 || STM32F328xx || */
  
  /* Calculate CR register value depending on DAC_Channel */
  tmpreg1 |= tmpreg2 << Channel;
 8001dd4:	693a      	ldr	r2, [r7, #16]
 8001dd6:	687b      	ldr	r3, [r7, #4]
 8001dd8:	fa02 f303 	lsl.w	r3, r2, r3
 8001ddc:	697a      	ldr	r2, [r7, #20]
 8001dde:	4313      	orrs	r3, r2
 8001de0:	617b      	str	r3, [r7, #20]
  /* Write to DAC CR */
  hdac->Instance->CR = tmpreg1;
 8001de2:	68fb      	ldr	r3, [r7, #12]
 8001de4:	681b      	ldr	r3, [r3, #0]
 8001de6:	697a      	ldr	r2, [r7, #20]
 8001de8:	601a      	str	r2, [r3, #0]
  
  /* Disable wave generation */
  hdac->Instance->CR &= ~(DAC_CR_WAVE1 << Channel);
 8001dea:	68fb      	ldr	r3, [r7, #12]
 8001dec:	681b      	ldr	r3, [r3, #0]
 8001dee:	6819      	ldr	r1, [r3, #0]
 8001df0:	22c0      	movs	r2, #192	@ 0xc0
 8001df2:	687b      	ldr	r3, [r7, #4]
 8001df4:	fa02 f303 	lsl.w	r3, r2, r3
 8001df8:	43da      	mvns	r2, r3
 8001dfa:	68fb      	ldr	r3, [r7, #12]
 8001dfc:	681b      	ldr	r3, [r3, #0]
 8001dfe:	400a      	ands	r2, r1
 8001e00:	601a      	str	r2, [r3, #0]

  /* Change DAC state */
  hdac->State = HAL_DAC_STATE_READY;
 8001e02:	68fb      	ldr	r3, [r7, #12]
 8001e04:	2201      	movs	r2, #1
 8001e06:	711a      	strb	r2, [r3, #4]
  
  /* Process unlocked */
  __HAL_UNLOCK(hdac);
 8001e08:	68fb      	ldr	r3, [r7, #12]
 8001e0a:	2200      	movs	r2, #0
 8001e0c:	715a      	strb	r2, [r3, #5]
  
  /* Return function status */
  return HAL_OK;
 8001e0e:	2300      	movs	r3, #0
}
 8001e10:	4618      	mov	r0, r3
 8001e12:	371c      	adds	r7, #28
 8001e14:	46bd      	mov	sp, r7
 8001e16:	f85d 7b04 	ldr.w	r7, [sp], #4
 8001e1a:	4770      	bx	lr

08001e1c <HAL_DMA_Init>:
  * @param  hdma Pointer to a DMA_HandleTypeDef structure that contains
  *               the configuration information for the specified DMA Channel.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA_Init(DMA_HandleTypeDef *hdma)
{
 8001e1c:	b580      	push	{r7, lr}
 8001e1e:	b084      	sub	sp, #16
 8001e20:	af00      	add	r7, sp, #0
 8001e22:	6078      	str	r0, [r7, #4]
  uint32_t tmp = 0U;
 8001e24:	2300      	movs	r3, #0
 8001e26:	60fb      	str	r3, [r7, #12]

  /* Check the DMA handle allocation */
  if(NULL == hdma)
 8001e28:	687b      	ldr	r3, [r7, #4]
 8001e2a:	2b00      	cmp	r3, #0
 8001e2c:	d101      	bne.n	8001e32 <HAL_DMA_Init+0x16>
  {
    return HAL_ERROR;
 8001e2e:	2301      	movs	r3, #1
 8001e30:	e037      	b.n	8001ea2 <HAL_DMA_Init+0x86>
  assert_param(IS_DMA_MEMORY_DATA_SIZE(hdma->Init.MemDataAlignment));
  assert_param(IS_DMA_MODE(hdma->Init.Mode));
  assert_param(IS_DMA_PRIORITY(hdma->Init.Priority));

  /* Change DMA peripheral state */
  hdma->State = HAL_DMA_STATE_BUSY;
 8001e32:	687b      	ldr	r3, [r7, #4]
 8001e34:	2202      	movs	r2, #2
 8001e36:	f883 2021 	strb.w	r2, [r3, #33]	@ 0x21

  /* Get the CR register value */
  tmp = hdma->Instance->CCR;
 8001e3a:	687b      	ldr	r3, [r7, #4]
 8001e3c:	681b      	ldr	r3, [r3, #0]
 8001e3e:	681b      	ldr	r3, [r3, #0]
 8001e40:	60fb      	str	r3, [r7, #12]

  /* Clear PL, MSIZE, PSIZE, MINC, PINC, CIRC, DIR bits */
  tmp &= ((uint32_t)~(DMA_CCR_PL    | DMA_CCR_MSIZE  | DMA_CCR_PSIZE  | \
 8001e42:	68fb      	ldr	r3, [r7, #12]
 8001e44:	f423 537f 	bic.w	r3, r3, #16320	@ 0x3fc0
 8001e48:	f023 0330 	bic.w	r3, r3, #48	@ 0x30
 8001e4c:	60fb      	str	r3, [r7, #12]
                      DMA_CCR_MINC  | DMA_CCR_PINC   | DMA_CCR_CIRC   | \
                      DMA_CCR_DIR));

  /* Prepare the DMA Channel configuration */
  tmp |=  hdma->Init.Direction        |
 8001e4e:	687b      	ldr	r3, [r7, #4]
 8001e50:	685a      	ldr	r2, [r3, #4]
          hdma->Init.PeriphInc           | hdma->Init.MemInc           |
 8001e52:	687b      	ldr	r3, [r7, #4]
 8001e54:	689b      	ldr	r3, [r3, #8]
  tmp |=  hdma->Init.Direction        |
 8001e56:	431a      	orrs	r2, r3
          hdma->Init.PeriphInc           | hdma->Init.MemInc           |
 8001e58:	687b      	ldr	r3, [r7, #4]
 8001e5a:	68db      	ldr	r3, [r3, #12]
 8001e5c:	431a      	orrs	r2, r3
          hdma->Init.PeriphDataAlignment | hdma->Init.MemDataAlignment |
 8001e5e:	687b      	ldr	r3, [r7, #4]
 8001e60:	691b      	ldr	r3, [r3, #16]
          hdma->Init.PeriphInc           | hdma->Init.MemInc           |
 8001e62:	431a      	orrs	r2, r3
          hdma->Init.PeriphDataAlignment | hdma->Init.MemDataAlignment |
 8001e64:	687b      	ldr	r3, [r7, #4]
 8001e66:	695b      	ldr	r3, [r3, #20]
 8001e68:	431a      	orrs	r2, r3
          hdma->Init.Mode                | hdma->Init.Priority;
 8001e6a:	687b      	ldr	r3, [r7, #4]
 8001e6c:	699b      	ldr	r3, [r3, #24]
          hdma->Init.PeriphDataAlignment | hdma->Init.MemDataAlignment |
 8001e6e:	431a      	orrs	r2, r3
          hdma->Init.Mode                | hdma->Init.Priority;
 8001e70:	687b      	ldr	r3, [r7, #4]
 8001e72:	69db      	ldr	r3, [r3, #28]
 8001e74:	4313      	orrs	r3, r2
  tmp |=  hdma->Init.Direction        |
 8001e76:	68fa      	ldr	r2, [r7, #12]
 8001e78:	4313      	orrs	r3, r2
 8001e7a:	60fb      	str	r3, [r7, #12]

  /* Write to DMA Channel CR register */
  hdma->Instance->CCR = tmp;
 8001e7c:	687b      	ldr	r3, [r7, #4]
 8001e7e:	681b      	ldr	r3, [r3, #0]
 8001e80:	68fa      	ldr	r2, [r7, #12]
 8001e82:	601a      	str	r2, [r3, #0]

  /* Initialize DmaBaseAddress and ChannelIndex parameters used
     by HAL_DMA_IRQHandler() and HAL_DMA_PollForTransfer() */
  DMA_CalcBaseAndBitshift(hdma);
 8001e84:	6878      	ldr	r0, [r7, #4]
 8001e86:	f000 f941 	bl	800210c <DMA_CalcBaseAndBitshift>

  /* Initialise the error code */
  hdma->ErrorCode = HAL_DMA_ERROR_NONE;
 8001e8a:	687b      	ldr	r3, [r7, #4]
 8001e8c:	2200      	movs	r2, #0
 8001e8e:	639a      	str	r2, [r3, #56]	@ 0x38

  /* Initialize the DMA state*/
  hdma->State = HAL_DMA_STATE_READY;
 8001e90:	687b      	ldr	r3, [r7, #4]
 8001e92:	2201      	movs	r2, #1
 8001e94:	f883 2021 	strb.w	r2, [r3, #33]	@ 0x21

  /* Allocate lock resource and initialize it */
  hdma->Lock = HAL_UNLOCKED;
 8001e98:	687b      	ldr	r3, [r7, #4]
 8001e9a:	2200      	movs	r2, #0
 8001e9c:	f883 2020 	strb.w	r2, [r3, #32]

  return HAL_OK;
 8001ea0:	2300      	movs	r3, #0
}
 8001ea2:	4618      	mov	r0, r3
 8001ea4:	3710      	adds	r7, #16
 8001ea6:	46bd      	mov	sp, r7
 8001ea8:	bd80      	pop	{r7, pc}

08001eaa <HAL_DMA_Start_IT>:
  * @param  DstAddress The destination memory Buffer address
  * @param  DataLength The length of data to be transferred from source to destination
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA_Start_IT(DMA_HandleTypeDef *hdma, uint32_t SrcAddress, uint32_t DstAddress, uint32_t DataLength)
{
 8001eaa:	b580      	push	{r7, lr}
 8001eac:	b086      	sub	sp, #24
 8001eae:	af00      	add	r7, sp, #0
 8001eb0:	60f8      	str	r0, [r7, #12]
 8001eb2:	60b9      	str	r1, [r7, #8]
 8001eb4:	607a      	str	r2, [r7, #4]
 8001eb6:	603b      	str	r3, [r7, #0]
  HAL_StatusTypeDef status = HAL_OK;
 8001eb8:	2300      	movs	r3, #0
 8001eba:	75fb      	strb	r3, [r7, #23]

  /* Check the parameters */
  assert_param(IS_DMA_BUFFER_SIZE(DataLength));

  /* Process locked */
  __HAL_LOCK(hdma);
 8001ebc:	68fb      	ldr	r3, [r7, #12]
 8001ebe:	f893 3020 	ldrb.w	r3, [r3, #32]
 8001ec2:	2b01      	cmp	r3, #1
 8001ec4:	d101      	bne.n	8001eca <HAL_DMA_Start_IT+0x20>
 8001ec6:	2302      	movs	r3, #2
 8001ec8:	e04a      	b.n	8001f60 <HAL_DMA_Start_IT+0xb6>
 8001eca:	68fb      	ldr	r3, [r7, #12]
 8001ecc:	2201      	movs	r2, #1
 8001ece:	f883 2020 	strb.w	r2, [r3, #32]

  if(HAL_DMA_STATE_READY == hdma->State)
 8001ed2:	68fb      	ldr	r3, [r7, #12]
 8001ed4:	f893 3021 	ldrb.w	r3, [r3, #33]	@ 0x21
 8001ed8:	2b01      	cmp	r3, #1
 8001eda:	d13a      	bne.n	8001f52 <HAL_DMA_Start_IT+0xa8>
  {
    /* Change DMA peripheral state */
    hdma->State = HAL_DMA_STATE_BUSY;
 8001edc:	68fb      	ldr	r3, [r7, #12]
 8001ede:	2202      	movs	r2, #2
 8001ee0:	f883 2021 	strb.w	r2, [r3, #33]	@ 0x21

    hdma->ErrorCode = HAL_DMA_ERROR_NONE;
 8001ee4:	68fb      	ldr	r3, [r7, #12]
 8001ee6:	2200      	movs	r2, #0
 8001ee8:	639a      	str	r2, [r3, #56]	@ 0x38

    /* Disable the peripheral */
    hdma->Instance->CCR &= ~DMA_CCR_EN;
 8001eea:	68fb      	ldr	r3, [r7, #12]
 8001eec:	681b      	ldr	r3, [r3, #0]
 8001eee:	681a      	ldr	r2, [r3, #0]
 8001ef0:	68fb      	ldr	r3, [r7, #12]
 8001ef2:	681b      	ldr	r3, [r3, #0]
 8001ef4:	f022 0201 	bic.w	r2, r2, #1
 8001ef8:	601a      	str	r2, [r3, #0]

    /* Configure the source, destination address and the data length */
    DMA_SetConfig(hdma, SrcAddress, DstAddress, DataLength);
 8001efa:	683b      	ldr	r3, [r7, #0]
 8001efc:	687a      	ldr	r2, [r7, #4]
 8001efe:	68b9      	ldr	r1, [r7, #8]
 8001f00:	68f8      	ldr	r0, [r7, #12]
 8001f02:	f000 f8d4 	bl	80020ae <DMA_SetConfig>

    /* Enable the transfer complete, & transfer error interrupts */
    /* Half transfer interrupt is optional: enable it only if associated callback is available */
    if(NULL != hdma->XferHalfCpltCallback )
 8001f06:	68fb      	ldr	r3, [r7, #12]
 8001f08:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8001f0a:	2b00      	cmp	r3, #0
 8001f0c:	d008      	beq.n	8001f20 <HAL_DMA_Start_IT+0x76>
    {
      hdma->Instance->CCR |= (DMA_IT_TC | DMA_IT_HT | DMA_IT_TE);
 8001f0e:	68fb      	ldr	r3, [r7, #12]
 8001f10:	681b      	ldr	r3, [r3, #0]
 8001f12:	681a      	ldr	r2, [r3, #0]
 8001f14:	68fb      	ldr	r3, [r7, #12]
 8001f16:	681b      	ldr	r3, [r3, #0]
 8001f18:	f042 020e 	orr.w	r2, r2, #14
 8001f1c:	601a      	str	r2, [r3, #0]
 8001f1e:	e00f      	b.n	8001f40 <HAL_DMA_Start_IT+0x96>
    }
    else
    {
      hdma->Instance->CCR |= (DMA_IT_TC | DMA_IT_TE);
 8001f20:	68fb      	ldr	r3, [r7, #12]
 8001f22:	681b      	ldr	r3, [r3, #0]
 8001f24:	681a      	ldr	r2, [r3, #0]
 8001f26:	68fb      	ldr	r3, [r7, #12]
 8001f28:	681b      	ldr	r3, [r3, #0]
 8001f2a:	f042 020a 	orr.w	r2, r2, #10
 8001f2e:	601a      	str	r2, [r3, #0]
      hdma->Instance->CCR &= ~DMA_IT_HT;
 8001f30:	68fb      	ldr	r3, [r7, #12]
 8001f32:	681b      	ldr	r3, [r3, #0]
 8001f34:	681a      	ldr	r2, [r3, #0]
 8001f36:	68fb      	ldr	r3, [r7, #12]
 8001f38:	681b      	ldr	r3, [r3, #0]
 8001f3a:	f022 0204 	bic.w	r2, r2, #4
 8001f3e:	601a      	str	r2, [r3, #0]
    }

    /* Enable the Peripheral */
    hdma->Instance->CCR |= DMA_CCR_EN;
 8001f40:	68fb      	ldr	r3, [r7, #12]
 8001f42:	681b      	ldr	r3, [r3, #0]
 8001f44:	681a      	ldr	r2, [r3, #0]
 8001f46:	68fb      	ldr	r3, [r7, #12]
 8001f48:	681b      	ldr	r3, [r3, #0]
 8001f4a:	f042 0201 	orr.w	r2, r2, #1
 8001f4e:	601a      	str	r2, [r3, #0]
 8001f50:	e005      	b.n	8001f5e <HAL_DMA_Start_IT+0xb4>
  }
  else
  {
    /* Process Unlocked */
    __HAL_UNLOCK(hdma);
 8001f52:	68fb      	ldr	r3, [r7, #12]
 8001f54:	2200      	movs	r2, #0
 8001f56:	f883 2020 	strb.w	r2, [r3, #32]

    /* Remain BUSY */
    status = HAL_BUSY;
 8001f5a:	2302      	movs	r3, #2
 8001f5c:	75fb      	strb	r3, [r7, #23]
  }

  return status;
 8001f5e:	7dfb      	ldrb	r3, [r7, #23]
}
 8001f60:	4618      	mov	r0, r3
 8001f62:	3718      	adds	r7, #24
 8001f64:	46bd      	mov	sp, r7
 8001f66:	bd80      	pop	{r7, pc}

08001f68 <HAL_DMA_IRQHandler>:
  * @param  hdma pointer to a DMA_HandleTypeDef structure that contains
  *               the configuration information for the specified DMA Channel.
  * @retval None
  */
void HAL_DMA_IRQHandler(DMA_HandleTypeDef *hdma)
{
 8001f68:	b580      	push	{r7, lr}
 8001f6a:	b084      	sub	sp, #16
 8001f6c:	af00      	add	r7, sp, #0
 8001f6e:	6078      	str	r0, [r7, #4]
  uint32_t flag_it = hdma->DmaBaseAddress->ISR;
 8001f70:	687b      	ldr	r3, [r7, #4]
 8001f72:	6bdb      	ldr	r3, [r3, #60]	@ 0x3c
 8001f74:	681b      	ldr	r3, [r3, #0]
 8001f76:	60fb      	str	r3, [r7, #12]
  uint32_t source_it = hdma->Instance->CCR;
 8001f78:	687b      	ldr	r3, [r7, #4]
 8001f7a:	681b      	ldr	r3, [r3, #0]
 8001f7c:	681b      	ldr	r3, [r3, #0]
 8001f7e:	60bb      	str	r3, [r7, #8]

  /* Half Transfer Complete Interrupt management ******************************/
  if ((RESET != (flag_it & (DMA_FLAG_HT1 << hdma->ChannelIndex))) && (RESET != (source_it & DMA_IT_HT)))
 8001f80:	687b      	ldr	r3, [r7, #4]
 8001f82:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8001f84:	2204      	movs	r2, #4
 8001f86:	409a      	lsls	r2, r3
 8001f88:	68fb      	ldr	r3, [r7, #12]
 8001f8a:	4013      	ands	r3, r2
 8001f8c:	2b00      	cmp	r3, #0
 8001f8e:	d024      	beq.n	8001fda <HAL_DMA_IRQHandler+0x72>
 8001f90:	68bb      	ldr	r3, [r7, #8]
 8001f92:	f003 0304 	and.w	r3, r3, #4
 8001f96:	2b00      	cmp	r3, #0
 8001f98:	d01f      	beq.n	8001fda <HAL_DMA_IRQHandler+0x72>
  {
    /* Disable the half transfer interrupt if the DMA mode is not CIRCULAR */
    if((hdma->Instance->CCR & DMA_CCR_CIRC) == 0U)
 8001f9a:	687b      	ldr	r3, [r7, #4]
 8001f9c:	681b      	ldr	r3, [r3, #0]
 8001f9e:	681b      	ldr	r3, [r3, #0]
 8001fa0:	f003 0320 	and.w	r3, r3, #32
 8001fa4:	2b00      	cmp	r3, #0
 8001fa6:	d107      	bne.n	8001fb8 <HAL_DMA_IRQHandler+0x50>
    {
      /* Disable the half transfer interrupt */
      hdma->Instance->CCR &= ~DMA_IT_HT;
 8001fa8:	687b      	ldr	r3, [r7, #4]
 8001faa:	681b      	ldr	r3, [r3, #0]
 8001fac:	681a      	ldr	r2, [r3, #0]
 8001fae:	687b      	ldr	r3, [r7, #4]
 8001fb0:	681b      	ldr	r3, [r3, #0]
 8001fb2:	f022 0204 	bic.w	r2, r2, #4
 8001fb6:	601a      	str	r2, [r3, #0]
    }

    /* Clear the half transfer complete flag */
    hdma->DmaBaseAddress->IFCR = DMA_FLAG_HT1 << hdma->ChannelIndex;
 8001fb8:	687b      	ldr	r3, [r7, #4]
 8001fba:	6c1a      	ldr	r2, [r3, #64]	@ 0x40
 8001fbc:	687b      	ldr	r3, [r7, #4]
 8001fbe:	6bdb      	ldr	r3, [r3, #60]	@ 0x3c
 8001fc0:	2104      	movs	r1, #4
 8001fc2:	fa01 f202 	lsl.w	r2, r1, r2
 8001fc6:	605a      	str	r2, [r3, #4]

    /* DMA peripheral state is not updated in Half Transfer */
    /* State is updated only in Transfer Complete case */

    if(hdma->XferHalfCpltCallback != NULL)
 8001fc8:	687b      	ldr	r3, [r7, #4]
 8001fca:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8001fcc:	2b00      	cmp	r3, #0
 8001fce:	d06a      	beq.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
    {
      /* Half transfer callback */
      hdma->XferHalfCpltCallback(hdma);
 8001fd0:	687b      	ldr	r3, [r7, #4]
 8001fd2:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8001fd4:	6878      	ldr	r0, [r7, #4]
 8001fd6:	4798      	blx	r3
    if(hdma->XferHalfCpltCallback != NULL)
 8001fd8:	e065      	b.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
    }
  }

  /* Transfer Complete Interrupt management ***********************************/
  else if ((RESET != (flag_it & (DMA_FLAG_TC1 << hdma->ChannelIndex))) && (RESET != (source_it & DMA_IT_TC)))
 8001fda:	687b      	ldr	r3, [r7, #4]
 8001fdc:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8001fde:	2202      	movs	r2, #2
 8001fe0:	409a      	lsls	r2, r3
 8001fe2:	68fb      	ldr	r3, [r7, #12]
 8001fe4:	4013      	ands	r3, r2
 8001fe6:	2b00      	cmp	r3, #0
 8001fe8:	d02c      	beq.n	8002044 <HAL_DMA_IRQHandler+0xdc>
 8001fea:	68bb      	ldr	r3, [r7, #8]
 8001fec:	f003 0302 	and.w	r3, r3, #2
 8001ff0:	2b00      	cmp	r3, #0
 8001ff2:	d027      	beq.n	8002044 <HAL_DMA_IRQHandler+0xdc>
  {
    if((hdma->Instance->CCR & DMA_CCR_CIRC) == 0U)
 8001ff4:	687b      	ldr	r3, [r7, #4]
 8001ff6:	681b      	ldr	r3, [r3, #0]
 8001ff8:	681b      	ldr	r3, [r3, #0]
 8001ffa:	f003 0320 	and.w	r3, r3, #32
 8001ffe:	2b00      	cmp	r3, #0
 8002000:	d10b      	bne.n	800201a <HAL_DMA_IRQHandler+0xb2>
    {
      /* Disable the transfer complete  & transfer error interrupts */
      /* if the DMA mode is not CIRCULAR */
      hdma->Instance->CCR &= ~(DMA_IT_TC | DMA_IT_TE);
 8002002:	687b      	ldr	r3, [r7, #4]
 8002004:	681b      	ldr	r3, [r3, #0]
 8002006:	681a      	ldr	r2, [r3, #0]
 8002008:	687b      	ldr	r3, [r7, #4]
 800200a:	681b      	ldr	r3, [r3, #0]
 800200c:	f022 020a 	bic.w	r2, r2, #10
 8002010:	601a      	str	r2, [r3, #0]

      /* Change the DMA state */
      hdma->State = HAL_DMA_STATE_READY;
 8002012:	687b      	ldr	r3, [r7, #4]
 8002014:	2201      	movs	r2, #1
 8002016:	f883 2021 	strb.w	r2, [r3, #33]	@ 0x21
    }

    /* Clear the transfer complete flag */
    hdma->DmaBaseAddress->IFCR = DMA_FLAG_TC1 << hdma->ChannelIndex;
 800201a:	687b      	ldr	r3, [r7, #4]
 800201c:	6c1a      	ldr	r2, [r3, #64]	@ 0x40
 800201e:	687b      	ldr	r3, [r7, #4]
 8002020:	6bdb      	ldr	r3, [r3, #60]	@ 0x3c
 8002022:	2102      	movs	r1, #2
 8002024:	fa01 f202 	lsl.w	r2, r1, r2
 8002028:	605a      	str	r2, [r3, #4]

    /* Process Unlocked */
    __HAL_UNLOCK(hdma);
 800202a:	687b      	ldr	r3, [r7, #4]
 800202c:	2200      	movs	r2, #0
 800202e:	f883 2020 	strb.w	r2, [r3, #32]

    if(hdma->XferCpltCallback != NULL)
 8002032:	687b      	ldr	r3, [r7, #4]
 8002034:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8002036:	2b00      	cmp	r3, #0
 8002038:	d035      	beq.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
    {
      /* Transfer complete callback */
      hdma->XferCpltCallback(hdma);
 800203a:	687b      	ldr	r3, [r7, #4]
 800203c:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 800203e:	6878      	ldr	r0, [r7, #4]
 8002040:	4798      	blx	r3
    if(hdma->XferCpltCallback != NULL)
 8002042:	e030      	b.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
    }
  }

  /* Transfer Error Interrupt management ***************************************/
  else if (( RESET != (flag_it & (DMA_FLAG_TE1 << hdma->ChannelIndex))) && (RESET != (source_it & DMA_IT_TE)))
 8002044:	687b      	ldr	r3, [r7, #4]
 8002046:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8002048:	2208      	movs	r2, #8
 800204a:	409a      	lsls	r2, r3
 800204c:	68fb      	ldr	r3, [r7, #12]
 800204e:	4013      	ands	r3, r2
 8002050:	2b00      	cmp	r3, #0
 8002052:	d028      	beq.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
 8002054:	68bb      	ldr	r3, [r7, #8]
 8002056:	f003 0308 	and.w	r3, r3, #8
 800205a:	2b00      	cmp	r3, #0
 800205c:	d023      	beq.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
  {
    /* When a DMA transfer error occurs */
    /* A hardware clear of its EN bits is performed */
    /* Then, disable all DMA interrupts */
    hdma->Instance->CCR &= ~(DMA_IT_TC | DMA_IT_HT | DMA_IT_TE);
 800205e:	687b      	ldr	r3, [r7, #4]
 8002060:	681b      	ldr	r3, [r3, #0]
 8002062:	681a      	ldr	r2, [r3, #0]
 8002064:	687b      	ldr	r3, [r7, #4]
 8002066:	681b      	ldr	r3, [r3, #0]
 8002068:	f022 020e 	bic.w	r2, r2, #14
 800206c:	601a      	str	r2, [r3, #0]

    /* Clear all flags */
    hdma->DmaBaseAddress->IFCR = DMA_FLAG_GL1 << hdma->ChannelIndex;
 800206e:	687b      	ldr	r3, [r7, #4]
 8002070:	6c1a      	ldr	r2, [r3, #64]	@ 0x40
 8002072:	687b      	ldr	r3, [r7, #4]
 8002074:	6bdb      	ldr	r3, [r3, #60]	@ 0x3c
 8002076:	2101      	movs	r1, #1
 8002078:	fa01 f202 	lsl.w	r2, r1, r2
 800207c:	605a      	str	r2, [r3, #4]

    /* Update error code */
    hdma->ErrorCode = HAL_DMA_ERROR_TE;
 800207e:	687b      	ldr	r3, [r7, #4]
 8002080:	2201      	movs	r2, #1
 8002082:	639a      	str	r2, [r3, #56]	@ 0x38

    /* Change the DMA state */
    hdma->State = HAL_DMA_STATE_READY;
 8002084:	687b      	ldr	r3, [r7, #4]
 8002086:	2201      	movs	r2, #1
 8002088:	f883 2021 	strb.w	r2, [r3, #33]	@ 0x21

    /* Process Unlocked */
    __HAL_UNLOCK(hdma);
 800208c:	687b      	ldr	r3, [r7, #4]
 800208e:	2200      	movs	r2, #0
 8002090:	f883 2020 	strb.w	r2, [r3, #32]

    if(hdma->XferErrorCallback != NULL)
 8002094:	687b      	ldr	r3, [r7, #4]
 8002096:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8002098:	2b00      	cmp	r3, #0
 800209a:	d004      	beq.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
    {
      /* Transfer error callback */
      hdma->XferErrorCallback(hdma);
 800209c:	687b      	ldr	r3, [r7, #4]
 800209e:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 80020a0:	6878      	ldr	r0, [r7, #4]
 80020a2:	4798      	blx	r3
    }
  }
}
 80020a4:	e7ff      	b.n	80020a6 <HAL_DMA_IRQHandler+0x13e>
 80020a6:	bf00      	nop
 80020a8:	3710      	adds	r7, #16
 80020aa:	46bd      	mov	sp, r7
 80020ac:	bd80      	pop	{r7, pc}

080020ae <DMA_SetConfig>:
  * @param  DstAddress The destination memory Buffer address
  * @param  DataLength The length of data to be transferred from source to destination
  * @retval HAL status
  */
static void DMA_SetConfig(DMA_HandleTypeDef *hdma, uint32_t SrcAddress, uint32_t DstAddress, uint32_t DataLength)
{
 80020ae:	b480      	push	{r7}
 80020b0:	b085      	sub	sp, #20
 80020b2:	af00      	add	r7, sp, #0
 80020b4:	60f8      	str	r0, [r7, #12]
 80020b6:	60b9      	str	r1, [r7, #8]
 80020b8:	607a      	str	r2, [r7, #4]
 80020ba:	603b      	str	r3, [r7, #0]
  /* Clear all flags */
  hdma->DmaBaseAddress->IFCR  = (DMA_FLAG_GL1 << hdma->ChannelIndex);
 80020bc:	68fb      	ldr	r3, [r7, #12]
 80020be:	6c1a      	ldr	r2, [r3, #64]	@ 0x40
 80020c0:	68fb      	ldr	r3, [r7, #12]
 80020c2:	6bdb      	ldr	r3, [r3, #60]	@ 0x3c
 80020c4:	2101      	movs	r1, #1
 80020c6:	fa01 f202 	lsl.w	r2, r1, r2
 80020ca:	605a      	str	r2, [r3, #4]

  /* Configure DMA Channel data length */
  hdma->Instance->CNDTR = DataLength;
 80020cc:	68fb      	ldr	r3, [r7, #12]
 80020ce:	681b      	ldr	r3, [r3, #0]
 80020d0:	683a      	ldr	r2, [r7, #0]
 80020d2:	605a      	str	r2, [r3, #4]

  /* Peripheral to Memory */
  if((hdma->Init.Direction) == DMA_MEMORY_TO_PERIPH)
 80020d4:	68fb      	ldr	r3, [r7, #12]
 80020d6:	685b      	ldr	r3, [r3, #4]
 80020d8:	2b10      	cmp	r3, #16
 80020da:	d108      	bne.n	80020ee <DMA_SetConfig+0x40>
  {
    /* Configure DMA Channel destination address */
    hdma->Instance->CPAR = DstAddress;
 80020dc:	68fb      	ldr	r3, [r7, #12]
 80020de:	681b      	ldr	r3, [r3, #0]
 80020e0:	687a      	ldr	r2, [r7, #4]
 80020e2:	609a      	str	r2, [r3, #8]

    /* Configure DMA Channel source address */
    hdma->Instance->CMAR = SrcAddress;
 80020e4:	68fb      	ldr	r3, [r7, #12]
 80020e6:	681b      	ldr	r3, [r3, #0]
 80020e8:	68ba      	ldr	r2, [r7, #8]
 80020ea:	60da      	str	r2, [r3, #12]
    hdma->Instance->CPAR = SrcAddress;

    /* Configure DMA Channel destination address */
    hdma->Instance->CMAR = DstAddress;
  }
}
 80020ec:	e007      	b.n	80020fe <DMA_SetConfig+0x50>
    hdma->Instance->CPAR = SrcAddress;
 80020ee:	68fb      	ldr	r3, [r7, #12]
 80020f0:	681b      	ldr	r3, [r3, #0]
 80020f2:	68ba      	ldr	r2, [r7, #8]
 80020f4:	609a      	str	r2, [r3, #8]
    hdma->Instance->CMAR = DstAddress;
 80020f6:	68fb      	ldr	r3, [r7, #12]
 80020f8:	681b      	ldr	r3, [r3, #0]
 80020fa:	687a      	ldr	r2, [r7, #4]
 80020fc:	60da      	str	r2, [r3, #12]
}
 80020fe:	bf00      	nop
 8002100:	3714      	adds	r7, #20
 8002102:	46bd      	mov	sp, r7
 8002104:	f85d 7b04 	ldr.w	r7, [sp], #4
 8002108:	4770      	bx	lr
	...

0800210c <DMA_CalcBaseAndBitshift>:
  * @param  hdma       pointer to a DMA_HandleTypeDef structure that contains
  *                     the configuration information for the specified DMA Stream.
  * @retval None
  */
static void DMA_CalcBaseAndBitshift(DMA_HandleTypeDef *hdma)
{
 800210c:	b480      	push	{r7}
 800210e:	b083      	sub	sp, #12
 8002110:	af00      	add	r7, sp, #0
 8002112:	6078      	str	r0, [r7, #4]
#if defined (DMA2)
  /* calculation of the channel index */
  if ((uint32_t)(hdma->Instance) < (uint32_t)(DMA2_Channel1))
 8002114:	687b      	ldr	r3, [r7, #4]
 8002116:	681b      	ldr	r3, [r3, #0]
 8002118:	461a      	mov	r2, r3
 800211a:	4b14      	ldr	r3, [pc, #80]	@ (800216c <DMA_CalcBaseAndBitshift+0x60>)
 800211c:	429a      	cmp	r2, r3
 800211e:	d80f      	bhi.n	8002140 <DMA_CalcBaseAndBitshift+0x34>
  {
    /* DMA1 */
    hdma->ChannelIndex = (((uint32_t)hdma->Instance - (uint32_t)DMA1_Channel1) / ((uint32_t)DMA1_Channel2 - (uint32_t)DMA1_Channel1)) << 2U;
 8002120:	687b      	ldr	r3, [r7, #4]
 8002122:	681b      	ldr	r3, [r3, #0]
 8002124:	461a      	mov	r2, r3
 8002126:	4b12      	ldr	r3, [pc, #72]	@ (8002170 <DMA_CalcBaseAndBitshift+0x64>)
 8002128:	4413      	add	r3, r2
 800212a:	4a12      	ldr	r2, [pc, #72]	@ (8002174 <DMA_CalcBaseAndBitshift+0x68>)
 800212c:	fba2 2303 	umull	r2, r3, r2, r3
 8002130:	091b      	lsrs	r3, r3, #4
 8002132:	009a      	lsls	r2, r3, #2
 8002134:	687b      	ldr	r3, [r7, #4]
 8002136:	641a      	str	r2, [r3, #64]	@ 0x40
    hdma->DmaBaseAddress = DMA1;
 8002138:	687b      	ldr	r3, [r7, #4]
 800213a:	4a0f      	ldr	r2, [pc, #60]	@ (8002178 <DMA_CalcBaseAndBitshift+0x6c>)
 800213c:	63da      	str	r2, [r3, #60]	@ 0x3c
  /* calculation of the channel index */
  /* DMA1 */
  hdma->ChannelIndex = (((uint32_t)hdma->Instance - (uint32_t)DMA1_Channel1) / ((uint32_t)DMA1_Channel2 - (uint32_t)DMA1_Channel1)) << 2U;
  hdma->DmaBaseAddress = DMA1;
#endif
}
 800213e:	e00e      	b.n	800215e <DMA_CalcBaseAndBitshift+0x52>
    hdma->ChannelIndex = (((uint32_t)hdma->Instance - (uint32_t)DMA2_Channel1) / ((uint32_t)DMA2_Channel2 - (uint32_t)DMA2_Channel1)) << 2U;
 8002140:	687b      	ldr	r3, [r7, #4]
 8002142:	681b      	ldr	r3, [r3, #0]
 8002144:	461a      	mov	r2, r3
 8002146:	4b0d      	ldr	r3, [pc, #52]	@ (800217c <DMA_CalcBaseAndBitshift+0x70>)
 8002148:	4413      	add	r3, r2
 800214a:	4a0a      	ldr	r2, [pc, #40]	@ (8002174 <DMA_CalcBaseAndBitshift+0x68>)
 800214c:	fba2 2303 	umull	r2, r3, r2, r3
 8002150:	091b      	lsrs	r3, r3, #4
 8002152:	009a      	lsls	r2, r3, #2
 8002154:	687b      	ldr	r3, [r7, #4]
 8002156:	641a      	str	r2, [r3, #64]	@ 0x40
    hdma->DmaBaseAddress = DMA2;
 8002158:	687b      	ldr	r3, [r7, #4]
 800215a:	4a09      	ldr	r2, [pc, #36]	@ (8002180 <DMA_CalcBaseAndBitshift+0x74>)
 800215c:	63da      	str	r2, [r3, #60]	@ 0x3c
}
 800215e:	bf00      	nop
 8002160:	370c      	adds	r7, #12
 8002162:	46bd      	mov	sp, r7
 8002164:	f85d 7b04 	ldr.w	r7, [sp], #4
 8002168:	4770      	bx	lr
 800216a:	bf00      	nop
 800216c:	40020407 	.word	0x40020407
 8002170:	bffdfff8 	.word	0xbffdfff8
 8002174:	cccccccd 	.word	0xcccccccd
 8002178:	40020000 	.word	0x40020000
 800217c:	bffdfbf8 	.word	0xbffdfbf8
 8002180:	40020400 	.word	0x40020400

08002184 <HAL_GPIO_Init>:
  * @param  GPIO_Init pointer to a GPIO_InitTypeDef structure that contains
  *         the configuration information for the specified GPIO peripheral.
  * @retval None
  */
void HAL_GPIO_Init(GPIO_TypeDef  *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
 8002184:	b480      	push	{r7}
 8002186:	b087      	sub	sp, #28
 8002188:	af00      	add	r7, sp, #0
 800218a:	6078      	str	r0, [r7, #4]
 800218c:	6039      	str	r1, [r7, #0]
  uint32_t position = 0x00u;
 800218e:	2300      	movs	r3, #0
 8002190:	617b      	str	r3, [r7, #20]
  assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
  assert_param(IS_GPIO_PIN(GPIO_Init->Pin));
  assert_param(IS_GPIO_MODE(GPIO_Init->Mode));

  /* Configure the port pins */
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 8002192:	e154      	b.n	800243e <HAL_GPIO_Init+0x2ba>
  {
    /* Get current io position */
    iocurrent = (GPIO_Init->Pin) & (1uL << position);
 8002194:	683b      	ldr	r3, [r7, #0]
 8002196:	681a      	ldr	r2, [r3, #0]
 8002198:	2101      	movs	r1, #1
 800219a:	697b      	ldr	r3, [r7, #20]
 800219c:	fa01 f303 	lsl.w	r3, r1, r3
 80021a0:	4013      	ands	r3, r2
 80021a2:	60fb      	str	r3, [r7, #12]

    if (iocurrent != 0x00u)
 80021a4:	68fb      	ldr	r3, [r7, #12]
 80021a6:	2b00      	cmp	r3, #0
 80021a8:	f000 8146 	beq.w	8002438 <HAL_GPIO_Init+0x2b4>
    {
      /*--------------------- GPIO Mode Configuration ------------------------*/
      /* In case of Output or Alternate function mode selection */
      if(((GPIO_Init->Mode & GPIO_MODE) == MODE_OUTPUT) || ((GPIO_Init->Mode & GPIO_MODE) == MODE_AF))
 80021ac:	683b      	ldr	r3, [r7, #0]
 80021ae:	685b      	ldr	r3, [r3, #4]
 80021b0:	f003 0303 	and.w	r3, r3, #3
 80021b4:	2b01      	cmp	r3, #1
 80021b6:	d005      	beq.n	80021c4 <HAL_GPIO_Init+0x40>
 80021b8:	683b      	ldr	r3, [r7, #0]
 80021ba:	685b      	ldr	r3, [r3, #4]
 80021bc:	f003 0303 	and.w	r3, r3, #3
 80021c0:	2b02      	cmp	r3, #2
 80021c2:	d130      	bne.n	8002226 <HAL_GPIO_Init+0xa2>
      {
        /* Check the Speed parameter */
        assert_param(IS_GPIO_SPEED(GPIO_Init->Speed));
        /* Configure the IO Speed */
        temp = GPIOx->OSPEEDR;
 80021c4:	687b      	ldr	r3, [r7, #4]
 80021c6:	689b      	ldr	r3, [r3, #8]
 80021c8:	613b      	str	r3, [r7, #16]
        temp &= ~(GPIO_OSPEEDER_OSPEEDR0 << (position * 2u));
 80021ca:	697b      	ldr	r3, [r7, #20]
 80021cc:	005b      	lsls	r3, r3, #1
 80021ce:	2203      	movs	r2, #3
 80021d0:	fa02 f303 	lsl.w	r3, r2, r3
 80021d4:	43db      	mvns	r3, r3
 80021d6:	693a      	ldr	r2, [r7, #16]
 80021d8:	4013      	ands	r3, r2
 80021da:	613b      	str	r3, [r7, #16]
        temp |= (GPIO_Init->Speed << (position * 2u));
 80021dc:	683b      	ldr	r3, [r7, #0]
 80021de:	68da      	ldr	r2, [r3, #12]
 80021e0:	697b      	ldr	r3, [r7, #20]
 80021e2:	005b      	lsls	r3, r3, #1
 80021e4:	fa02 f303 	lsl.w	r3, r2, r3
 80021e8:	693a      	ldr	r2, [r7, #16]
 80021ea:	4313      	orrs	r3, r2
 80021ec:	613b      	str	r3, [r7, #16]
        GPIOx->OSPEEDR = temp;
 80021ee:	687b      	ldr	r3, [r7, #4]
 80021f0:	693a      	ldr	r2, [r7, #16]
 80021f2:	609a      	str	r2, [r3, #8]

        /* Configure the IO Output Type */
        temp = GPIOx->OTYPER;
 80021f4:	687b      	ldr	r3, [r7, #4]
 80021f6:	685b      	ldr	r3, [r3, #4]
 80021f8:	613b      	str	r3, [r7, #16]
        temp &= ~(GPIO_OTYPER_OT_0 << position) ;
 80021fa:	2201      	movs	r2, #1
 80021fc:	697b      	ldr	r3, [r7, #20]
 80021fe:	fa02 f303 	lsl.w	r3, r2, r3
 8002202:	43db      	mvns	r3, r3
 8002204:	693a      	ldr	r2, [r7, #16]
 8002206:	4013      	ands	r3, r2
 8002208:	613b      	str	r3, [r7, #16]
        temp |= (((GPIO_Init->Mode & OUTPUT_TYPE) >> OUTPUT_TYPE_Pos) << position);
 800220a:	683b      	ldr	r3, [r7, #0]
 800220c:	685b      	ldr	r3, [r3, #4]
 800220e:	091b      	lsrs	r3, r3, #4
 8002210:	f003 0201 	and.w	r2, r3, #1
 8002214:	697b      	ldr	r3, [r7, #20]
 8002216:	fa02 f303 	lsl.w	r3, r2, r3
 800221a:	693a      	ldr	r2, [r7, #16]
 800221c:	4313      	orrs	r3, r2
 800221e:	613b      	str	r3, [r7, #16]
        GPIOx->OTYPER = temp;
 8002220:	687b      	ldr	r3, [r7, #4]
 8002222:	693a      	ldr	r2, [r7, #16]
 8002224:	605a      	str	r2, [r3, #4]
      }

      if((GPIO_Init->Mode & GPIO_MODE) != MODE_ANALOG)
 8002226:	683b      	ldr	r3, [r7, #0]
 8002228:	685b      	ldr	r3, [r3, #4]
 800222a:	f003 0303 	and.w	r3, r3, #3
 800222e:	2b03      	cmp	r3, #3
 8002230:	d017      	beq.n	8002262 <HAL_GPIO_Init+0xde>
      {
        /* Check the Pull parameter */
        assert_param(IS_GPIO_PULL(GPIO_Init->Pull));

        /* Activate the Pull-up or Pull down resistor for the current IO */
        temp = GPIOx->PUPDR;
 8002232:	687b      	ldr	r3, [r7, #4]
 8002234:	68db      	ldr	r3, [r3, #12]
 8002236:	613b      	str	r3, [r7, #16]
        temp &= ~(GPIO_PUPDR_PUPDR0 << (position * 2u));
 8002238:	697b      	ldr	r3, [r7, #20]
 800223a:	005b      	lsls	r3, r3, #1
 800223c:	2203      	movs	r2, #3
 800223e:	fa02 f303 	lsl.w	r3, r2, r3
 8002242:	43db      	mvns	r3, r3
 8002244:	693a      	ldr	r2, [r7, #16]
 8002246:	4013      	ands	r3, r2
 8002248:	613b      	str	r3, [r7, #16]
        temp |= ((GPIO_Init->Pull) << (position * 2u));
 800224a:	683b      	ldr	r3, [r7, #0]
 800224c:	689a      	ldr	r2, [r3, #8]
 800224e:	697b      	ldr	r3, [r7, #20]
 8002250:	005b      	lsls	r3, r3, #1
 8002252:	fa02 f303 	lsl.w	r3, r2, r3
 8002256:	693a      	ldr	r2, [r7, #16]
 8002258:	4313      	orrs	r3, r2
 800225a:	613b      	str	r3, [r7, #16]
        GPIOx->PUPDR = temp;
 800225c:	687b      	ldr	r3, [r7, #4]
 800225e:	693a      	ldr	r2, [r7, #16]
 8002260:	60da      	str	r2, [r3, #12]
      }

      /*--------------------- GPIO Mode Configuration ------------------------*/
      /* In case of Alternate function mode selection */
      if((GPIO_Init->Mode & GPIO_MODE) == MODE_AF)
 8002262:	683b      	ldr	r3, [r7, #0]
 8002264:	685b      	ldr	r3, [r3, #4]
 8002266:	f003 0303 	and.w	r3, r3, #3
 800226a:	2b02      	cmp	r3, #2
 800226c:	d123      	bne.n	80022b6 <HAL_GPIO_Init+0x132>
        /* Check the Alternate function parameters */
        assert_param(IS_GPIO_AF_INSTANCE(GPIOx));
        assert_param(IS_GPIO_AF(GPIO_Init->Alternate));

        /* Configure Alternate function mapped with the current IO */
        temp = GPIOx->AFR[position >> 3u];
 800226e:	697b      	ldr	r3, [r7, #20]
 8002270:	08da      	lsrs	r2, r3, #3
 8002272:	687b      	ldr	r3, [r7, #4]
 8002274:	3208      	adds	r2, #8
 8002276:	f853 3022 	ldr.w	r3, [r3, r2, lsl #2]
 800227a:	613b      	str	r3, [r7, #16]
        temp &= ~(0xFu << ((position & 0x07u) * 4u));
 800227c:	697b      	ldr	r3, [r7, #20]
 800227e:	f003 0307 	and.w	r3, r3, #7
 8002282:	009b      	lsls	r3, r3, #2
 8002284:	220f      	movs	r2, #15
 8002286:	fa02 f303 	lsl.w	r3, r2, r3
 800228a:	43db      	mvns	r3, r3
 800228c:	693a      	ldr	r2, [r7, #16]
 800228e:	4013      	ands	r3, r2
 8002290:	613b      	str	r3, [r7, #16]
        temp |= ((GPIO_Init->Alternate) << ((position & 0x07u) * 4u));
 8002292:	683b      	ldr	r3, [r7, #0]
 8002294:	691a      	ldr	r2, [r3, #16]
 8002296:	697b      	ldr	r3, [r7, #20]
 8002298:	f003 0307 	and.w	r3, r3, #7
 800229c:	009b      	lsls	r3, r3, #2
 800229e:	fa02 f303 	lsl.w	r3, r2, r3
 80022a2:	693a      	ldr	r2, [r7, #16]
 80022a4:	4313      	orrs	r3, r2
 80022a6:	613b      	str	r3, [r7, #16]
        GPIOx->AFR[position >> 3u] = temp;
 80022a8:	697b      	ldr	r3, [r7, #20]
 80022aa:	08da      	lsrs	r2, r3, #3
 80022ac:	687b      	ldr	r3, [r7, #4]
 80022ae:	3208      	adds	r2, #8
 80022b0:	6939      	ldr	r1, [r7, #16]
 80022b2:	f843 1022 	str.w	r1, [r3, r2, lsl #2]
      }

      /* Configure IO Direction mode (Input, Output, Alternate or Analog) */
      temp = GPIOx->MODER;
 80022b6:	687b      	ldr	r3, [r7, #4]
 80022b8:	681b      	ldr	r3, [r3, #0]
 80022ba:	613b      	str	r3, [r7, #16]
      temp &= ~(GPIO_MODER_MODER0 << (position * 2u));
 80022bc:	697b      	ldr	r3, [r7, #20]
 80022be:	005b      	lsls	r3, r3, #1
 80022c0:	2203      	movs	r2, #3
 80022c2:	fa02 f303 	lsl.w	r3, r2, r3
 80022c6:	43db      	mvns	r3, r3
 80022c8:	693a      	ldr	r2, [r7, #16]
 80022ca:	4013      	ands	r3, r2
 80022cc:	613b      	str	r3, [r7, #16]
      temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2u));
 80022ce:	683b      	ldr	r3, [r7, #0]
 80022d0:	685b      	ldr	r3, [r3, #4]
 80022d2:	f003 0203 	and.w	r2, r3, #3
 80022d6:	697b      	ldr	r3, [r7, #20]
 80022d8:	005b      	lsls	r3, r3, #1
 80022da:	fa02 f303 	lsl.w	r3, r2, r3
 80022de:	693a      	ldr	r2, [r7, #16]
 80022e0:	4313      	orrs	r3, r2
 80022e2:	613b      	str	r3, [r7, #16]
      GPIOx->MODER = temp;
 80022e4:	687b      	ldr	r3, [r7, #4]
 80022e6:	693a      	ldr	r2, [r7, #16]
 80022e8:	601a      	str	r2, [r3, #0]

      /*--------------------- EXTI Mode Configuration ------------------------*/
      /* Configure the External Interrupt or event for the current IO */
      if((GPIO_Init->Mode & EXTI_MODE) != 0x00u)
 80022ea:	683b      	ldr	r3, [r7, #0]
 80022ec:	685b      	ldr	r3, [r3, #4]
 80022ee:	f403 3340 	and.w	r3, r3, #196608	@ 0x30000
 80022f2:	2b00      	cmp	r3, #0
 80022f4:	f000 80a0 	beq.w	8002438 <HAL_GPIO_Init+0x2b4>
      {
        /* Enable SYSCFG Clock */
        __HAL_RCC_SYSCFG_CLK_ENABLE();
 80022f8:	4b58      	ldr	r3, [pc, #352]	@ (800245c <HAL_GPIO_Init+0x2d8>)
 80022fa:	699b      	ldr	r3, [r3, #24]
 80022fc:	4a57      	ldr	r2, [pc, #348]	@ (800245c <HAL_GPIO_Init+0x2d8>)
 80022fe:	f043 0301 	orr.w	r3, r3, #1
 8002302:	6193      	str	r3, [r2, #24]
 8002304:	4b55      	ldr	r3, [pc, #340]	@ (800245c <HAL_GPIO_Init+0x2d8>)
 8002306:	699b      	ldr	r3, [r3, #24]
 8002308:	f003 0301 	and.w	r3, r3, #1
 800230c:	60bb      	str	r3, [r7, #8]
 800230e:	68bb      	ldr	r3, [r7, #8]

        temp = SYSCFG->EXTICR[position >> 2u];
 8002310:	4a53      	ldr	r2, [pc, #332]	@ (8002460 <HAL_GPIO_Init+0x2dc>)
 8002312:	697b      	ldr	r3, [r7, #20]
 8002314:	089b      	lsrs	r3, r3, #2
 8002316:	3302      	adds	r3, #2
 8002318:	f852 3023 	ldr.w	r3, [r2, r3, lsl #2]
 800231c:	613b      	str	r3, [r7, #16]
        temp &= ~(0x0FuL << (4u * (position & 0x03u)));
 800231e:	697b      	ldr	r3, [r7, #20]
 8002320:	f003 0303 	and.w	r3, r3, #3
 8002324:	009b      	lsls	r3, r3, #2
 8002326:	220f      	movs	r2, #15
 8002328:	fa02 f303 	lsl.w	r3, r2, r3
 800232c:	43db      	mvns	r3, r3
 800232e:	693a      	ldr	r2, [r7, #16]
 8002330:	4013      	ands	r3, r2
 8002332:	613b      	str	r3, [r7, #16]
        temp |= (GPIO_GET_INDEX(GPIOx) << (4u * (position & 0x03u)));
 8002334:	687b      	ldr	r3, [r7, #4]
 8002336:	f1b3 4f90 	cmp.w	r3, #1207959552	@ 0x48000000
 800233a:	d019      	beq.n	8002370 <HAL_GPIO_Init+0x1ec>
 800233c:	687b      	ldr	r3, [r7, #4]
 800233e:	4a49      	ldr	r2, [pc, #292]	@ (8002464 <HAL_GPIO_Init+0x2e0>)
 8002340:	4293      	cmp	r3, r2
 8002342:	d013      	beq.n	800236c <HAL_GPIO_Init+0x1e8>
 8002344:	687b      	ldr	r3, [r7, #4]
 8002346:	4a48      	ldr	r2, [pc, #288]	@ (8002468 <HAL_GPIO_Init+0x2e4>)
 8002348:	4293      	cmp	r3, r2
 800234a:	d00d      	beq.n	8002368 <HAL_GPIO_Init+0x1e4>
 800234c:	687b      	ldr	r3, [r7, #4]
 800234e:	4a47      	ldr	r2, [pc, #284]	@ (800246c <HAL_GPIO_Init+0x2e8>)
 8002350:	4293      	cmp	r3, r2
 8002352:	d007      	beq.n	8002364 <HAL_GPIO_Init+0x1e0>
 8002354:	687b      	ldr	r3, [r7, #4]
 8002356:	4a46      	ldr	r2, [pc, #280]	@ (8002470 <HAL_GPIO_Init+0x2ec>)
 8002358:	4293      	cmp	r3, r2
 800235a:	d101      	bne.n	8002360 <HAL_GPIO_Init+0x1dc>
 800235c:	2304      	movs	r3, #4
 800235e:	e008      	b.n	8002372 <HAL_GPIO_Init+0x1ee>
 8002360:	2305      	movs	r3, #5
 8002362:	e006      	b.n	8002372 <HAL_GPIO_Init+0x1ee>
 8002364:	2303      	movs	r3, #3
 8002366:	e004      	b.n	8002372 <HAL_GPIO_Init+0x1ee>
 8002368:	2302      	movs	r3, #2
 800236a:	e002      	b.n	8002372 <HAL_GPIO_Init+0x1ee>
 800236c:	2301      	movs	r3, #1
 800236e:	e000      	b.n	8002372 <HAL_GPIO_Init+0x1ee>
 8002370:	2300      	movs	r3, #0
 8002372:	697a      	ldr	r2, [r7, #20]
 8002374:	f002 0203 	and.w	r2, r2, #3
 8002378:	0092      	lsls	r2, r2, #2
 800237a:	4093      	lsls	r3, r2
 800237c:	693a      	ldr	r2, [r7, #16]
 800237e:	4313      	orrs	r3, r2
 8002380:	613b      	str	r3, [r7, #16]
        SYSCFG->EXTICR[position >> 2u] = temp;
 8002382:	4937      	ldr	r1, [pc, #220]	@ (8002460 <HAL_GPIO_Init+0x2dc>)
 8002384:	697b      	ldr	r3, [r7, #20]
 8002386:	089b      	lsrs	r3, r3, #2
 8002388:	3302      	adds	r3, #2
 800238a:	693a      	ldr	r2, [r7, #16]
 800238c:	f841 2023 	str.w	r2, [r1, r3, lsl #2]

        /* Clear Rising Falling edge configuration */
        temp = EXTI->RTSR;
 8002390:	4b38      	ldr	r3, [pc, #224]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 8002392:	689b      	ldr	r3, [r3, #8]
 8002394:	613b      	str	r3, [r7, #16]
        temp &= ~(iocurrent);
 8002396:	68fb      	ldr	r3, [r7, #12]
 8002398:	43db      	mvns	r3, r3
 800239a:	693a      	ldr	r2, [r7, #16]
 800239c:	4013      	ands	r3, r2
 800239e:	613b      	str	r3, [r7, #16]
        if((GPIO_Init->Mode & TRIGGER_RISING) != 0x00u)
 80023a0:	683b      	ldr	r3, [r7, #0]
 80023a2:	685b      	ldr	r3, [r3, #4]
 80023a4:	f403 1380 	and.w	r3, r3, #1048576	@ 0x100000
 80023a8:	2b00      	cmp	r3, #0
 80023aa:	d003      	beq.n	80023b4 <HAL_GPIO_Init+0x230>
        {
          temp |= iocurrent;
 80023ac:	693a      	ldr	r2, [r7, #16]
 80023ae:	68fb      	ldr	r3, [r7, #12]
 80023b0:	4313      	orrs	r3, r2
 80023b2:	613b      	str	r3, [r7, #16]
        }
        EXTI->RTSR = temp;
 80023b4:	4a2f      	ldr	r2, [pc, #188]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 80023b6:	693b      	ldr	r3, [r7, #16]
 80023b8:	6093      	str	r3, [r2, #8]

        temp = EXTI->FTSR;
 80023ba:	4b2e      	ldr	r3, [pc, #184]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 80023bc:	68db      	ldr	r3, [r3, #12]
 80023be:	613b      	str	r3, [r7, #16]
        temp &= ~(iocurrent);
 80023c0:	68fb      	ldr	r3, [r7, #12]
 80023c2:	43db      	mvns	r3, r3
 80023c4:	693a      	ldr	r2, [r7, #16]
 80023c6:	4013      	ands	r3, r2
 80023c8:	613b      	str	r3, [r7, #16]
        if((GPIO_Init->Mode & TRIGGER_FALLING) != 0x00u)
 80023ca:	683b      	ldr	r3, [r7, #0]
 80023cc:	685b      	ldr	r3, [r3, #4]
 80023ce:	f403 1300 	and.w	r3, r3, #2097152	@ 0x200000
 80023d2:	2b00      	cmp	r3, #0
 80023d4:	d003      	beq.n	80023de <HAL_GPIO_Init+0x25a>
        {
          temp |= iocurrent;
 80023d6:	693a      	ldr	r2, [r7, #16]
 80023d8:	68fb      	ldr	r3, [r7, #12]
 80023da:	4313      	orrs	r3, r2
 80023dc:	613b      	str	r3, [r7, #16]
        }
        EXTI->FTSR = temp;
 80023de:	4a25      	ldr	r2, [pc, #148]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 80023e0:	693b      	ldr	r3, [r7, #16]
 80023e2:	60d3      	str	r3, [r2, #12]

        temp = EXTI->EMR;
 80023e4:	4b23      	ldr	r3, [pc, #140]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 80023e6:	685b      	ldr	r3, [r3, #4]
 80023e8:	613b      	str	r3, [r7, #16]
        temp &= ~(iocurrent);
 80023ea:	68fb      	ldr	r3, [r7, #12]
 80023ec:	43db      	mvns	r3, r3
 80023ee:	693a      	ldr	r2, [r7, #16]
 80023f0:	4013      	ands	r3, r2
 80023f2:	613b      	str	r3, [r7, #16]
        if((GPIO_Init->Mode & EXTI_EVT) != 0x00u)
 80023f4:	683b      	ldr	r3, [r7, #0]
 80023f6:	685b      	ldr	r3, [r3, #4]
 80023f8:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 80023fc:	2b00      	cmp	r3, #0
 80023fe:	d003      	beq.n	8002408 <HAL_GPIO_Init+0x284>
        {
          temp |= iocurrent;
 8002400:	693a      	ldr	r2, [r7, #16]
 8002402:	68fb      	ldr	r3, [r7, #12]
 8002404:	4313      	orrs	r3, r2
 8002406:	613b      	str	r3, [r7, #16]
        }
        EXTI->EMR = temp;
 8002408:	4a1a      	ldr	r2, [pc, #104]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 800240a:	693b      	ldr	r3, [r7, #16]
 800240c:	6053      	str	r3, [r2, #4]

        /* Clear EXTI line configuration */
        temp = EXTI->IMR;
 800240e:	4b19      	ldr	r3, [pc, #100]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 8002410:	681b      	ldr	r3, [r3, #0]
 8002412:	613b      	str	r3, [r7, #16]
        temp &= ~(iocurrent);
 8002414:	68fb      	ldr	r3, [r7, #12]
 8002416:	43db      	mvns	r3, r3
 8002418:	693a      	ldr	r2, [r7, #16]
 800241a:	4013      	ands	r3, r2
 800241c:	613b      	str	r3, [r7, #16]
        if((GPIO_Init->Mode & EXTI_IT) != 0x00u)
 800241e:	683b      	ldr	r3, [r7, #0]
 8002420:	685b      	ldr	r3, [r3, #4]
 8002422:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 8002426:	2b00      	cmp	r3, #0
 8002428:	d003      	beq.n	8002432 <HAL_GPIO_Init+0x2ae>
        {
          temp |= iocurrent;
 800242a:	693a      	ldr	r2, [r7, #16]
 800242c:	68fb      	ldr	r3, [r7, #12]
 800242e:	4313      	orrs	r3, r2
 8002430:	613b      	str	r3, [r7, #16]
        }
        EXTI->IMR = temp;
 8002432:	4a10      	ldr	r2, [pc, #64]	@ (8002474 <HAL_GPIO_Init+0x2f0>)
 8002434:	693b      	ldr	r3, [r7, #16]
 8002436:	6013      	str	r3, [r2, #0]
      }
    }

    position++;
 8002438:	697b      	ldr	r3, [r7, #20]
 800243a:	3301      	adds	r3, #1
 800243c:	617b      	str	r3, [r7, #20]
  while (((GPIO_Init->Pin) >> position) != 0x00u)
 800243e:	683b      	ldr	r3, [r7, #0]
 8002440:	681a      	ldr	r2, [r3, #0]
 8002442:	697b      	ldr	r3, [r7, #20]
 8002444:	fa22 f303 	lsr.w	r3, r2, r3
 8002448:	2b00      	cmp	r3, #0
 800244a:	f47f aea3 	bne.w	8002194 <HAL_GPIO_Init+0x10>
  }
}
 800244e:	bf00      	nop
 8002450:	bf00      	nop
 8002452:	371c      	adds	r7, #28
 8002454:	46bd      	mov	sp, r7
 8002456:	f85d 7b04 	ldr.w	r7, [sp], #4
 800245a:	4770      	bx	lr
 800245c:	******** 	.word	0x********
 8002460:	40010000 	.word	0x40010000
 8002464:	48000400 	.word	0x48000400
 8002468:	48000800 	.word	0x48000800
 800246c:	48000c00 	.word	0x48000c00
 8002470:	48001000 	.word	0x48001000
 8002474:	40010400 	.word	0x40010400

08002478 <HAL_RCC_OscConfig>:
  *         supported by this macro. User should request a transition to HSE Off
  *         first and then HSE On or HSE Bypass.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_RCC_OscConfig(RCC_OscInitTypeDef  *RCC_OscInitStruct)
{
 8002478:	b580      	push	{r7, lr}
 800247a:	f5ad 7d00 	sub.w	sp, sp, #512	@ 0x200
 800247e:	af00      	add	r7, sp, #0
 8002480:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002484:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002488:	6018      	str	r0, [r3, #0]
#if defined(RCC_CFGR_PLLSRC_HSI_PREDIV)
  uint32_t pll_config2;
#endif /* RCC_CFGR_PLLSRC_HSI_PREDIV */

  /* Check Null pointer */
  if(RCC_OscInitStruct == NULL)
 800248a:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800248e:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002492:	681b      	ldr	r3, [r3, #0]
 8002494:	2b00      	cmp	r3, #0
 8002496:	d102      	bne.n	800249e <HAL_RCC_OscConfig+0x26>
  {
    return HAL_ERROR;
 8002498:	2301      	movs	r3, #1
 800249a:	f000 bff4 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>

  /* Check the parameters */
  assert_param(IS_RCC_OSCILLATORTYPE(RCC_OscInitStruct->OscillatorType));

  /*------------------------------- HSE Configuration ------------------------*/ 
  if(((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 800249e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80024a2:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 80024a6:	681b      	ldr	r3, [r3, #0]
 80024a8:	681b      	ldr	r3, [r3, #0]
 80024aa:	f003 0301 	and.w	r3, r3, #1
 80024ae:	2b00      	cmp	r3, #0
 80024b0:	f000 816d 	beq.w	800278e <HAL_RCC_OscConfig+0x316>
  {
    /* Check the parameters */
    assert_param(IS_RCC_HSE(RCC_OscInitStruct->HSEState));

    /* When the HSE is used as system clock or clock source for PLL in these cases it is not allowed to be disabled */
    if((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSE) 
 80024b4:	4bb4      	ldr	r3, [pc, #720]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80024b6:	685b      	ldr	r3, [r3, #4]
 80024b8:	f003 030c 	and.w	r3, r3, #12
 80024bc:	2b04      	cmp	r3, #4
 80024be:	d00c      	beq.n	80024da <HAL_RCC_OscConfig+0x62>
       || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSE)))
 80024c0:	4bb1      	ldr	r3, [pc, #708]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80024c2:	685b      	ldr	r3, [r3, #4]
 80024c4:	f003 030c 	and.w	r3, r3, #12
 80024c8:	2b08      	cmp	r3, #8
 80024ca:	d157      	bne.n	800257c <HAL_RCC_OscConfig+0x104>
 80024cc:	4bae      	ldr	r3, [pc, #696]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80024ce:	685b      	ldr	r3, [r3, #4]
 80024d0:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 80024d4:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 80024d8:	d150      	bne.n	800257c <HAL_RCC_OscConfig+0x104>
 80024da:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 80024de:	f8c7 31f0 	str.w	r3, [r7, #496]	@ 0x1f0
  uint32_t result;

#if ((defined (__ARM_ARCH_7M__      ) && (__ARM_ARCH_7M__      == 1)) || \
     (defined (__ARM_ARCH_7EM__     ) && (__ARM_ARCH_7EM__     == 1)) || \
     (defined (__ARM_ARCH_8M_MAIN__ ) && (__ARM_ARCH_8M_MAIN__ == 1))    )
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80024e2:	f8d7 31f0 	ldr.w	r3, [r7, #496]	@ 0x1f0
 80024e6:	fa93 f3a3 	rbit	r3, r3
 80024ea:	f8c7 31ec 	str.w	r3, [r7, #492]	@ 0x1ec
    result |= value & 1U;
    s--;
  }
  result <<= s;                        /* shift when v's highest bits are zero */
#endif
  return result;
 80024ee:	f8d7 31ec 	ldr.w	r3, [r7, #492]	@ 0x1ec
    {
      if((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 80024f2:	fab3 f383 	clz	r3, r3
 80024f6:	b2db      	uxtb	r3, r3
 80024f8:	2b3f      	cmp	r3, #63	@ 0x3f
 80024fa:	d802      	bhi.n	8002502 <HAL_RCC_OscConfig+0x8a>
 80024fc:	4ba2      	ldr	r3, [pc, #648]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80024fe:	681b      	ldr	r3, [r3, #0]
 8002500:	e015      	b.n	800252e <HAL_RCC_OscConfig+0xb6>
 8002502:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8002506:	f8c7 31e8 	str.w	r3, [r7, #488]	@ 0x1e8
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800250a:	f8d7 31e8 	ldr.w	r3, [r7, #488]	@ 0x1e8
 800250e:	fa93 f3a3 	rbit	r3, r3
 8002512:	f8c7 31e4 	str.w	r3, [r7, #484]	@ 0x1e4
 8002516:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 800251a:	f8c7 31e0 	str.w	r3, [r7, #480]	@ 0x1e0
 800251e:	f8d7 31e0 	ldr.w	r3, [r7, #480]	@ 0x1e0
 8002522:	fa93 f3a3 	rbit	r3, r3
 8002526:	f8c7 31dc 	str.w	r3, [r7, #476]	@ 0x1dc
 800252a:	4b97      	ldr	r3, [pc, #604]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 800252c:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 800252e:	f44f 3200 	mov.w	r2, #131072	@ 0x20000
 8002532:	f8c7 21d8 	str.w	r2, [r7, #472]	@ 0x1d8
 8002536:	f8d7 21d8 	ldr.w	r2, [r7, #472]	@ 0x1d8
 800253a:	fa92 f2a2 	rbit	r2, r2
 800253e:	f8c7 21d4 	str.w	r2, [r7, #468]	@ 0x1d4
  return result;
 8002542:	f8d7 21d4 	ldr.w	r2, [r7, #468]	@ 0x1d4
 8002546:	fab2 f282 	clz	r2, r2
 800254a:	b2d2      	uxtb	r2, r2
 800254c:	f042 0220 	orr.w	r2, r2, #32
 8002550:	b2d2      	uxtb	r2, r2
 8002552:	f002 021f 	and.w	r2, r2, #31
 8002556:	2101      	movs	r1, #1
 8002558:	fa01 f202 	lsl.w	r2, r1, r2
 800255c:	4013      	ands	r3, r2
 800255e:	2b00      	cmp	r3, #0
 8002560:	f000 8114 	beq.w	800278c <HAL_RCC_OscConfig+0x314>
 8002564:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002568:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 800256c:	681b      	ldr	r3, [r3, #0]
 800256e:	685b      	ldr	r3, [r3, #4]
 8002570:	2b00      	cmp	r3, #0
 8002572:	f040 810b 	bne.w	800278c <HAL_RCC_OscConfig+0x314>
      {
        return HAL_ERROR;
 8002576:	2301      	movs	r3, #1
 8002578:	f000 bf85 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
      }
    }
    else
    {
      /* Set the new HSE configuration ---------------------------------------*/
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 800257c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002580:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002584:	681b      	ldr	r3, [r3, #0]
 8002586:	685b      	ldr	r3, [r3, #4]
 8002588:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 800258c:	d106      	bne.n	800259c <HAL_RCC_OscConfig+0x124>
 800258e:	4b7e      	ldr	r3, [pc, #504]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002590:	681b      	ldr	r3, [r3, #0]
 8002592:	4a7d      	ldr	r2, [pc, #500]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002594:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 8002598:	6013      	str	r3, [r2, #0]
 800259a:	e036      	b.n	800260a <HAL_RCC_OscConfig+0x192>
 800259c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80025a0:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 80025a4:	681b      	ldr	r3, [r3, #0]
 80025a6:	685b      	ldr	r3, [r3, #4]
 80025a8:	2b00      	cmp	r3, #0
 80025aa:	d10c      	bne.n	80025c6 <HAL_RCC_OscConfig+0x14e>
 80025ac:	4b76      	ldr	r3, [pc, #472]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025ae:	681b      	ldr	r3, [r3, #0]
 80025b0:	4a75      	ldr	r2, [pc, #468]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025b2:	f423 3380 	bic.w	r3, r3, #65536	@ 0x10000
 80025b6:	6013      	str	r3, [r2, #0]
 80025b8:	4b73      	ldr	r3, [pc, #460]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025ba:	681b      	ldr	r3, [r3, #0]
 80025bc:	4a72      	ldr	r2, [pc, #456]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025be:	f423 2380 	bic.w	r3, r3, #262144	@ 0x40000
 80025c2:	6013      	str	r3, [r2, #0]
 80025c4:	e021      	b.n	800260a <HAL_RCC_OscConfig+0x192>
 80025c6:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80025ca:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 80025ce:	681b      	ldr	r3, [r3, #0]
 80025d0:	685b      	ldr	r3, [r3, #4]
 80025d2:	f5b3 2fa0 	cmp.w	r3, #327680	@ 0x50000
 80025d6:	d10c      	bne.n	80025f2 <HAL_RCC_OscConfig+0x17a>
 80025d8:	4b6b      	ldr	r3, [pc, #428]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025da:	681b      	ldr	r3, [r3, #0]
 80025dc:	4a6a      	ldr	r2, [pc, #424]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025de:	f443 2380 	orr.w	r3, r3, #262144	@ 0x40000
 80025e2:	6013      	str	r3, [r2, #0]
 80025e4:	4b68      	ldr	r3, [pc, #416]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025e6:	681b      	ldr	r3, [r3, #0]
 80025e8:	4a67      	ldr	r2, [pc, #412]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025ea:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 80025ee:	6013      	str	r3, [r2, #0]
 80025f0:	e00b      	b.n	800260a <HAL_RCC_OscConfig+0x192>
 80025f2:	4b65      	ldr	r3, [pc, #404]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025f4:	681b      	ldr	r3, [r3, #0]
 80025f6:	4a64      	ldr	r2, [pc, #400]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80025f8:	f423 3380 	bic.w	r3, r3, #65536	@ 0x10000
 80025fc:	6013      	str	r3, [r2, #0]
 80025fe:	4b62      	ldr	r3, [pc, #392]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002600:	681b      	ldr	r3, [r3, #0]
 8002602:	4a61      	ldr	r2, [pc, #388]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002604:	f423 2380 	bic.w	r3, r3, #262144	@ 0x40000
 8002608:	6013      	str	r3, [r2, #0]
      
#if defined(RCC_CFGR_PLLSRC_HSI_DIV2)
      /* Configure the HSE predivision factor --------------------------------*/
      __HAL_RCC_HSE_PREDIV_CONFIG(RCC_OscInitStruct->HSEPredivValue);
 800260a:	4b5f      	ldr	r3, [pc, #380]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 800260c:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 800260e:	f023 020f 	bic.w	r2, r3, #15
 8002612:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002616:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 800261a:	681b      	ldr	r3, [r3, #0]
 800261c:	689b      	ldr	r3, [r3, #8]
 800261e:	495a      	ldr	r1, [pc, #360]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002620:	4313      	orrs	r3, r2
 8002622:	62cb      	str	r3, [r1, #44]	@ 0x2c
#endif /* RCC_CFGR_PLLSRC_HSI_DIV2 */

       /* Check the HSE State */
      if(RCC_OscInitStruct->HSEState != RCC_HSE_OFF)
 8002624:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002628:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 800262c:	681b      	ldr	r3, [r3, #0]
 800262e:	685b      	ldr	r3, [r3, #4]
 8002630:	2b00      	cmp	r3, #0
 8002632:	d054      	beq.n	80026de <HAL_RCC_OscConfig+0x266>
      {
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8002634:	f7fe f9e4 	bl	8000a00 <HAL_GetTick>
 8002638:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
        
        /* Wait till HSE is ready */
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 800263c:	e00a      	b.n	8002654 <HAL_RCC_OscConfig+0x1dc>
        {
          if((HAL_GetTick() - tickstart ) > HSE_TIMEOUT_VALUE)
 800263e:	f7fe f9df 	bl	8000a00 <HAL_GetTick>
 8002642:	4602      	mov	r2, r0
 8002644:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8002648:	1ad3      	subs	r3, r2, r3
 800264a:	2b64      	cmp	r3, #100	@ 0x64
 800264c:	d902      	bls.n	8002654 <HAL_RCC_OscConfig+0x1dc>
          {
            return HAL_TIMEOUT;
 800264e:	2303      	movs	r3, #3
 8002650:	f000 bf19 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
 8002654:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8002658:	f8c7 31d0 	str.w	r3, [r7, #464]	@ 0x1d0
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800265c:	f8d7 31d0 	ldr.w	r3, [r7, #464]	@ 0x1d0
 8002660:	fa93 f3a3 	rbit	r3, r3
 8002664:	f8c7 31cc 	str.w	r3, [r7, #460]	@ 0x1cc
  return result;
 8002668:	f8d7 31cc 	ldr.w	r3, [r7, #460]	@ 0x1cc
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 800266c:	fab3 f383 	clz	r3, r3
 8002670:	b2db      	uxtb	r3, r3
 8002672:	2b3f      	cmp	r3, #63	@ 0x3f
 8002674:	d802      	bhi.n	800267c <HAL_RCC_OscConfig+0x204>
 8002676:	4b44      	ldr	r3, [pc, #272]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002678:	681b      	ldr	r3, [r3, #0]
 800267a:	e015      	b.n	80026a8 <HAL_RCC_OscConfig+0x230>
 800267c:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8002680:	f8c7 31c8 	str.w	r3, [r7, #456]	@ 0x1c8
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002684:	f8d7 31c8 	ldr.w	r3, [r7, #456]	@ 0x1c8
 8002688:	fa93 f3a3 	rbit	r3, r3
 800268c:	f8c7 31c4 	str.w	r3, [r7, #452]	@ 0x1c4
 8002690:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8002694:	f8c7 31c0 	str.w	r3, [r7, #448]	@ 0x1c0
 8002698:	f8d7 31c0 	ldr.w	r3, [r7, #448]	@ 0x1c0
 800269c:	fa93 f3a3 	rbit	r3, r3
 80026a0:	f8c7 31bc 	str.w	r3, [r7, #444]	@ 0x1bc
 80026a4:	4b38      	ldr	r3, [pc, #224]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 80026a6:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 80026a8:	f44f 3200 	mov.w	r2, #131072	@ 0x20000
 80026ac:	f8c7 21b8 	str.w	r2, [r7, #440]	@ 0x1b8
 80026b0:	f8d7 21b8 	ldr.w	r2, [r7, #440]	@ 0x1b8
 80026b4:	fa92 f2a2 	rbit	r2, r2
 80026b8:	f8c7 21b4 	str.w	r2, [r7, #436]	@ 0x1b4
  return result;
 80026bc:	f8d7 21b4 	ldr.w	r2, [r7, #436]	@ 0x1b4
 80026c0:	fab2 f282 	clz	r2, r2
 80026c4:	b2d2      	uxtb	r2, r2
 80026c6:	f042 0220 	orr.w	r2, r2, #32
 80026ca:	b2d2      	uxtb	r2, r2
 80026cc:	f002 021f 	and.w	r2, r2, #31
 80026d0:	2101      	movs	r1, #1
 80026d2:	fa01 f202 	lsl.w	r2, r1, r2
 80026d6:	4013      	ands	r3, r2
 80026d8:	2b00      	cmp	r3, #0
 80026da:	d0b0      	beq.n	800263e <HAL_RCC_OscConfig+0x1c6>
 80026dc:	e057      	b.n	800278e <HAL_RCC_OscConfig+0x316>
        }
      }
      else
      {
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 80026de:	f7fe f98f 	bl	8000a00 <HAL_GetTick>
 80026e2:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
        
        /* Wait till HSE is disabled */
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 80026e6:	e00a      	b.n	80026fe <HAL_RCC_OscConfig+0x286>
        {
           if((HAL_GetTick() - tickstart ) > HSE_TIMEOUT_VALUE)
 80026e8:	f7fe f98a 	bl	8000a00 <HAL_GetTick>
 80026ec:	4602      	mov	r2, r0
 80026ee:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 80026f2:	1ad3      	subs	r3, r2, r3
 80026f4:	2b64      	cmp	r3, #100	@ 0x64
 80026f6:	d902      	bls.n	80026fe <HAL_RCC_OscConfig+0x286>
          {
            return HAL_TIMEOUT;
 80026f8:	2303      	movs	r3, #3
 80026fa:	f000 bec4 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
 80026fe:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8002702:	f8c7 31b0 	str.w	r3, [r7, #432]	@ 0x1b0
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002706:	f8d7 31b0 	ldr.w	r3, [r7, #432]	@ 0x1b0
 800270a:	fa93 f3a3 	rbit	r3, r3
 800270e:	f8c7 31ac 	str.w	r3, [r7, #428]	@ 0x1ac
  return result;
 8002712:	f8d7 31ac 	ldr.w	r3, [r7, #428]	@ 0x1ac
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 8002716:	fab3 f383 	clz	r3, r3
 800271a:	b2db      	uxtb	r3, r3
 800271c:	2b3f      	cmp	r3, #63	@ 0x3f
 800271e:	d802      	bhi.n	8002726 <HAL_RCC_OscConfig+0x2ae>
 8002720:	4b19      	ldr	r3, [pc, #100]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002722:	681b      	ldr	r3, [r3, #0]
 8002724:	e015      	b.n	8002752 <HAL_RCC_OscConfig+0x2da>
 8002726:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 800272a:	f8c7 31a8 	str.w	r3, [r7, #424]	@ 0x1a8
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800272e:	f8d7 31a8 	ldr.w	r3, [r7, #424]	@ 0x1a8
 8002732:	fa93 f3a3 	rbit	r3, r3
 8002736:	f8c7 31a4 	str.w	r3, [r7, #420]	@ 0x1a4
 800273a:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 800273e:	f8c7 31a0 	str.w	r3, [r7, #416]	@ 0x1a0
 8002742:	f8d7 31a0 	ldr.w	r3, [r7, #416]	@ 0x1a0
 8002746:	fa93 f3a3 	rbit	r3, r3
 800274a:	f8c7 319c 	str.w	r3, [r7, #412]	@ 0x19c
 800274e:	4b0e      	ldr	r3, [pc, #56]	@ (8002788 <HAL_RCC_OscConfig+0x310>)
 8002750:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8002752:	f44f 3200 	mov.w	r2, #131072	@ 0x20000
 8002756:	f8c7 2198 	str.w	r2, [r7, #408]	@ 0x198
 800275a:	f8d7 2198 	ldr.w	r2, [r7, #408]	@ 0x198
 800275e:	fa92 f2a2 	rbit	r2, r2
 8002762:	f8c7 2194 	str.w	r2, [r7, #404]	@ 0x194
  return result;
 8002766:	f8d7 2194 	ldr.w	r2, [r7, #404]	@ 0x194
 800276a:	fab2 f282 	clz	r2, r2
 800276e:	b2d2      	uxtb	r2, r2
 8002770:	f042 0220 	orr.w	r2, r2, #32
 8002774:	b2d2      	uxtb	r2, r2
 8002776:	f002 021f 	and.w	r2, r2, #31
 800277a:	2101      	movs	r1, #1
 800277c:	fa01 f202 	lsl.w	r2, r1, r2
 8002780:	4013      	ands	r3, r2
 8002782:	2b00      	cmp	r3, #0
 8002784:	d1b0      	bne.n	80026e8 <HAL_RCC_OscConfig+0x270>
 8002786:	e002      	b.n	800278e <HAL_RCC_OscConfig+0x316>
 8002788:	******** 	.word	0x********
      if((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 800278c:	bf00      	nop
        }
      }
    }
  }
  /*----------------------------- HSI Configuration --------------------------*/ 
  if(((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI) == RCC_OSCILLATORTYPE_HSI)
 800278e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002792:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002796:	681b      	ldr	r3, [r3, #0]
 8002798:	681b      	ldr	r3, [r3, #0]
 800279a:	f003 0302 	and.w	r3, r3, #2
 800279e:	2b00      	cmp	r3, #0
 80027a0:	f000 816c 	beq.w	8002a7c <HAL_RCC_OscConfig+0x604>
    /* Check the parameters */
    assert_param(IS_RCC_HSI(RCC_OscInitStruct->HSIState));
    assert_param(IS_RCC_CALIBRATION_VALUE(RCC_OscInitStruct->HSICalibrationValue));
    
    /* Check if HSI is used as system clock or as PLL source when PLL is selected as system clock */ 
    if((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_HSI) 
 80027a4:	4bcc      	ldr	r3, [pc, #816]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 80027a6:	685b      	ldr	r3, [r3, #4]
 80027a8:	f003 030c 	and.w	r3, r3, #12
 80027ac:	2b00      	cmp	r3, #0
 80027ae:	d00b      	beq.n	80027c8 <HAL_RCC_OscConfig+0x350>
       || ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_SYSCLKSOURCE_STATUS_PLLCLK) && (__HAL_RCC_GET_PLL_OSCSOURCE() == RCC_PLLSOURCE_HSI)))
 80027b0:	4bc9      	ldr	r3, [pc, #804]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 80027b2:	685b      	ldr	r3, [r3, #4]
 80027b4:	f003 030c 	and.w	r3, r3, #12
 80027b8:	2b08      	cmp	r3, #8
 80027ba:	d16d      	bne.n	8002898 <HAL_RCC_OscConfig+0x420>
 80027bc:	4bc6      	ldr	r3, [pc, #792]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 80027be:	685b      	ldr	r3, [r3, #4]
 80027c0:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 80027c4:	2b00      	cmp	r3, #0
 80027c6:	d167      	bne.n	8002898 <HAL_RCC_OscConfig+0x420>
 80027c8:	2302      	movs	r3, #2
 80027ca:	f8c7 3190 	str.w	r3, [r7, #400]	@ 0x190
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80027ce:	f8d7 3190 	ldr.w	r3, [r7, #400]	@ 0x190
 80027d2:	fa93 f3a3 	rbit	r3, r3
 80027d6:	f8c7 318c 	str.w	r3, [r7, #396]	@ 0x18c
  return result;
 80027da:	f8d7 318c 	ldr.w	r3, [r7, #396]	@ 0x18c
    {
      /* When HSI is used as system clock it will not disabled */
      if((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 80027de:	fab3 f383 	clz	r3, r3
 80027e2:	b2db      	uxtb	r3, r3
 80027e4:	2b3f      	cmp	r3, #63	@ 0x3f
 80027e6:	d802      	bhi.n	80027ee <HAL_RCC_OscConfig+0x376>
 80027e8:	4bbb      	ldr	r3, [pc, #748]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 80027ea:	681b      	ldr	r3, [r3, #0]
 80027ec:	e013      	b.n	8002816 <HAL_RCC_OscConfig+0x39e>
 80027ee:	2302      	movs	r3, #2
 80027f0:	f8c7 3188 	str.w	r3, [r7, #392]	@ 0x188
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80027f4:	f8d7 3188 	ldr.w	r3, [r7, #392]	@ 0x188
 80027f8:	fa93 f3a3 	rbit	r3, r3
 80027fc:	f8c7 3184 	str.w	r3, [r7, #388]	@ 0x184
 8002800:	2302      	movs	r3, #2
 8002802:	f8c7 3180 	str.w	r3, [r7, #384]	@ 0x180
 8002806:	f8d7 3180 	ldr.w	r3, [r7, #384]	@ 0x180
 800280a:	fa93 f3a3 	rbit	r3, r3
 800280e:	f8c7 317c 	str.w	r3, [r7, #380]	@ 0x17c
 8002812:	4bb1      	ldr	r3, [pc, #708]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002814:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8002816:	2202      	movs	r2, #2
 8002818:	f8c7 2178 	str.w	r2, [r7, #376]	@ 0x178
 800281c:	f8d7 2178 	ldr.w	r2, [r7, #376]	@ 0x178
 8002820:	fa92 f2a2 	rbit	r2, r2
 8002824:	f8c7 2174 	str.w	r2, [r7, #372]	@ 0x174
  return result;
 8002828:	f8d7 2174 	ldr.w	r2, [r7, #372]	@ 0x174
 800282c:	fab2 f282 	clz	r2, r2
 8002830:	b2d2      	uxtb	r2, r2
 8002832:	f042 0220 	orr.w	r2, r2, #32
 8002836:	b2d2      	uxtb	r2, r2
 8002838:	f002 021f 	and.w	r2, r2, #31
 800283c:	2101      	movs	r1, #1
 800283e:	fa01 f202 	lsl.w	r2, r1, r2
 8002842:	4013      	ands	r3, r2
 8002844:	2b00      	cmp	r3, #0
 8002846:	d00a      	beq.n	800285e <HAL_RCC_OscConfig+0x3e6>
 8002848:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800284c:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002850:	681b      	ldr	r3, [r3, #0]
 8002852:	691b      	ldr	r3, [r3, #16]
 8002854:	2b01      	cmp	r3, #1
 8002856:	d002      	beq.n	800285e <HAL_RCC_OscConfig+0x3e6>
      {
        return HAL_ERROR;
 8002858:	2301      	movs	r3, #1
 800285a:	f000 be14 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
      }
      /* Otherwise, just the calibration is allowed */
      else
      {
        /* Adjusts the Internal High Speed oscillator (HSI) calibration value.*/
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 800285e:	4b9e      	ldr	r3, [pc, #632]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002860:	681b      	ldr	r3, [r3, #0]
 8002862:	f023 02f8 	bic.w	r2, r3, #248	@ 0xf8
 8002866:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800286a:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 800286e:	681b      	ldr	r3, [r3, #0]
 8002870:	695b      	ldr	r3, [r3, #20]
 8002872:	21f8      	movs	r1, #248	@ 0xf8
 8002874:	f8c7 1170 	str.w	r1, [r7, #368]	@ 0x170
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002878:	f8d7 1170 	ldr.w	r1, [r7, #368]	@ 0x170
 800287c:	fa91 f1a1 	rbit	r1, r1
 8002880:	f8c7 116c 	str.w	r1, [r7, #364]	@ 0x16c
  return result;
 8002884:	f8d7 116c 	ldr.w	r1, [r7, #364]	@ 0x16c
 8002888:	fab1 f181 	clz	r1, r1
 800288c:	b2c9      	uxtb	r1, r1
 800288e:	408b      	lsls	r3, r1
 8002890:	4991      	ldr	r1, [pc, #580]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002892:	4313      	orrs	r3, r2
 8002894:	600b      	str	r3, [r1, #0]
      if((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 8002896:	e0f1      	b.n	8002a7c <HAL_RCC_OscConfig+0x604>
      }
    }
    else
    {
      /* Check the HSI State */
      if(RCC_OscInitStruct->HSIState != RCC_HSI_OFF)
 8002898:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800289c:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 80028a0:	681b      	ldr	r3, [r3, #0]
 80028a2:	691b      	ldr	r3, [r3, #16]
 80028a4:	2b00      	cmp	r3, #0
 80028a6:	f000 8083 	beq.w	80029b0 <HAL_RCC_OscConfig+0x538>
 80028aa:	2301      	movs	r3, #1
 80028ac:	f8c7 3168 	str.w	r3, [r7, #360]	@ 0x168
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80028b0:	f8d7 3168 	ldr.w	r3, [r7, #360]	@ 0x168
 80028b4:	fa93 f3a3 	rbit	r3, r3
 80028b8:	f8c7 3164 	str.w	r3, [r7, #356]	@ 0x164
  return result;
 80028bc:	f8d7 3164 	ldr.w	r3, [r7, #356]	@ 0x164
      {
       /* Enable the Internal High Speed oscillator (HSI). */
        __HAL_RCC_HSI_ENABLE();
 80028c0:	fab3 f383 	clz	r3, r3
 80028c4:	b2db      	uxtb	r3, r3
 80028c6:	f103 5384 	add.w	r3, r3, #276824064	@ 0x10800000
 80028ca:	f503 1384 	add.w	r3, r3, #1081344	@ 0x108000
 80028ce:	009b      	lsls	r3, r3, #2
 80028d0:	461a      	mov	r2, r3
 80028d2:	2301      	movs	r3, #1
 80028d4:	6013      	str	r3, [r2, #0]
        
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 80028d6:	f7fe f893 	bl	8000a00 <HAL_GetTick>
 80028da:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
        
        /* Wait till HSI is ready */
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 80028de:	e00a      	b.n	80028f6 <HAL_RCC_OscConfig+0x47e>
        {
          if((HAL_GetTick() - tickstart ) > HSI_TIMEOUT_VALUE)
 80028e0:	f7fe f88e 	bl	8000a00 <HAL_GetTick>
 80028e4:	4602      	mov	r2, r0
 80028e6:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 80028ea:	1ad3      	subs	r3, r2, r3
 80028ec:	2b02      	cmp	r3, #2
 80028ee:	d902      	bls.n	80028f6 <HAL_RCC_OscConfig+0x47e>
          {
            return HAL_TIMEOUT;
 80028f0:	2303      	movs	r3, #3
 80028f2:	f000 bdc8 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
 80028f6:	2302      	movs	r3, #2
 80028f8:	f8c7 3160 	str.w	r3, [r7, #352]	@ 0x160
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80028fc:	f8d7 3160 	ldr.w	r3, [r7, #352]	@ 0x160
 8002900:	fa93 f3a3 	rbit	r3, r3
 8002904:	f8c7 315c 	str.w	r3, [r7, #348]	@ 0x15c
  return result;
 8002908:	f8d7 315c 	ldr.w	r3, [r7, #348]	@ 0x15c
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 800290c:	fab3 f383 	clz	r3, r3
 8002910:	b2db      	uxtb	r3, r3
 8002912:	2b3f      	cmp	r3, #63	@ 0x3f
 8002914:	d802      	bhi.n	800291c <HAL_RCC_OscConfig+0x4a4>
 8002916:	4b70      	ldr	r3, [pc, #448]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002918:	681b      	ldr	r3, [r3, #0]
 800291a:	e013      	b.n	8002944 <HAL_RCC_OscConfig+0x4cc>
 800291c:	2302      	movs	r3, #2
 800291e:	f8c7 3158 	str.w	r3, [r7, #344]	@ 0x158
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002922:	f8d7 3158 	ldr.w	r3, [r7, #344]	@ 0x158
 8002926:	fa93 f3a3 	rbit	r3, r3
 800292a:	f8c7 3154 	str.w	r3, [r7, #340]	@ 0x154
 800292e:	2302      	movs	r3, #2
 8002930:	f8c7 3150 	str.w	r3, [r7, #336]	@ 0x150
 8002934:	f8d7 3150 	ldr.w	r3, [r7, #336]	@ 0x150
 8002938:	fa93 f3a3 	rbit	r3, r3
 800293c:	f8c7 314c 	str.w	r3, [r7, #332]	@ 0x14c
 8002940:	4b65      	ldr	r3, [pc, #404]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002942:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8002944:	2202      	movs	r2, #2
 8002946:	f8c7 2148 	str.w	r2, [r7, #328]	@ 0x148
 800294a:	f8d7 2148 	ldr.w	r2, [r7, #328]	@ 0x148
 800294e:	fa92 f2a2 	rbit	r2, r2
 8002952:	f8c7 2144 	str.w	r2, [r7, #324]	@ 0x144
  return result;
 8002956:	f8d7 2144 	ldr.w	r2, [r7, #324]	@ 0x144
 800295a:	fab2 f282 	clz	r2, r2
 800295e:	b2d2      	uxtb	r2, r2
 8002960:	f042 0220 	orr.w	r2, r2, #32
 8002964:	b2d2      	uxtb	r2, r2
 8002966:	f002 021f 	and.w	r2, r2, #31
 800296a:	2101      	movs	r1, #1
 800296c:	fa01 f202 	lsl.w	r2, r1, r2
 8002970:	4013      	ands	r3, r2
 8002972:	2b00      	cmp	r3, #0
 8002974:	d0b4      	beq.n	80028e0 <HAL_RCC_OscConfig+0x468>
          }
        }
                
        /* Adjusts the Internal High Speed oscillator (HSI) calibration value.*/
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8002976:	4b58      	ldr	r3, [pc, #352]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002978:	681b      	ldr	r3, [r3, #0]
 800297a:	f023 02f8 	bic.w	r2, r3, #248	@ 0xf8
 800297e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002982:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002986:	681b      	ldr	r3, [r3, #0]
 8002988:	695b      	ldr	r3, [r3, #20]
 800298a:	21f8      	movs	r1, #248	@ 0xf8
 800298c:	f8c7 1140 	str.w	r1, [r7, #320]	@ 0x140
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002990:	f8d7 1140 	ldr.w	r1, [r7, #320]	@ 0x140
 8002994:	fa91 f1a1 	rbit	r1, r1
 8002998:	f8c7 113c 	str.w	r1, [r7, #316]	@ 0x13c
  return result;
 800299c:	f8d7 113c 	ldr.w	r1, [r7, #316]	@ 0x13c
 80029a0:	fab1 f181 	clz	r1, r1
 80029a4:	b2c9      	uxtb	r1, r1
 80029a6:	408b      	lsls	r3, r1
 80029a8:	494b      	ldr	r1, [pc, #300]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 80029aa:	4313      	orrs	r3, r2
 80029ac:	600b      	str	r3, [r1, #0]
 80029ae:	e065      	b.n	8002a7c <HAL_RCC_OscConfig+0x604>
 80029b0:	2301      	movs	r3, #1
 80029b2:	f8c7 3138 	str.w	r3, [r7, #312]	@ 0x138
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80029b6:	f8d7 3138 	ldr.w	r3, [r7, #312]	@ 0x138
 80029ba:	fa93 f3a3 	rbit	r3, r3
 80029be:	f8c7 3134 	str.w	r3, [r7, #308]	@ 0x134
  return result;
 80029c2:	f8d7 3134 	ldr.w	r3, [r7, #308]	@ 0x134
      }
      else
      {
        /* Disable the Internal High Speed oscillator (HSI). */
        __HAL_RCC_HSI_DISABLE();
 80029c6:	fab3 f383 	clz	r3, r3
 80029ca:	b2db      	uxtb	r3, r3
 80029cc:	f103 5384 	add.w	r3, r3, #276824064	@ 0x10800000
 80029d0:	f503 1384 	add.w	r3, r3, #1081344	@ 0x108000
 80029d4:	009b      	lsls	r3, r3, #2
 80029d6:	461a      	mov	r2, r3
 80029d8:	2300      	movs	r3, #0
 80029da:	6013      	str	r3, [r2, #0]
        
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 80029dc:	f7fe f810 	bl	8000a00 <HAL_GetTick>
 80029e0:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
        
        /* Wait till HSI is disabled */
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 80029e4:	e00a      	b.n	80029fc <HAL_RCC_OscConfig+0x584>
        {
          if((HAL_GetTick() - tickstart ) > HSI_TIMEOUT_VALUE)
 80029e6:	f7fe f80b 	bl	8000a00 <HAL_GetTick>
 80029ea:	4602      	mov	r2, r0
 80029ec:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 80029f0:	1ad3      	subs	r3, r2, r3
 80029f2:	2b02      	cmp	r3, #2
 80029f4:	d902      	bls.n	80029fc <HAL_RCC_OscConfig+0x584>
          {
            return HAL_TIMEOUT;
 80029f6:	2303      	movs	r3, #3
 80029f8:	f000 bd45 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
 80029fc:	2302      	movs	r3, #2
 80029fe:	f8c7 3130 	str.w	r3, [r7, #304]	@ 0x130
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002a02:	f8d7 3130 	ldr.w	r3, [r7, #304]	@ 0x130
 8002a06:	fa93 f3a3 	rbit	r3, r3
 8002a0a:	f8c7 312c 	str.w	r3, [r7, #300]	@ 0x12c
  return result;
 8002a0e:	f8d7 312c 	ldr.w	r3, [r7, #300]	@ 0x12c
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 8002a12:	fab3 f383 	clz	r3, r3
 8002a16:	b2db      	uxtb	r3, r3
 8002a18:	2b3f      	cmp	r3, #63	@ 0x3f
 8002a1a:	d802      	bhi.n	8002a22 <HAL_RCC_OscConfig+0x5aa>
 8002a1c:	4b2e      	ldr	r3, [pc, #184]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002a1e:	681b      	ldr	r3, [r3, #0]
 8002a20:	e013      	b.n	8002a4a <HAL_RCC_OscConfig+0x5d2>
 8002a22:	2302      	movs	r3, #2
 8002a24:	f8c7 3128 	str.w	r3, [r7, #296]	@ 0x128
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002a28:	f8d7 3128 	ldr.w	r3, [r7, #296]	@ 0x128
 8002a2c:	fa93 f3a3 	rbit	r3, r3
 8002a30:	f8c7 3124 	str.w	r3, [r7, #292]	@ 0x124
 8002a34:	2302      	movs	r3, #2
 8002a36:	f8c7 3120 	str.w	r3, [r7, #288]	@ 0x120
 8002a3a:	f8d7 3120 	ldr.w	r3, [r7, #288]	@ 0x120
 8002a3e:	fa93 f3a3 	rbit	r3, r3
 8002a42:	f8c7 311c 	str.w	r3, [r7, #284]	@ 0x11c
 8002a46:	4b24      	ldr	r3, [pc, #144]	@ (8002ad8 <HAL_RCC_OscConfig+0x660>)
 8002a48:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8002a4a:	2202      	movs	r2, #2
 8002a4c:	f8c7 2118 	str.w	r2, [r7, #280]	@ 0x118
 8002a50:	f8d7 2118 	ldr.w	r2, [r7, #280]	@ 0x118
 8002a54:	fa92 f2a2 	rbit	r2, r2
 8002a58:	f8c7 2114 	str.w	r2, [r7, #276]	@ 0x114
  return result;
 8002a5c:	f8d7 2114 	ldr.w	r2, [r7, #276]	@ 0x114
 8002a60:	fab2 f282 	clz	r2, r2
 8002a64:	b2d2      	uxtb	r2, r2
 8002a66:	f042 0220 	orr.w	r2, r2, #32
 8002a6a:	b2d2      	uxtb	r2, r2
 8002a6c:	f002 021f 	and.w	r2, r2, #31
 8002a70:	2101      	movs	r1, #1
 8002a72:	fa01 f202 	lsl.w	r2, r1, r2
 8002a76:	4013      	ands	r3, r2
 8002a78:	2b00      	cmp	r3, #0
 8002a7a:	d1b4      	bne.n	80029e6 <HAL_RCC_OscConfig+0x56e>
        }
      }
    }
  }
  /*------------------------------ LSI Configuration -------------------------*/ 
  if(((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 8002a7c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002a80:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002a84:	681b      	ldr	r3, [r3, #0]
 8002a86:	681b      	ldr	r3, [r3, #0]
 8002a88:	f003 0308 	and.w	r3, r3, #8
 8002a8c:	2b00      	cmp	r3, #0
 8002a8e:	f000 8115 	beq.w	8002cbc <HAL_RCC_OscConfig+0x844>
  {
    /* Check the parameters */
    assert_param(IS_RCC_LSI(RCC_OscInitStruct->LSIState));
    
    /* Check the LSI State */
    if(RCC_OscInitStruct->LSIState != RCC_LSI_OFF)
 8002a92:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002a96:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002a9a:	681b      	ldr	r3, [r3, #0]
 8002a9c:	699b      	ldr	r3, [r3, #24]
 8002a9e:	2b00      	cmp	r3, #0
 8002aa0:	d07e      	beq.n	8002ba0 <HAL_RCC_OscConfig+0x728>
 8002aa2:	2301      	movs	r3, #1
 8002aa4:	f8c7 3110 	str.w	r3, [r7, #272]	@ 0x110
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002aa8:	f8d7 3110 	ldr.w	r3, [r7, #272]	@ 0x110
 8002aac:	fa93 f3a3 	rbit	r3, r3
 8002ab0:	f8c7 310c 	str.w	r3, [r7, #268]	@ 0x10c
  return result;
 8002ab4:	f8d7 310c 	ldr.w	r3, [r7, #268]	@ 0x10c
    {
      /* Enable the Internal Low Speed oscillator (LSI). */
      __HAL_RCC_LSI_ENABLE();
 8002ab8:	fab3 f383 	clz	r3, r3
 8002abc:	b2db      	uxtb	r3, r3
 8002abe:	461a      	mov	r2, r3
 8002ac0:	4b06      	ldr	r3, [pc, #24]	@ (8002adc <HAL_RCC_OscConfig+0x664>)
 8002ac2:	4413      	add	r3, r2
 8002ac4:	009b      	lsls	r3, r3, #2
 8002ac6:	461a      	mov	r2, r3
 8002ac8:	2301      	movs	r3, #1
 8002aca:	6013      	str	r3, [r2, #0]
      
      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8002acc:	f7fd ff98 	bl	8000a00 <HAL_GetTick>
 8002ad0:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
      
      /* Wait till LSI is ready */  
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 8002ad4:	e00f      	b.n	8002af6 <HAL_RCC_OscConfig+0x67e>
 8002ad6:	bf00      	nop
 8002ad8:	******** 	.word	0x********
 8002adc:	10908120 	.word	0x10908120
      {
        if((HAL_GetTick() - tickstart ) > LSI_TIMEOUT_VALUE)
 8002ae0:	f7fd ff8e 	bl	8000a00 <HAL_GetTick>
 8002ae4:	4602      	mov	r2, r0
 8002ae6:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8002aea:	1ad3      	subs	r3, r2, r3
 8002aec:	2b02      	cmp	r3, #2
 8002aee:	d902      	bls.n	8002af6 <HAL_RCC_OscConfig+0x67e>
        {
          return HAL_TIMEOUT;
 8002af0:	2303      	movs	r3, #3
 8002af2:	f000 bcc8 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
 8002af6:	2302      	movs	r3, #2
 8002af8:	f8c7 3108 	str.w	r3, [r7, #264]	@ 0x108
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002afc:	f8d7 3108 	ldr.w	r3, [r7, #264]	@ 0x108
 8002b00:	fa93 f3a3 	rbit	r3, r3
 8002b04:	f8c7 3104 	str.w	r3, [r7, #260]	@ 0x104
 8002b08:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b0c:	f5a3 7380 	sub.w	r3, r3, #256	@ 0x100
 8002b10:	2202      	movs	r2, #2
 8002b12:	601a      	str	r2, [r3, #0]
 8002b14:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b18:	f5a3 7380 	sub.w	r3, r3, #256	@ 0x100
 8002b1c:	681b      	ldr	r3, [r3, #0]
 8002b1e:	fa93 f2a3 	rbit	r2, r3
 8002b22:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b26:	f5a3 7382 	sub.w	r3, r3, #260	@ 0x104
 8002b2a:	601a      	str	r2, [r3, #0]
 8002b2c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b30:	f5a3 7384 	sub.w	r3, r3, #264	@ 0x108
 8002b34:	2202      	movs	r2, #2
 8002b36:	601a      	str	r2, [r3, #0]
 8002b38:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b3c:	f5a3 7384 	sub.w	r3, r3, #264	@ 0x108
 8002b40:	681b      	ldr	r3, [r3, #0]
 8002b42:	fa93 f2a3 	rbit	r2, r3
 8002b46:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b4a:	f5a3 7386 	sub.w	r3, r3, #268	@ 0x10c
 8002b4e:	601a      	str	r2, [r3, #0]
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 8002b50:	4bb0      	ldr	r3, [pc, #704]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002b52:	6a5a      	ldr	r2, [r3, #36]	@ 0x24
 8002b54:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b58:	f5a3 7388 	sub.w	r3, r3, #272	@ 0x110
 8002b5c:	2102      	movs	r1, #2
 8002b5e:	6019      	str	r1, [r3, #0]
 8002b60:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b64:	f5a3 7388 	sub.w	r3, r3, #272	@ 0x110
 8002b68:	681b      	ldr	r3, [r3, #0]
 8002b6a:	fa93 f1a3 	rbit	r1, r3
 8002b6e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b72:	f5a3 738a 	sub.w	r3, r3, #276	@ 0x114
 8002b76:	6019      	str	r1, [r3, #0]
  return result;
 8002b78:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002b7c:	f5a3 738a 	sub.w	r3, r3, #276	@ 0x114
 8002b80:	681b      	ldr	r3, [r3, #0]
 8002b82:	fab3 f383 	clz	r3, r3
 8002b86:	b2db      	uxtb	r3, r3
 8002b88:	f043 0360 	orr.w	r3, r3, #96	@ 0x60
 8002b8c:	b2db      	uxtb	r3, r3
 8002b8e:	f003 031f 	and.w	r3, r3, #31
 8002b92:	2101      	movs	r1, #1
 8002b94:	fa01 f303 	lsl.w	r3, r1, r3
 8002b98:	4013      	ands	r3, r2
 8002b9a:	2b00      	cmp	r3, #0
 8002b9c:	d0a0      	beq.n	8002ae0 <HAL_RCC_OscConfig+0x668>
 8002b9e:	e08d      	b.n	8002cbc <HAL_RCC_OscConfig+0x844>
 8002ba0:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002ba4:	f5a3 738c 	sub.w	r3, r3, #280	@ 0x118
 8002ba8:	2201      	movs	r2, #1
 8002baa:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002bac:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002bb0:	f5a3 738c 	sub.w	r3, r3, #280	@ 0x118
 8002bb4:	681b      	ldr	r3, [r3, #0]
 8002bb6:	fa93 f2a3 	rbit	r2, r3
 8002bba:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002bbe:	f5a3 738e 	sub.w	r3, r3, #284	@ 0x11c
 8002bc2:	601a      	str	r2, [r3, #0]
  return result;
 8002bc4:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002bc8:	f5a3 738e 	sub.w	r3, r3, #284	@ 0x11c
 8002bcc:	681b      	ldr	r3, [r3, #0]
      }
    }
    else
    {
      /* Disable the Internal Low Speed oscillator (LSI). */
      __HAL_RCC_LSI_DISABLE();
 8002bce:	fab3 f383 	clz	r3, r3
 8002bd2:	b2db      	uxtb	r3, r3
 8002bd4:	461a      	mov	r2, r3
 8002bd6:	4b90      	ldr	r3, [pc, #576]	@ (8002e18 <HAL_RCC_OscConfig+0x9a0>)
 8002bd8:	4413      	add	r3, r2
 8002bda:	009b      	lsls	r3, r3, #2
 8002bdc:	461a      	mov	r2, r3
 8002bde:	2300      	movs	r3, #0
 8002be0:	6013      	str	r3, [r2, #0]
      
      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8002be2:	f7fd ff0d 	bl	8000a00 <HAL_GetTick>
 8002be6:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
      
      /* Wait till LSI is disabled */  
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 8002bea:	e00a      	b.n	8002c02 <HAL_RCC_OscConfig+0x78a>
      {
        if((HAL_GetTick() - tickstart ) > LSI_TIMEOUT_VALUE)
 8002bec:	f7fd ff08 	bl	8000a00 <HAL_GetTick>
 8002bf0:	4602      	mov	r2, r0
 8002bf2:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8002bf6:	1ad3      	subs	r3, r2, r3
 8002bf8:	2b02      	cmp	r3, #2
 8002bfa:	d902      	bls.n	8002c02 <HAL_RCC_OscConfig+0x78a>
        {
          return HAL_TIMEOUT;
 8002bfc:	2303      	movs	r3, #3
 8002bfe:	f000 bc42 	b.w	8003486 <HAL_RCC_OscConfig+0x100e>
 8002c02:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c06:	f5a3 7390 	sub.w	r3, r3, #288	@ 0x120
 8002c0a:	2202      	movs	r2, #2
 8002c0c:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002c0e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c12:	f5a3 7390 	sub.w	r3, r3, #288	@ 0x120
 8002c16:	681b      	ldr	r3, [r3, #0]
 8002c18:	fa93 f2a3 	rbit	r2, r3
 8002c1c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c20:	f5a3 7392 	sub.w	r3, r3, #292	@ 0x124
 8002c24:	601a      	str	r2, [r3, #0]
 8002c26:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c2a:	f5a3 7394 	sub.w	r3, r3, #296	@ 0x128
 8002c2e:	2202      	movs	r2, #2
 8002c30:	601a      	str	r2, [r3, #0]
 8002c32:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c36:	f5a3 7394 	sub.w	r3, r3, #296	@ 0x128
 8002c3a:	681b      	ldr	r3, [r3, #0]
 8002c3c:	fa93 f2a3 	rbit	r2, r3
 8002c40:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c44:	f5a3 7396 	sub.w	r3, r3, #300	@ 0x12c
 8002c48:	601a      	str	r2, [r3, #0]
 8002c4a:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c4e:	f5a3 7398 	sub.w	r3, r3, #304	@ 0x130
 8002c52:	2202      	movs	r2, #2
 8002c54:	601a      	str	r2, [r3, #0]
 8002c56:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c5a:	f5a3 7398 	sub.w	r3, r3, #304	@ 0x130
 8002c5e:	681b      	ldr	r3, [r3, #0]
 8002c60:	fa93 f2a3 	rbit	r2, r3
 8002c64:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c68:	f5a3 739a 	sub.w	r3, r3, #308	@ 0x134
 8002c6c:	601a      	str	r2, [r3, #0]
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 8002c6e:	4b69      	ldr	r3, [pc, #420]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002c70:	6a5a      	ldr	r2, [r3, #36]	@ 0x24
 8002c72:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c76:	f5a3 739c 	sub.w	r3, r3, #312	@ 0x138
 8002c7a:	2102      	movs	r1, #2
 8002c7c:	6019      	str	r1, [r3, #0]
 8002c7e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c82:	f5a3 739c 	sub.w	r3, r3, #312	@ 0x138
 8002c86:	681b      	ldr	r3, [r3, #0]
 8002c88:	fa93 f1a3 	rbit	r1, r3
 8002c8c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c90:	f5a3 739e 	sub.w	r3, r3, #316	@ 0x13c
 8002c94:	6019      	str	r1, [r3, #0]
  return result;
 8002c96:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002c9a:	f5a3 739e 	sub.w	r3, r3, #316	@ 0x13c
 8002c9e:	681b      	ldr	r3, [r3, #0]
 8002ca0:	fab3 f383 	clz	r3, r3
 8002ca4:	b2db      	uxtb	r3, r3
 8002ca6:	f043 0360 	orr.w	r3, r3, #96	@ 0x60
 8002caa:	b2db      	uxtb	r3, r3
 8002cac:	f003 031f 	and.w	r3, r3, #31
 8002cb0:	2101      	movs	r1, #1
 8002cb2:	fa01 f303 	lsl.w	r3, r1, r3
 8002cb6:	4013      	ands	r3, r2
 8002cb8:	2b00      	cmp	r3, #0
 8002cba:	d197      	bne.n	8002bec <HAL_RCC_OscConfig+0x774>
        }
      }
    }
  }
  /*------------------------------ LSE Configuration -------------------------*/ 
  if(((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 8002cbc:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002cc0:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002cc4:	681b      	ldr	r3, [r3, #0]
 8002cc6:	681b      	ldr	r3, [r3, #0]
 8002cc8:	f003 0304 	and.w	r3, r3, #4
 8002ccc:	2b00      	cmp	r3, #0
 8002cce:	f000 819e 	beq.w	800300e <HAL_RCC_OscConfig+0xb96>
  {
    FlagStatus       pwrclkchanged = RESET;
 8002cd2:	2300      	movs	r3, #0
 8002cd4:	f887 31ff 	strb.w	r3, [r7, #511]	@ 0x1ff
    /* Check the parameters */
    assert_param(IS_RCC_LSE(RCC_OscInitStruct->LSEState));

    /* Update LSE configuration in Backup Domain control register    */
    /* Requires to enable write access to Backup Domain of necessary */
    if(__HAL_RCC_PWR_IS_CLK_DISABLED())
 8002cd8:	4b4e      	ldr	r3, [pc, #312]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002cda:	69db      	ldr	r3, [r3, #28]
 8002cdc:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8002ce0:	2b00      	cmp	r3, #0
 8002ce2:	d116      	bne.n	8002d12 <HAL_RCC_OscConfig+0x89a>
    {
      __HAL_RCC_PWR_CLK_ENABLE();
 8002ce4:	4b4b      	ldr	r3, [pc, #300]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002ce6:	69db      	ldr	r3, [r3, #28]
 8002ce8:	4a4a      	ldr	r2, [pc, #296]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002cea:	f043 5380 	orr.w	r3, r3, #268435456	@ 0x10000000
 8002cee:	61d3      	str	r3, [r2, #28]
 8002cf0:	4b48      	ldr	r3, [pc, #288]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002cf2:	69db      	ldr	r3, [r3, #28]
 8002cf4:	f003 5280 	and.w	r2, r3, #268435456	@ 0x10000000
 8002cf8:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002cfc:	f5a3 73fc 	sub.w	r3, r3, #504	@ 0x1f8
 8002d00:	601a      	str	r2, [r3, #0]
 8002d02:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002d06:	f5a3 73fc 	sub.w	r3, r3, #504	@ 0x1f8
 8002d0a:	681b      	ldr	r3, [r3, #0]
      pwrclkchanged = SET;
 8002d0c:	2301      	movs	r3, #1
 8002d0e:	f887 31ff 	strb.w	r3, [r7, #511]	@ 0x1ff
    }
    
    if(HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8002d12:	4b42      	ldr	r3, [pc, #264]	@ (8002e1c <HAL_RCC_OscConfig+0x9a4>)
 8002d14:	681b      	ldr	r3, [r3, #0]
 8002d16:	f403 7380 	and.w	r3, r3, #256	@ 0x100
 8002d1a:	2b00      	cmp	r3, #0
 8002d1c:	d11a      	bne.n	8002d54 <HAL_RCC_OscConfig+0x8dc>
    {
      /* Enable write access to Backup domain */
      SET_BIT(PWR->CR, PWR_CR_DBP);
 8002d1e:	4b3f      	ldr	r3, [pc, #252]	@ (8002e1c <HAL_RCC_OscConfig+0x9a4>)
 8002d20:	681b      	ldr	r3, [r3, #0]
 8002d22:	4a3e      	ldr	r2, [pc, #248]	@ (8002e1c <HAL_RCC_OscConfig+0x9a4>)
 8002d24:	f443 7380 	orr.w	r3, r3, #256	@ 0x100
 8002d28:	6013      	str	r3, [r2, #0]
      
      /* Wait for Backup domain Write protection disable */
      tickstart = HAL_GetTick();
 8002d2a:	f7fd fe69 	bl	8000a00 <HAL_GetTick>
 8002d2e:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8

      while(HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8002d32:	e009      	b.n	8002d48 <HAL_RCC_OscConfig+0x8d0>
      {
        if((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 8002d34:	f7fd fe64 	bl	8000a00 <HAL_GetTick>
 8002d38:	4602      	mov	r2, r0
 8002d3a:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8002d3e:	1ad3      	subs	r3, r2, r3
 8002d40:	2b64      	cmp	r3, #100	@ 0x64
 8002d42:	d901      	bls.n	8002d48 <HAL_RCC_OscConfig+0x8d0>
        {
          return HAL_TIMEOUT;
 8002d44:	2303      	movs	r3, #3
 8002d46:	e39e      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
      while(HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8002d48:	4b34      	ldr	r3, [pc, #208]	@ (8002e1c <HAL_RCC_OscConfig+0x9a4>)
 8002d4a:	681b      	ldr	r3, [r3, #0]
 8002d4c:	f403 7380 	and.w	r3, r3, #256	@ 0x100
 8002d50:	2b00      	cmp	r3, #0
 8002d52:	d0ef      	beq.n	8002d34 <HAL_RCC_OscConfig+0x8bc>
        }
      }
    }

    /* Set the new LSE configuration -----------------------------------------*/
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8002d54:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002d58:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002d5c:	681b      	ldr	r3, [r3, #0]
 8002d5e:	68db      	ldr	r3, [r3, #12]
 8002d60:	2b01      	cmp	r3, #1
 8002d62:	d106      	bne.n	8002d72 <HAL_RCC_OscConfig+0x8fa>
 8002d64:	4b2b      	ldr	r3, [pc, #172]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002d66:	6a1b      	ldr	r3, [r3, #32]
 8002d68:	4a2a      	ldr	r2, [pc, #168]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002d6a:	f043 0301 	orr.w	r3, r3, #1
 8002d6e:	6213      	str	r3, [r2, #32]
 8002d70:	e035      	b.n	8002dde <HAL_RCC_OscConfig+0x966>
 8002d72:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002d76:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002d7a:	681b      	ldr	r3, [r3, #0]
 8002d7c:	68db      	ldr	r3, [r3, #12]
 8002d7e:	2b00      	cmp	r3, #0
 8002d80:	d10c      	bne.n	8002d9c <HAL_RCC_OscConfig+0x924>
 8002d82:	4b24      	ldr	r3, [pc, #144]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002d84:	6a1b      	ldr	r3, [r3, #32]
 8002d86:	4a23      	ldr	r2, [pc, #140]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002d88:	f023 0301 	bic.w	r3, r3, #1
 8002d8c:	6213      	str	r3, [r2, #32]
 8002d8e:	4b21      	ldr	r3, [pc, #132]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002d90:	6a1b      	ldr	r3, [r3, #32]
 8002d92:	4a20      	ldr	r2, [pc, #128]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002d94:	f023 0304 	bic.w	r3, r3, #4
 8002d98:	6213      	str	r3, [r2, #32]
 8002d9a:	e020      	b.n	8002dde <HAL_RCC_OscConfig+0x966>
 8002d9c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002da0:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002da4:	681b      	ldr	r3, [r3, #0]
 8002da6:	68db      	ldr	r3, [r3, #12]
 8002da8:	2b05      	cmp	r3, #5
 8002daa:	d10c      	bne.n	8002dc6 <HAL_RCC_OscConfig+0x94e>
 8002dac:	4b19      	ldr	r3, [pc, #100]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002dae:	6a1b      	ldr	r3, [r3, #32]
 8002db0:	4a18      	ldr	r2, [pc, #96]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002db2:	f043 0304 	orr.w	r3, r3, #4
 8002db6:	6213      	str	r3, [r2, #32]
 8002db8:	4b16      	ldr	r3, [pc, #88]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002dba:	6a1b      	ldr	r3, [r3, #32]
 8002dbc:	4a15      	ldr	r2, [pc, #84]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002dbe:	f043 0301 	orr.w	r3, r3, #1
 8002dc2:	6213      	str	r3, [r2, #32]
 8002dc4:	e00b      	b.n	8002dde <HAL_RCC_OscConfig+0x966>
 8002dc6:	4b13      	ldr	r3, [pc, #76]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002dc8:	6a1b      	ldr	r3, [r3, #32]
 8002dca:	4a12      	ldr	r2, [pc, #72]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002dcc:	f023 0301 	bic.w	r3, r3, #1
 8002dd0:	6213      	str	r3, [r2, #32]
 8002dd2:	4b10      	ldr	r3, [pc, #64]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002dd4:	6a1b      	ldr	r3, [r3, #32]
 8002dd6:	4a0f      	ldr	r2, [pc, #60]	@ (8002e14 <HAL_RCC_OscConfig+0x99c>)
 8002dd8:	f023 0304 	bic.w	r3, r3, #4
 8002ddc:	6213      	str	r3, [r2, #32]
    /* Check the LSE State */
    if(RCC_OscInitStruct->LSEState != RCC_LSE_OFF)
 8002dde:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002de2:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8002de6:	681b      	ldr	r3, [r3, #0]
 8002de8:	68db      	ldr	r3, [r3, #12]
 8002dea:	2b00      	cmp	r3, #0
 8002dec:	f000 8087 	beq.w	8002efe <HAL_RCC_OscConfig+0xa86>
    {
      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8002df0:	f7fd fe06 	bl	8000a00 <HAL_GetTick>
 8002df4:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
      
      /* Wait till LSE is ready */  
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8002df8:	e012      	b.n	8002e20 <HAL_RCC_OscConfig+0x9a8>
      {
        if((HAL_GetTick() - tickstart ) > RCC_LSE_TIMEOUT_VALUE)
 8002dfa:	f7fd fe01 	bl	8000a00 <HAL_GetTick>
 8002dfe:	4602      	mov	r2, r0
 8002e00:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8002e04:	1ad3      	subs	r3, r2, r3
 8002e06:	f241 3288 	movw	r2, #5000	@ 0x1388
 8002e0a:	4293      	cmp	r3, r2
 8002e0c:	d908      	bls.n	8002e20 <HAL_RCC_OscConfig+0x9a8>
        {
          return HAL_TIMEOUT;
 8002e0e:	2303      	movs	r3, #3
 8002e10:	e339      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
 8002e12:	bf00      	nop
 8002e14:	******** 	.word	0x********
 8002e18:	10908120 	.word	0x10908120
 8002e1c:	40007000 	.word	0x40007000
 8002e20:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e24:	f5a3 73a0 	sub.w	r3, r3, #320	@ 0x140
 8002e28:	2202      	movs	r2, #2
 8002e2a:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002e2c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e30:	f5a3 73a0 	sub.w	r3, r3, #320	@ 0x140
 8002e34:	681b      	ldr	r3, [r3, #0]
 8002e36:	fa93 f2a3 	rbit	r2, r3
 8002e3a:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e3e:	f5a3 73a2 	sub.w	r3, r3, #324	@ 0x144
 8002e42:	601a      	str	r2, [r3, #0]
 8002e44:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e48:	f5a3 73a4 	sub.w	r3, r3, #328	@ 0x148
 8002e4c:	2202      	movs	r2, #2
 8002e4e:	601a      	str	r2, [r3, #0]
 8002e50:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e54:	f5a3 73a4 	sub.w	r3, r3, #328	@ 0x148
 8002e58:	681b      	ldr	r3, [r3, #0]
 8002e5a:	fa93 f2a3 	rbit	r2, r3
 8002e5e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e62:	f5a3 73a6 	sub.w	r3, r3, #332	@ 0x14c
 8002e66:	601a      	str	r2, [r3, #0]
  return result;
 8002e68:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e6c:	f5a3 73a6 	sub.w	r3, r3, #332	@ 0x14c
 8002e70:	681b      	ldr	r3, [r3, #0]
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8002e72:	fab3 f383 	clz	r3, r3
 8002e76:	b2db      	uxtb	r3, r3
 8002e78:	f023 035f 	bic.w	r3, r3, #95	@ 0x5f
 8002e7c:	b2db      	uxtb	r3, r3
 8002e7e:	2b00      	cmp	r3, #0
 8002e80:	d102      	bne.n	8002e88 <HAL_RCC_OscConfig+0xa10>
 8002e82:	4b98      	ldr	r3, [pc, #608]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 8002e84:	6a1b      	ldr	r3, [r3, #32]
 8002e86:	e013      	b.n	8002eb0 <HAL_RCC_OscConfig+0xa38>
 8002e88:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e8c:	f5a3 73a8 	sub.w	r3, r3, #336	@ 0x150
 8002e90:	2202      	movs	r2, #2
 8002e92:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002e94:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002e98:	f5a3 73a8 	sub.w	r3, r3, #336	@ 0x150
 8002e9c:	681b      	ldr	r3, [r3, #0]
 8002e9e:	fa93 f2a3 	rbit	r2, r3
 8002ea2:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002ea6:	f5a3 73aa 	sub.w	r3, r3, #340	@ 0x154
 8002eaa:	601a      	str	r2, [r3, #0]
 8002eac:	4b8d      	ldr	r3, [pc, #564]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 8002eae:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8002eb0:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002eb4:	f5a2 72ac 	sub.w	r2, r2, #344	@ 0x158
 8002eb8:	2102      	movs	r1, #2
 8002eba:	6011      	str	r1, [r2, #0]
 8002ebc:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002ec0:	f5a2 72ac 	sub.w	r2, r2, #344	@ 0x158
 8002ec4:	6812      	ldr	r2, [r2, #0]
 8002ec6:	fa92 f1a2 	rbit	r1, r2
 8002eca:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002ece:	f5a2 72ae 	sub.w	r2, r2, #348	@ 0x15c
 8002ed2:	6011      	str	r1, [r2, #0]
  return result;
 8002ed4:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002ed8:	f5a2 72ae 	sub.w	r2, r2, #348	@ 0x15c
 8002edc:	6812      	ldr	r2, [r2, #0]
 8002ede:	fab2 f282 	clz	r2, r2
 8002ee2:	b2d2      	uxtb	r2, r2
 8002ee4:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8002ee8:	b2d2      	uxtb	r2, r2
 8002eea:	f002 021f 	and.w	r2, r2, #31
 8002eee:	2101      	movs	r1, #1
 8002ef0:	fa01 f202 	lsl.w	r2, r1, r2
 8002ef4:	4013      	ands	r3, r2
 8002ef6:	2b00      	cmp	r3, #0
 8002ef8:	f43f af7f 	beq.w	8002dfa <HAL_RCC_OscConfig+0x982>
 8002efc:	e07d      	b.n	8002ffa <HAL_RCC_OscConfig+0xb82>
      }
    }
    else
    {
      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8002efe:	f7fd fd7f 	bl	8000a00 <HAL_GetTick>
 8002f02:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
      
      /* Wait till LSE is disabled */  
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 8002f06:	e00b      	b.n	8002f20 <HAL_RCC_OscConfig+0xaa8>
      {
        if((HAL_GetTick() - tickstart ) > RCC_LSE_TIMEOUT_VALUE)
 8002f08:	f7fd fd7a 	bl	8000a00 <HAL_GetTick>
 8002f0c:	4602      	mov	r2, r0
 8002f0e:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8002f12:	1ad3      	subs	r3, r2, r3
 8002f14:	f241 3288 	movw	r2, #5000	@ 0x1388
 8002f18:	4293      	cmp	r3, r2
 8002f1a:	d901      	bls.n	8002f20 <HAL_RCC_OscConfig+0xaa8>
        {
          return HAL_TIMEOUT;
 8002f1c:	2303      	movs	r3, #3
 8002f1e:	e2b2      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
 8002f20:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f24:	f5a3 73b0 	sub.w	r3, r3, #352	@ 0x160
 8002f28:	2202      	movs	r2, #2
 8002f2a:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002f2c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f30:	f5a3 73b0 	sub.w	r3, r3, #352	@ 0x160
 8002f34:	681b      	ldr	r3, [r3, #0]
 8002f36:	fa93 f2a3 	rbit	r2, r3
 8002f3a:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f3e:	f5a3 73b2 	sub.w	r3, r3, #356	@ 0x164
 8002f42:	601a      	str	r2, [r3, #0]
 8002f44:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f48:	f5a3 73b4 	sub.w	r3, r3, #360	@ 0x168
 8002f4c:	2202      	movs	r2, #2
 8002f4e:	601a      	str	r2, [r3, #0]
 8002f50:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f54:	f5a3 73b4 	sub.w	r3, r3, #360	@ 0x168
 8002f58:	681b      	ldr	r3, [r3, #0]
 8002f5a:	fa93 f2a3 	rbit	r2, r3
 8002f5e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f62:	f5a3 73b6 	sub.w	r3, r3, #364	@ 0x16c
 8002f66:	601a      	str	r2, [r3, #0]
  return result;
 8002f68:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f6c:	f5a3 73b6 	sub.w	r3, r3, #364	@ 0x16c
 8002f70:	681b      	ldr	r3, [r3, #0]
      while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 8002f72:	fab3 f383 	clz	r3, r3
 8002f76:	b2db      	uxtb	r3, r3
 8002f78:	f023 035f 	bic.w	r3, r3, #95	@ 0x5f
 8002f7c:	b2db      	uxtb	r3, r3
 8002f7e:	2b00      	cmp	r3, #0
 8002f80:	d102      	bne.n	8002f88 <HAL_RCC_OscConfig+0xb10>
 8002f82:	4b58      	ldr	r3, [pc, #352]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 8002f84:	6a1b      	ldr	r3, [r3, #32]
 8002f86:	e013      	b.n	8002fb0 <HAL_RCC_OscConfig+0xb38>
 8002f88:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f8c:	f5a3 73b8 	sub.w	r3, r3, #368	@ 0x170
 8002f90:	2202      	movs	r2, #2
 8002f92:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8002f94:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002f98:	f5a3 73b8 	sub.w	r3, r3, #368	@ 0x170
 8002f9c:	681b      	ldr	r3, [r3, #0]
 8002f9e:	fa93 f2a3 	rbit	r2, r3
 8002fa2:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8002fa6:	f5a3 73ba 	sub.w	r3, r3, #372	@ 0x174
 8002faa:	601a      	str	r2, [r3, #0]
 8002fac:	4b4d      	ldr	r3, [pc, #308]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 8002fae:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8002fb0:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002fb4:	f5a2 72bc 	sub.w	r2, r2, #376	@ 0x178
 8002fb8:	2102      	movs	r1, #2
 8002fba:	6011      	str	r1, [r2, #0]
 8002fbc:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002fc0:	f5a2 72bc 	sub.w	r2, r2, #376	@ 0x178
 8002fc4:	6812      	ldr	r2, [r2, #0]
 8002fc6:	fa92 f1a2 	rbit	r1, r2
 8002fca:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002fce:	f5a2 72be 	sub.w	r2, r2, #380	@ 0x17c
 8002fd2:	6011      	str	r1, [r2, #0]
  return result;
 8002fd4:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8002fd8:	f5a2 72be 	sub.w	r2, r2, #380	@ 0x17c
 8002fdc:	6812      	ldr	r2, [r2, #0]
 8002fde:	fab2 f282 	clz	r2, r2
 8002fe2:	b2d2      	uxtb	r2, r2
 8002fe4:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8002fe8:	b2d2      	uxtb	r2, r2
 8002fea:	f002 021f 	and.w	r2, r2, #31
 8002fee:	2101      	movs	r1, #1
 8002ff0:	fa01 f202 	lsl.w	r2, r1, r2
 8002ff4:	4013      	ands	r3, r2
 8002ff6:	2b00      	cmp	r3, #0
 8002ff8:	d186      	bne.n	8002f08 <HAL_RCC_OscConfig+0xa90>
        }
      }
    }

    /* Require to disable power clock if necessary */
    if(pwrclkchanged == SET)
 8002ffa:	f897 31ff 	ldrb.w	r3, [r7, #511]	@ 0x1ff
 8002ffe:	2b01      	cmp	r3, #1
 8003000:	d105      	bne.n	800300e <HAL_RCC_OscConfig+0xb96>
    {
      __HAL_RCC_PWR_CLK_DISABLE();
 8003002:	4b38      	ldr	r3, [pc, #224]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 8003004:	69db      	ldr	r3, [r3, #28]
 8003006:	4a37      	ldr	r2, [pc, #220]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 8003008:	f023 5380 	bic.w	r3, r3, #268435456	@ 0x10000000
 800300c:	61d3      	str	r3, [r2, #28]
  }

  /*-------------------------------- PLL Configuration -----------------------*/
  /* Check the parameters */
  assert_param(IS_RCC_PLL(RCC_OscInitStruct->PLL.PLLState));
  if ((RCC_OscInitStruct->PLL.PLLState) != RCC_PLL_NONE)
 800300e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003012:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8003016:	681b      	ldr	r3, [r3, #0]
 8003018:	69db      	ldr	r3, [r3, #28]
 800301a:	2b00      	cmp	r3, #0
 800301c:	f000 8232 	beq.w	8003484 <HAL_RCC_OscConfig+0x100c>
  {
    /* Check if the PLL is used as system clock or not */
    if(__HAL_RCC_GET_SYSCLK_SOURCE() != RCC_SYSCLKSOURCE_STATUS_PLLCLK)
 8003020:	4b30      	ldr	r3, [pc, #192]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 8003022:	685b      	ldr	r3, [r3, #4]
 8003024:	f003 030c 	and.w	r3, r3, #12
 8003028:	2b08      	cmp	r3, #8
 800302a:	f000 8201 	beq.w	8003430 <HAL_RCC_OscConfig+0xfb8>
    { 
      if((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 800302e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003032:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8003036:	681b      	ldr	r3, [r3, #0]
 8003038:	69db      	ldr	r3, [r3, #28]
 800303a:	2b02      	cmp	r3, #2
 800303c:	f040 8157 	bne.w	80032ee <HAL_RCC_OscConfig+0xe76>
 8003040:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003044:	f5a3 73c0 	sub.w	r3, r3, #384	@ 0x180
 8003048:	f04f 7280 	mov.w	r2, #16777216	@ 0x1000000
 800304c:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800304e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003052:	f5a3 73c0 	sub.w	r3, r3, #384	@ 0x180
 8003056:	681b      	ldr	r3, [r3, #0]
 8003058:	fa93 f2a3 	rbit	r2, r3
 800305c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003060:	f5a3 73c2 	sub.w	r3, r3, #388	@ 0x184
 8003064:	601a      	str	r2, [r3, #0]
  return result;
 8003066:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800306a:	f5a3 73c2 	sub.w	r3, r3, #388	@ 0x184
 800306e:	681b      	ldr	r3, [r3, #0]
#if   defined(RCC_CFGR_PLLSRC_HSI_PREDIV)
        assert_param(IS_RCC_PREDIV(RCC_OscInitStruct->PLL.PREDIV));
#endif
  
        /* Disable the main PLL. */
        __HAL_RCC_PLL_DISABLE();
 8003070:	fab3 f383 	clz	r3, r3
 8003074:	b2db      	uxtb	r3, r3
 8003076:	f103 5384 	add.w	r3, r3, #276824064	@ 0x10800000
 800307a:	f503 1384 	add.w	r3, r3, #1081344	@ 0x108000
 800307e:	009b      	lsls	r3, r3, #2
 8003080:	461a      	mov	r2, r3
 8003082:	2300      	movs	r3, #0
 8003084:	6013      	str	r3, [r2, #0]
        
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8003086:	f7fd fcbb 	bl	8000a00 <HAL_GetTick>
 800308a:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
        
        /* Wait till PLL is disabled */
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 800308e:	e009      	b.n	80030a4 <HAL_RCC_OscConfig+0xc2c>
        {
          if((HAL_GetTick() - tickstart ) > PLL_TIMEOUT_VALUE)
 8003090:	f7fd fcb6 	bl	8000a00 <HAL_GetTick>
 8003094:	4602      	mov	r2, r0
 8003096:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 800309a:	1ad3      	subs	r3, r2, r3
 800309c:	2b02      	cmp	r3, #2
 800309e:	d901      	bls.n	80030a4 <HAL_RCC_OscConfig+0xc2c>
          {
            return HAL_TIMEOUT;
 80030a0:	2303      	movs	r3, #3
 80030a2:	e1f0      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
 80030a4:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80030a8:	f5a3 73c4 	sub.w	r3, r3, #392	@ 0x188
 80030ac:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 80030b0:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80030b2:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80030b6:	f5a3 73c4 	sub.w	r3, r3, #392	@ 0x188
 80030ba:	681b      	ldr	r3, [r3, #0]
 80030bc:	fa93 f2a3 	rbit	r2, r3
 80030c0:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80030c4:	f5a3 73c6 	sub.w	r3, r3, #396	@ 0x18c
 80030c8:	601a      	str	r2, [r3, #0]
  return result;
 80030ca:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80030ce:	f5a3 73c6 	sub.w	r3, r3, #396	@ 0x18c
 80030d2:	681b      	ldr	r3, [r3, #0]
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 80030d4:	fab3 f383 	clz	r3, r3
 80030d8:	b2db      	uxtb	r3, r3
 80030da:	2b3f      	cmp	r3, #63	@ 0x3f
 80030dc:	d804      	bhi.n	80030e8 <HAL_RCC_OscConfig+0xc70>
 80030de:	4b01      	ldr	r3, [pc, #4]	@ (80030e4 <HAL_RCC_OscConfig+0xc6c>)
 80030e0:	681b      	ldr	r3, [r3, #0]
 80030e2:	e029      	b.n	8003138 <HAL_RCC_OscConfig+0xcc0>
 80030e4:	******** 	.word	0x********
 80030e8:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80030ec:	f5a3 73c8 	sub.w	r3, r3, #400	@ 0x190
 80030f0:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 80030f4:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80030f6:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80030fa:	f5a3 73c8 	sub.w	r3, r3, #400	@ 0x190
 80030fe:	681b      	ldr	r3, [r3, #0]
 8003100:	fa93 f2a3 	rbit	r2, r3
 8003104:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003108:	f5a3 73ca 	sub.w	r3, r3, #404	@ 0x194
 800310c:	601a      	str	r2, [r3, #0]
 800310e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003112:	f5a3 73cc 	sub.w	r3, r3, #408	@ 0x198
 8003116:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 800311a:	601a      	str	r2, [r3, #0]
 800311c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003120:	f5a3 73cc 	sub.w	r3, r3, #408	@ 0x198
 8003124:	681b      	ldr	r3, [r3, #0]
 8003126:	fa93 f2a3 	rbit	r2, r3
 800312a:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800312e:	f5a3 73ce 	sub.w	r3, r3, #412	@ 0x19c
 8003132:	601a      	str	r2, [r3, #0]
 8003134:	4bc3      	ldr	r3, [pc, #780]	@ (8003444 <HAL_RCC_OscConfig+0xfcc>)
 8003136:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8003138:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 800313c:	f5a2 72d0 	sub.w	r2, r2, #416	@ 0x1a0
 8003140:	f04f 7100 	mov.w	r1, #33554432	@ 0x2000000
 8003144:	6011      	str	r1, [r2, #0]
 8003146:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 800314a:	f5a2 72d0 	sub.w	r2, r2, #416	@ 0x1a0
 800314e:	6812      	ldr	r2, [r2, #0]
 8003150:	fa92 f1a2 	rbit	r1, r2
 8003154:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8003158:	f5a2 72d2 	sub.w	r2, r2, #420	@ 0x1a4
 800315c:	6011      	str	r1, [r2, #0]
  return result;
 800315e:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8003162:	f5a2 72d2 	sub.w	r2, r2, #420	@ 0x1a4
 8003166:	6812      	ldr	r2, [r2, #0]
 8003168:	fab2 f282 	clz	r2, r2
 800316c:	b2d2      	uxtb	r2, r2
 800316e:	f042 0220 	orr.w	r2, r2, #32
 8003172:	b2d2      	uxtb	r2, r2
 8003174:	f002 021f 	and.w	r2, r2, #31
 8003178:	2101      	movs	r1, #1
 800317a:	fa01 f202 	lsl.w	r2, r1, r2
 800317e:	4013      	ands	r3, r2
 8003180:	2b00      	cmp	r3, #0
 8003182:	d185      	bne.n	8003090 <HAL_RCC_OscConfig+0xc18>
        __HAL_RCC_PLL_CONFIG(RCC_OscInitStruct->PLL.PLLSource,
                             RCC_OscInitStruct->PLL.PREDIV,
                             RCC_OscInitStruct->PLL.PLLMUL);
#else
      /* Configure the main PLL clock source and multiplication factor. */
      __HAL_RCC_PLL_CONFIG(RCC_OscInitStruct->PLL.PLLSource,
 8003184:	4baf      	ldr	r3, [pc, #700]	@ (8003444 <HAL_RCC_OscConfig+0xfcc>)
 8003186:	685b      	ldr	r3, [r3, #4]
 8003188:	f423 1274 	bic.w	r2, r3, #3997696	@ 0x3d0000
 800318c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003190:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8003194:	681b      	ldr	r3, [r3, #0]
 8003196:	6a59      	ldr	r1, [r3, #36]	@ 0x24
 8003198:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800319c:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 80031a0:	681b      	ldr	r3, [r3, #0]
 80031a2:	6a1b      	ldr	r3, [r3, #32]
 80031a4:	430b      	orrs	r3, r1
 80031a6:	49a7      	ldr	r1, [pc, #668]	@ (8003444 <HAL_RCC_OscConfig+0xfcc>)
 80031a8:	4313      	orrs	r3, r2
 80031aa:	604b      	str	r3, [r1, #4]
 80031ac:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80031b0:	f5a3 73d4 	sub.w	r3, r3, #424	@ 0x1a8
 80031b4:	f04f 7280 	mov.w	r2, #16777216	@ 0x1000000
 80031b8:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80031ba:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80031be:	f5a3 73d4 	sub.w	r3, r3, #424	@ 0x1a8
 80031c2:	681b      	ldr	r3, [r3, #0]
 80031c4:	fa93 f2a3 	rbit	r2, r3
 80031c8:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80031cc:	f5a3 73d6 	sub.w	r3, r3, #428	@ 0x1ac
 80031d0:	601a      	str	r2, [r3, #0]
  return result;
 80031d2:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80031d6:	f5a3 73d6 	sub.w	r3, r3, #428	@ 0x1ac
 80031da:	681b      	ldr	r3, [r3, #0]
                           RCC_OscInitStruct->PLL.PLLMUL);
#endif /* RCC_CFGR_PLLSRC_HSI_PREDIV */
        /* Enable the main PLL. */
        __HAL_RCC_PLL_ENABLE();
 80031dc:	fab3 f383 	clz	r3, r3
 80031e0:	b2db      	uxtb	r3, r3
 80031e2:	f103 5384 	add.w	r3, r3, #276824064	@ 0x10800000
 80031e6:	f503 1384 	add.w	r3, r3, #1081344	@ 0x108000
 80031ea:	009b      	lsls	r3, r3, #2
 80031ec:	461a      	mov	r2, r3
 80031ee:	2301      	movs	r3, #1
 80031f0:	6013      	str	r3, [r2, #0]
        
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 80031f2:	f7fd fc05 	bl	8000a00 <HAL_GetTick>
 80031f6:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
        
        /* Wait till PLL is ready */
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  == RESET)
 80031fa:	e009      	b.n	8003210 <HAL_RCC_OscConfig+0xd98>
        {
          if((HAL_GetTick() - tickstart ) > PLL_TIMEOUT_VALUE)
 80031fc:	f7fd fc00 	bl	8000a00 <HAL_GetTick>
 8003200:	4602      	mov	r2, r0
 8003202:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8003206:	1ad3      	subs	r3, r2, r3
 8003208:	2b02      	cmp	r3, #2
 800320a:	d901      	bls.n	8003210 <HAL_RCC_OscConfig+0xd98>
          {
            return HAL_TIMEOUT;
 800320c:	2303      	movs	r3, #3
 800320e:	e13a      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
 8003210:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003214:	f5a3 73d8 	sub.w	r3, r3, #432	@ 0x1b0
 8003218:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 800321c:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800321e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003222:	f5a3 73d8 	sub.w	r3, r3, #432	@ 0x1b0
 8003226:	681b      	ldr	r3, [r3, #0]
 8003228:	fa93 f2a3 	rbit	r2, r3
 800322c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003230:	f5a3 73da 	sub.w	r3, r3, #436	@ 0x1b4
 8003234:	601a      	str	r2, [r3, #0]
  return result;
 8003236:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800323a:	f5a3 73da 	sub.w	r3, r3, #436	@ 0x1b4
 800323e:	681b      	ldr	r3, [r3, #0]
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  == RESET)
 8003240:	fab3 f383 	clz	r3, r3
 8003244:	b2db      	uxtb	r3, r3
 8003246:	2b3f      	cmp	r3, #63	@ 0x3f
 8003248:	d802      	bhi.n	8003250 <HAL_RCC_OscConfig+0xdd8>
 800324a:	4b7e      	ldr	r3, [pc, #504]	@ (8003444 <HAL_RCC_OscConfig+0xfcc>)
 800324c:	681b      	ldr	r3, [r3, #0]
 800324e:	e027      	b.n	80032a0 <HAL_RCC_OscConfig+0xe28>
 8003250:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003254:	f5a3 73dc 	sub.w	r3, r3, #440	@ 0x1b8
 8003258:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 800325c:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800325e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003262:	f5a3 73dc 	sub.w	r3, r3, #440	@ 0x1b8
 8003266:	681b      	ldr	r3, [r3, #0]
 8003268:	fa93 f2a3 	rbit	r2, r3
 800326c:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003270:	f5a3 73de 	sub.w	r3, r3, #444	@ 0x1bc
 8003274:	601a      	str	r2, [r3, #0]
 8003276:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800327a:	f5a3 73e0 	sub.w	r3, r3, #448	@ 0x1c0
 800327e:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 8003282:	601a      	str	r2, [r3, #0]
 8003284:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003288:	f5a3 73e0 	sub.w	r3, r3, #448	@ 0x1c0
 800328c:	681b      	ldr	r3, [r3, #0]
 800328e:	fa93 f2a3 	rbit	r2, r3
 8003292:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003296:	f5a3 73e2 	sub.w	r3, r3, #452	@ 0x1c4
 800329a:	601a      	str	r2, [r3, #0]
 800329c:	4b69      	ldr	r3, [pc, #420]	@ (8003444 <HAL_RCC_OscConfig+0xfcc>)
 800329e:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 80032a0:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 80032a4:	f5a2 72e4 	sub.w	r2, r2, #456	@ 0x1c8
 80032a8:	f04f 7100 	mov.w	r1, #33554432	@ 0x2000000
 80032ac:	6011      	str	r1, [r2, #0]
 80032ae:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 80032b2:	f5a2 72e4 	sub.w	r2, r2, #456	@ 0x1c8
 80032b6:	6812      	ldr	r2, [r2, #0]
 80032b8:	fa92 f1a2 	rbit	r1, r2
 80032bc:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 80032c0:	f5a2 72e6 	sub.w	r2, r2, #460	@ 0x1cc
 80032c4:	6011      	str	r1, [r2, #0]
  return result;
 80032c6:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 80032ca:	f5a2 72e6 	sub.w	r2, r2, #460	@ 0x1cc
 80032ce:	6812      	ldr	r2, [r2, #0]
 80032d0:	fab2 f282 	clz	r2, r2
 80032d4:	b2d2      	uxtb	r2, r2
 80032d6:	f042 0220 	orr.w	r2, r2, #32
 80032da:	b2d2      	uxtb	r2, r2
 80032dc:	f002 021f 	and.w	r2, r2, #31
 80032e0:	2101      	movs	r1, #1
 80032e2:	fa01 f202 	lsl.w	r2, r1, r2
 80032e6:	4013      	ands	r3, r2
 80032e8:	2b00      	cmp	r3, #0
 80032ea:	d087      	beq.n	80031fc <HAL_RCC_OscConfig+0xd84>
 80032ec:	e0ca      	b.n	8003484 <HAL_RCC_OscConfig+0x100c>
 80032ee:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80032f2:	f5a3 73e8 	sub.w	r3, r3, #464	@ 0x1d0
 80032f6:	f04f 7280 	mov.w	r2, #16777216	@ 0x1000000
 80032fa:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80032fc:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003300:	f5a3 73e8 	sub.w	r3, r3, #464	@ 0x1d0
 8003304:	681b      	ldr	r3, [r3, #0]
 8003306:	fa93 f2a3 	rbit	r2, r3
 800330a:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800330e:	f5a3 73ea 	sub.w	r3, r3, #468	@ 0x1d4
 8003312:	601a      	str	r2, [r3, #0]
  return result;
 8003314:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003318:	f5a3 73ea 	sub.w	r3, r3, #468	@ 0x1d4
 800331c:	681b      	ldr	r3, [r3, #0]
        }
      }
      else
      {
        /* Disable the main PLL. */
        __HAL_RCC_PLL_DISABLE();
 800331e:	fab3 f383 	clz	r3, r3
 8003322:	b2db      	uxtb	r3, r3
 8003324:	f103 5384 	add.w	r3, r3, #276824064	@ 0x10800000
 8003328:	f503 1384 	add.w	r3, r3, #1081344	@ 0x108000
 800332c:	009b      	lsls	r3, r3, #2
 800332e:	461a      	mov	r2, r3
 8003330:	2300      	movs	r3, #0
 8003332:	6013      	str	r3, [r2, #0]
 
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8003334:	f7fd fb64 	bl	8000a00 <HAL_GetTick>
 8003338:	f8c7 01f8 	str.w	r0, [r7, #504]	@ 0x1f8
        
        /* Wait till PLL is disabled */  
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 800333c:	e009      	b.n	8003352 <HAL_RCC_OscConfig+0xeda>
        {
          if((HAL_GetTick() - tickstart ) > PLL_TIMEOUT_VALUE)
 800333e:	f7fd fb5f 	bl	8000a00 <HAL_GetTick>
 8003342:	4602      	mov	r2, r0
 8003344:	f8d7 31f8 	ldr.w	r3, [r7, #504]	@ 0x1f8
 8003348:	1ad3      	subs	r3, r2, r3
 800334a:	2b02      	cmp	r3, #2
 800334c:	d901      	bls.n	8003352 <HAL_RCC_OscConfig+0xeda>
          {
            return HAL_TIMEOUT;
 800334e:	2303      	movs	r3, #3
 8003350:	e099      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
 8003352:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003356:	f5a3 73ec 	sub.w	r3, r3, #472	@ 0x1d8
 800335a:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 800335e:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8003360:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003364:	f5a3 73ec 	sub.w	r3, r3, #472	@ 0x1d8
 8003368:	681b      	ldr	r3, [r3, #0]
 800336a:	fa93 f2a3 	rbit	r2, r3
 800336e:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003372:	f5a3 73ee 	sub.w	r3, r3, #476	@ 0x1dc
 8003376:	601a      	str	r2, [r3, #0]
  return result;
 8003378:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800337c:	f5a3 73ee 	sub.w	r3, r3, #476	@ 0x1dc
 8003380:	681b      	ldr	r3, [r3, #0]
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY)  != RESET)
 8003382:	fab3 f383 	clz	r3, r3
 8003386:	b2db      	uxtb	r3, r3
 8003388:	2b3f      	cmp	r3, #63	@ 0x3f
 800338a:	d802      	bhi.n	8003392 <HAL_RCC_OscConfig+0xf1a>
 800338c:	4b2d      	ldr	r3, [pc, #180]	@ (8003444 <HAL_RCC_OscConfig+0xfcc>)
 800338e:	681b      	ldr	r3, [r3, #0]
 8003390:	e027      	b.n	80033e2 <HAL_RCC_OscConfig+0xf6a>
 8003392:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003396:	f5a3 73f0 	sub.w	r3, r3, #480	@ 0x1e0
 800339a:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 800339e:	601a      	str	r2, [r3, #0]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80033a0:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80033a4:	f5a3 73f0 	sub.w	r3, r3, #480	@ 0x1e0
 80033a8:	681b      	ldr	r3, [r3, #0]
 80033aa:	fa93 f2a3 	rbit	r2, r3
 80033ae:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80033b2:	f5a3 73f2 	sub.w	r3, r3, #484	@ 0x1e4
 80033b6:	601a      	str	r2, [r3, #0]
 80033b8:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80033bc:	f5a3 73f4 	sub.w	r3, r3, #488	@ 0x1e8
 80033c0:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 80033c4:	601a      	str	r2, [r3, #0]
 80033c6:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80033ca:	f5a3 73f4 	sub.w	r3, r3, #488	@ 0x1e8
 80033ce:	681b      	ldr	r3, [r3, #0]
 80033d0:	fa93 f2a3 	rbit	r2, r3
 80033d4:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 80033d8:	f5a3 73f6 	sub.w	r3, r3, #492	@ 0x1ec
 80033dc:	601a      	str	r2, [r3, #0]
 80033de:	4b19      	ldr	r3, [pc, #100]	@ (8003444 <HAL_RCC_OscConfig+0xfcc>)
 80033e0:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 80033e2:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 80033e6:	f5a2 72f8 	sub.w	r2, r2, #496	@ 0x1f0
 80033ea:	f04f 7100 	mov.w	r1, #33554432	@ 0x2000000
 80033ee:	6011      	str	r1, [r2, #0]
 80033f0:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 80033f4:	f5a2 72f8 	sub.w	r2, r2, #496	@ 0x1f0
 80033f8:	6812      	ldr	r2, [r2, #0]
 80033fa:	fa92 f1a2 	rbit	r1, r2
 80033fe:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 8003402:	f5a2 72fa 	sub.w	r2, r2, #500	@ 0x1f4
 8003406:	6011      	str	r1, [r2, #0]
  return result;
 8003408:	f507 7200 	add.w	r2, r7, #512	@ 0x200
 800340c:	f5a2 72fa 	sub.w	r2, r2, #500	@ 0x1f4
 8003410:	6812      	ldr	r2, [r2, #0]
 8003412:	fab2 f282 	clz	r2, r2
 8003416:	b2d2      	uxtb	r2, r2
 8003418:	f042 0220 	orr.w	r2, r2, #32
 800341c:	b2d2      	uxtb	r2, r2
 800341e:	f002 021f 	and.w	r2, r2, #31
 8003422:	2101      	movs	r1, #1
 8003424:	fa01 f202 	lsl.w	r2, r1, r2
 8003428:	4013      	ands	r3, r2
 800342a:	2b00      	cmp	r3, #0
 800342c:	d187      	bne.n	800333e <HAL_RCC_OscConfig+0xec6>
 800342e:	e029      	b.n	8003484 <HAL_RCC_OscConfig+0x100c>
      }
    }
    else
    {
      /* Check if there is a request to disable the PLL used as System clock source */
      if((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF)
 8003430:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003434:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8003438:	681b      	ldr	r3, [r3, #0]
 800343a:	69db      	ldr	r3, [r3, #28]
 800343c:	2b01      	cmp	r3, #1
 800343e:	d103      	bne.n	8003448 <HAL_RCC_OscConfig+0xfd0>
      {
        return HAL_ERROR;
 8003440:	2301      	movs	r3, #1
 8003442:	e020      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
 8003444:	******** 	.word	0x********
      }
      else
      {
        /* Do not return HAL_ERROR if request repeats the current configuration */
        pll_config = RCC->CFGR;
 8003448:	4b11      	ldr	r3, [pc, #68]	@ (8003490 <HAL_RCC_OscConfig+0x1018>)
 800344a:	685b      	ldr	r3, [r3, #4]
 800344c:	f8c7 31f4 	str.w	r3, [r7, #500]	@ 0x1f4
        pll_config2 = RCC->CFGR2;
        if((READ_BIT(pll_config, RCC_CFGR_PLLSRC)   != RCC_OscInitStruct->PLL.PLLSource) ||      
           (READ_BIT(pll_config, RCC_CFGR_PLLMUL)   != RCC_OscInitStruct->PLL.PLLMUL)    ||      
           (READ_BIT(pll_config2, RCC_CFGR2_PREDIV)  != RCC_OscInitStruct->PLL.PREDIV))     
#else
        if((READ_BIT(pll_config, RCC_CFGR_PLLSRC)   != RCC_OscInitStruct->PLL.PLLSource) ||      
 8003450:	f8d7 31f4 	ldr.w	r3, [r7, #500]	@ 0x1f4
 8003454:	f403 3280 	and.w	r2, r3, #65536	@ 0x10000
 8003458:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 800345c:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8003460:	681b      	ldr	r3, [r3, #0]
 8003462:	6a1b      	ldr	r3, [r3, #32]
 8003464:	429a      	cmp	r2, r3
 8003466:	d10b      	bne.n	8003480 <HAL_RCC_OscConfig+0x1008>
           (READ_BIT(pll_config, RCC_CFGR_PLLMUL)   != RCC_OscInitStruct->PLL.PLLMUL))
 8003468:	f8d7 31f4 	ldr.w	r3, [r7, #500]	@ 0x1f4
 800346c:	f403 1270 	and.w	r2, r3, #3932160	@ 0x3c0000
 8003470:	f507 7300 	add.w	r3, r7, #512	@ 0x200
 8003474:	f5a3 73fe 	sub.w	r3, r3, #508	@ 0x1fc
 8003478:	681b      	ldr	r3, [r3, #0]
 800347a:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
        if((READ_BIT(pll_config, RCC_CFGR_PLLSRC)   != RCC_OscInitStruct->PLL.PLLSource) ||      
 800347c:	429a      	cmp	r2, r3
 800347e:	d001      	beq.n	8003484 <HAL_RCC_OscConfig+0x100c>
#endif
        {
          return HAL_ERROR;
 8003480:	2301      	movs	r3, #1
 8003482:	e000      	b.n	8003486 <HAL_RCC_OscConfig+0x100e>
        }
      }
    }
  }

  return HAL_OK;
 8003484:	2300      	movs	r3, #0
}
 8003486:	4618      	mov	r0, r3
 8003488:	f507 7700 	add.w	r7, r7, #512	@ 0x200
 800348c:	46bd      	mov	sp, r7
 800348e:	bd80      	pop	{r7, pc}
 8003490:	******** 	.word	0x********

08003494 <HAL_RCC_ClockConfig>:
  *         You can use @ref HAL_RCC_GetClockConfig() function to know which clock is
  *         currently used as system clock source.
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_RCC_ClockConfig(RCC_ClkInitTypeDef  *RCC_ClkInitStruct, uint32_t FLatency)
{
 8003494:	b580      	push	{r7, lr}
 8003496:	b09e      	sub	sp, #120	@ 0x78
 8003498:	af00      	add	r7, sp, #0
 800349a:	6078      	str	r0, [r7, #4]
 800349c:	6039      	str	r1, [r7, #0]
  uint32_t tickstart = 0U;
 800349e:	2300      	movs	r3, #0
 80034a0:	677b      	str	r3, [r7, #116]	@ 0x74

  /* Check Null pointer */
  if(RCC_ClkInitStruct == NULL)
 80034a2:	687b      	ldr	r3, [r7, #4]
 80034a4:	2b00      	cmp	r3, #0
 80034a6:	d101      	bne.n	80034ac <HAL_RCC_ClockConfig+0x18>
  {
    return HAL_ERROR;
 80034a8:	2301      	movs	r3, #1
 80034aa:	e154      	b.n	8003756 <HAL_RCC_ClockConfig+0x2c2>
  /* To correctly read data from FLASH memory, the number of wait states (LATENCY) 
  must be correctly programmed according to the frequency of the CPU clock 
    (HCLK) of the device. */

  /* Increasing the number of wait states because of higher CPU frequency */
  if(FLatency > __HAL_FLASH_GET_LATENCY())
 80034ac:	4b89      	ldr	r3, [pc, #548]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80034ae:	681b      	ldr	r3, [r3, #0]
 80034b0:	f003 0307 	and.w	r3, r3, #7
 80034b4:	683a      	ldr	r2, [r7, #0]
 80034b6:	429a      	cmp	r2, r3
 80034b8:	d910      	bls.n	80034dc <HAL_RCC_ClockConfig+0x48>
  {    
    /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
    __HAL_FLASH_SET_LATENCY(FLatency);
 80034ba:	4b86      	ldr	r3, [pc, #536]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80034bc:	681b      	ldr	r3, [r3, #0]
 80034be:	f023 0207 	bic.w	r2, r3, #7
 80034c2:	4984      	ldr	r1, [pc, #528]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80034c4:	683b      	ldr	r3, [r7, #0]
 80034c6:	4313      	orrs	r3, r2
 80034c8:	600b      	str	r3, [r1, #0]
    
    /* Check that the new number of wait states is taken into account to access the Flash
    memory by reading the FLASH_ACR register */
    if(__HAL_FLASH_GET_LATENCY() != FLatency)
 80034ca:	4b82      	ldr	r3, [pc, #520]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80034cc:	681b      	ldr	r3, [r3, #0]
 80034ce:	f003 0307 	and.w	r3, r3, #7
 80034d2:	683a      	ldr	r2, [r7, #0]
 80034d4:	429a      	cmp	r2, r3
 80034d6:	d001      	beq.n	80034dc <HAL_RCC_ClockConfig+0x48>
    {
      return HAL_ERROR;
 80034d8:	2301      	movs	r3, #1
 80034da:	e13c      	b.n	8003756 <HAL_RCC_ClockConfig+0x2c2>
    }
  }

  /*-------------------------- HCLK Configuration --------------------------*/
  if(((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
 80034dc:	687b      	ldr	r3, [r7, #4]
 80034de:	681b      	ldr	r3, [r3, #0]
 80034e0:	f003 0302 	and.w	r3, r3, #2
 80034e4:	2b00      	cmp	r3, #0
 80034e6:	d008      	beq.n	80034fa <HAL_RCC_ClockConfig+0x66>
  {
    assert_param(IS_RCC_HCLK(RCC_ClkInitStruct->AHBCLKDivider));
    MODIFY_REG(RCC->CFGR, RCC_CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 80034e8:	4b7b      	ldr	r3, [pc, #492]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 80034ea:	685b      	ldr	r3, [r3, #4]
 80034ec:	f023 02f0 	bic.w	r2, r3, #240	@ 0xf0
 80034f0:	687b      	ldr	r3, [r7, #4]
 80034f2:	689b      	ldr	r3, [r3, #8]
 80034f4:	4978      	ldr	r1, [pc, #480]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 80034f6:	4313      	orrs	r3, r2
 80034f8:	604b      	str	r3, [r1, #4]
  }

  /*------------------------- SYSCLK Configuration ---------------------------*/ 
  if(((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_SYSCLK) == RCC_CLOCKTYPE_SYSCLK)
 80034fa:	687b      	ldr	r3, [r7, #4]
 80034fc:	681b      	ldr	r3, [r3, #0]
 80034fe:	f003 0301 	and.w	r3, r3, #1
 8003502:	2b00      	cmp	r3, #0
 8003504:	f000 80cd 	beq.w	80036a2 <HAL_RCC_ClockConfig+0x20e>
  {    
    assert_param(IS_RCC_SYSCLKSOURCE(RCC_ClkInitStruct->SYSCLKSource));
    
    /* HSE is selected as System Clock Source */
    if(RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_HSE)
 8003508:	687b      	ldr	r3, [r7, #4]
 800350a:	685b      	ldr	r3, [r3, #4]
 800350c:	2b01      	cmp	r3, #1
 800350e:	d137      	bne.n	8003580 <HAL_RCC_ClockConfig+0xec>
 8003510:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8003514:	66fb      	str	r3, [r7, #108]	@ 0x6c
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8003516:	6efb      	ldr	r3, [r7, #108]	@ 0x6c
 8003518:	fa93 f3a3 	rbit	r3, r3
 800351c:	673b      	str	r3, [r7, #112]	@ 0x70
  return result;
 800351e:	6f3b      	ldr	r3, [r7, #112]	@ 0x70
    {
      /* Check the HSE ready flag */  
      if(__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8003520:	fab3 f383 	clz	r3, r3
 8003524:	b2db      	uxtb	r3, r3
 8003526:	2b3f      	cmp	r3, #63	@ 0x3f
 8003528:	d802      	bhi.n	8003530 <HAL_RCC_ClockConfig+0x9c>
 800352a:	4b6b      	ldr	r3, [pc, #428]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 800352c:	681b      	ldr	r3, [r3, #0]
 800352e:	e00f      	b.n	8003550 <HAL_RCC_ClockConfig+0xbc>
 8003530:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8003534:	66bb      	str	r3, [r7, #104]	@ 0x68
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8003536:	6ebb      	ldr	r3, [r7, #104]	@ 0x68
 8003538:	fa93 f3a3 	rbit	r3, r3
 800353c:	667b      	str	r3, [r7, #100]	@ 0x64
 800353e:	f44f 3300 	mov.w	r3, #131072	@ 0x20000
 8003542:	663b      	str	r3, [r7, #96]	@ 0x60
 8003544:	6e3b      	ldr	r3, [r7, #96]	@ 0x60
 8003546:	fa93 f3a3 	rbit	r3, r3
 800354a:	65fb      	str	r3, [r7, #92]	@ 0x5c
 800354c:	4b62      	ldr	r3, [pc, #392]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 800354e:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8003550:	f44f 3200 	mov.w	r2, #131072	@ 0x20000
 8003554:	65ba      	str	r2, [r7, #88]	@ 0x58
 8003556:	6dba      	ldr	r2, [r7, #88]	@ 0x58
 8003558:	fa92 f2a2 	rbit	r2, r2
 800355c:	657a      	str	r2, [r7, #84]	@ 0x54
  return result;
 800355e:	6d7a      	ldr	r2, [r7, #84]	@ 0x54
 8003560:	fab2 f282 	clz	r2, r2
 8003564:	b2d2      	uxtb	r2, r2
 8003566:	f042 0220 	orr.w	r2, r2, #32
 800356a:	b2d2      	uxtb	r2, r2
 800356c:	f002 021f 	and.w	r2, r2, #31
 8003570:	2101      	movs	r1, #1
 8003572:	fa01 f202 	lsl.w	r2, r1, r2
 8003576:	4013      	ands	r3, r2
 8003578:	2b00      	cmp	r3, #0
 800357a:	d171      	bne.n	8003660 <HAL_RCC_ClockConfig+0x1cc>
      {
        return HAL_ERROR;
 800357c:	2301      	movs	r3, #1
 800357e:	e0ea      	b.n	8003756 <HAL_RCC_ClockConfig+0x2c2>
      }
    }
    /* PLL is selected as System Clock Source */
    else if(RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)
 8003580:	687b      	ldr	r3, [r7, #4]
 8003582:	685b      	ldr	r3, [r3, #4]
 8003584:	2b02      	cmp	r3, #2
 8003586:	d137      	bne.n	80035f8 <HAL_RCC_ClockConfig+0x164>
 8003588:	f04f 7300 	mov.w	r3, #33554432	@ 0x2000000
 800358c:	653b      	str	r3, [r7, #80]	@ 0x50
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800358e:	6d3b      	ldr	r3, [r7, #80]	@ 0x50
 8003590:	fa93 f3a3 	rbit	r3, r3
 8003594:	64fb      	str	r3, [r7, #76]	@ 0x4c
  return result;
 8003596:	6cfb      	ldr	r3, [r7, #76]	@ 0x4c
    {
      /* Check the PLL ready flag */  
      if(__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 8003598:	fab3 f383 	clz	r3, r3
 800359c:	b2db      	uxtb	r3, r3
 800359e:	2b3f      	cmp	r3, #63	@ 0x3f
 80035a0:	d802      	bhi.n	80035a8 <HAL_RCC_ClockConfig+0x114>
 80035a2:	4b4d      	ldr	r3, [pc, #308]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 80035a4:	681b      	ldr	r3, [r3, #0]
 80035a6:	e00f      	b.n	80035c8 <HAL_RCC_ClockConfig+0x134>
 80035a8:	f04f 7300 	mov.w	r3, #33554432	@ 0x2000000
 80035ac:	64bb      	str	r3, [r7, #72]	@ 0x48
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80035ae:	6cbb      	ldr	r3, [r7, #72]	@ 0x48
 80035b0:	fa93 f3a3 	rbit	r3, r3
 80035b4:	647b      	str	r3, [r7, #68]	@ 0x44
 80035b6:	f04f 7300 	mov.w	r3, #33554432	@ 0x2000000
 80035ba:	643b      	str	r3, [r7, #64]	@ 0x40
 80035bc:	6c3b      	ldr	r3, [r7, #64]	@ 0x40
 80035be:	fa93 f3a3 	rbit	r3, r3
 80035c2:	63fb      	str	r3, [r7, #60]	@ 0x3c
 80035c4:	4b44      	ldr	r3, [pc, #272]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 80035c6:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 80035c8:	f04f 7200 	mov.w	r2, #33554432	@ 0x2000000
 80035cc:	63ba      	str	r2, [r7, #56]	@ 0x38
 80035ce:	6bba      	ldr	r2, [r7, #56]	@ 0x38
 80035d0:	fa92 f2a2 	rbit	r2, r2
 80035d4:	637a      	str	r2, [r7, #52]	@ 0x34
  return result;
 80035d6:	6b7a      	ldr	r2, [r7, #52]	@ 0x34
 80035d8:	fab2 f282 	clz	r2, r2
 80035dc:	b2d2      	uxtb	r2, r2
 80035de:	f042 0220 	orr.w	r2, r2, #32
 80035e2:	b2d2      	uxtb	r2, r2
 80035e4:	f002 021f 	and.w	r2, r2, #31
 80035e8:	2101      	movs	r1, #1
 80035ea:	fa01 f202 	lsl.w	r2, r1, r2
 80035ee:	4013      	ands	r3, r2
 80035f0:	2b00      	cmp	r3, #0
 80035f2:	d135      	bne.n	8003660 <HAL_RCC_ClockConfig+0x1cc>
      {
        return HAL_ERROR;
 80035f4:	2301      	movs	r3, #1
 80035f6:	e0ae      	b.n	8003756 <HAL_RCC_ClockConfig+0x2c2>
 80035f8:	2302      	movs	r3, #2
 80035fa:	633b      	str	r3, [r7, #48]	@ 0x30
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80035fc:	6b3b      	ldr	r3, [r7, #48]	@ 0x30
 80035fe:	fa93 f3a3 	rbit	r3, r3
 8003602:	62fb      	str	r3, [r7, #44]	@ 0x2c
  return result;
 8003604:	6afb      	ldr	r3, [r7, #44]	@ 0x2c
    }
    /* HSI is selected as System Clock Source */
    else
    {
      /* Check the HSI ready flag */  
      if(__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8003606:	fab3 f383 	clz	r3, r3
 800360a:	b2db      	uxtb	r3, r3
 800360c:	2b3f      	cmp	r3, #63	@ 0x3f
 800360e:	d802      	bhi.n	8003616 <HAL_RCC_ClockConfig+0x182>
 8003610:	4b31      	ldr	r3, [pc, #196]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 8003612:	681b      	ldr	r3, [r3, #0]
 8003614:	e00d      	b.n	8003632 <HAL_RCC_ClockConfig+0x19e>
 8003616:	2302      	movs	r3, #2
 8003618:	62bb      	str	r3, [r7, #40]	@ 0x28
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800361a:	6abb      	ldr	r3, [r7, #40]	@ 0x28
 800361c:	fa93 f3a3 	rbit	r3, r3
 8003620:	627b      	str	r3, [r7, #36]	@ 0x24
 8003622:	2302      	movs	r3, #2
 8003624:	623b      	str	r3, [r7, #32]
 8003626:	6a3b      	ldr	r3, [r7, #32]
 8003628:	fa93 f3a3 	rbit	r3, r3
 800362c:	61fb      	str	r3, [r7, #28]
 800362e:	4b2a      	ldr	r3, [pc, #168]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 8003630:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8003632:	2202      	movs	r2, #2
 8003634:	61ba      	str	r2, [r7, #24]
 8003636:	69ba      	ldr	r2, [r7, #24]
 8003638:	fa92 f2a2 	rbit	r2, r2
 800363c:	617a      	str	r2, [r7, #20]
  return result;
 800363e:	697a      	ldr	r2, [r7, #20]
 8003640:	fab2 f282 	clz	r2, r2
 8003644:	b2d2      	uxtb	r2, r2
 8003646:	f042 0220 	orr.w	r2, r2, #32
 800364a:	b2d2      	uxtb	r2, r2
 800364c:	f002 021f 	and.w	r2, r2, #31
 8003650:	2101      	movs	r1, #1
 8003652:	fa01 f202 	lsl.w	r2, r1, r2
 8003656:	4013      	ands	r3, r2
 8003658:	2b00      	cmp	r3, #0
 800365a:	d101      	bne.n	8003660 <HAL_RCC_ClockConfig+0x1cc>
      {
        return HAL_ERROR;
 800365c:	2301      	movs	r3, #1
 800365e:	e07a      	b.n	8003756 <HAL_RCC_ClockConfig+0x2c2>
      }
    }

    __HAL_RCC_SYSCLK_CONFIG(RCC_ClkInitStruct->SYSCLKSource);
 8003660:	4b1d      	ldr	r3, [pc, #116]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 8003662:	685b      	ldr	r3, [r3, #4]
 8003664:	f023 0203 	bic.w	r2, r3, #3
 8003668:	687b      	ldr	r3, [r7, #4]
 800366a:	685b      	ldr	r3, [r3, #4]
 800366c:	491a      	ldr	r1, [pc, #104]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 800366e:	4313      	orrs	r3, r2
 8003670:	604b      	str	r3, [r1, #4]

    /* Get Start Tick */
    tickstart = HAL_GetTick();
 8003672:	f7fd f9c5 	bl	8000a00 <HAL_GetTick>
 8003676:	6778      	str	r0, [r7, #116]	@ 0x74
    
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 8003678:	e00a      	b.n	8003690 <HAL_RCC_ClockConfig+0x1fc>
    {
      if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 800367a:	f7fd f9c1 	bl	8000a00 <HAL_GetTick>
 800367e:	4602      	mov	r2, r0
 8003680:	6f7b      	ldr	r3, [r7, #116]	@ 0x74
 8003682:	1ad3      	subs	r3, r2, r3
 8003684:	f241 3288 	movw	r2, #5000	@ 0x1388
 8003688:	4293      	cmp	r3, r2
 800368a:	d901      	bls.n	8003690 <HAL_RCC_ClockConfig+0x1fc>
      {
        return HAL_TIMEOUT;
 800368c:	2303      	movs	r3, #3
 800368e:	e062      	b.n	8003756 <HAL_RCC_ClockConfig+0x2c2>
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 8003690:	4b11      	ldr	r3, [pc, #68]	@ (80036d8 <HAL_RCC_ClockConfig+0x244>)
 8003692:	685b      	ldr	r3, [r3, #4]
 8003694:	f003 020c 	and.w	r2, r3, #12
 8003698:	687b      	ldr	r3, [r7, #4]
 800369a:	685b      	ldr	r3, [r3, #4]
 800369c:	009b      	lsls	r3, r3, #2
 800369e:	429a      	cmp	r2, r3
 80036a0:	d1eb      	bne.n	800367a <HAL_RCC_ClockConfig+0x1e6>
      }
    }
  }
  /* Decreasing the number of wait states because of lower CPU frequency */
  if(FLatency < __HAL_FLASH_GET_LATENCY())
 80036a2:	4b0c      	ldr	r3, [pc, #48]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80036a4:	681b      	ldr	r3, [r3, #0]
 80036a6:	f003 0307 	and.w	r3, r3, #7
 80036aa:	683a      	ldr	r2, [r7, #0]
 80036ac:	429a      	cmp	r2, r3
 80036ae:	d215      	bcs.n	80036dc <HAL_RCC_ClockConfig+0x248>
  {    
    /* Program the new number of wait states to the LATENCY bits in the FLASH_ACR register */
    __HAL_FLASH_SET_LATENCY(FLatency);
 80036b0:	4b08      	ldr	r3, [pc, #32]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80036b2:	681b      	ldr	r3, [r3, #0]
 80036b4:	f023 0207 	bic.w	r2, r3, #7
 80036b8:	4906      	ldr	r1, [pc, #24]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80036ba:	683b      	ldr	r3, [r7, #0]
 80036bc:	4313      	orrs	r3, r2
 80036be:	600b      	str	r3, [r1, #0]
    
    /* Check that the new number of wait states is taken into account to access the Flash
    memory by reading the FLASH_ACR register */
    if(__HAL_FLASH_GET_LATENCY() != FLatency)
 80036c0:	4b04      	ldr	r3, [pc, #16]	@ (80036d4 <HAL_RCC_ClockConfig+0x240>)
 80036c2:	681b      	ldr	r3, [r3, #0]
 80036c4:	f003 0307 	and.w	r3, r3, #7
 80036c8:	683a      	ldr	r2, [r7, #0]
 80036ca:	429a      	cmp	r2, r3
 80036cc:	d006      	beq.n	80036dc <HAL_RCC_ClockConfig+0x248>
    {
      return HAL_ERROR;
 80036ce:	2301      	movs	r3, #1
 80036d0:	e041      	b.n	8003756 <HAL_RCC_ClockConfig+0x2c2>
 80036d2:	bf00      	nop
 80036d4:	******** 	.word	0x********
 80036d8:	******** 	.word	0x********
    }
  }    

  /*-------------------------- PCLK1 Configuration ---------------------------*/ 
  if(((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 80036dc:	687b      	ldr	r3, [r7, #4]
 80036de:	681b      	ldr	r3, [r3, #0]
 80036e0:	f003 0304 	and.w	r3, r3, #4
 80036e4:	2b00      	cmp	r3, #0
 80036e6:	d008      	beq.n	80036fa <HAL_RCC_ClockConfig+0x266>
  {
    assert_param(IS_RCC_PCLK(RCC_ClkInitStruct->APB1CLKDivider));
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_ClkInitStruct->APB1CLKDivider);
 80036e8:	4b1d      	ldr	r3, [pc, #116]	@ (8003760 <HAL_RCC_ClockConfig+0x2cc>)
 80036ea:	685b      	ldr	r3, [r3, #4]
 80036ec:	f423 62e0 	bic.w	r2, r3, #1792	@ 0x700
 80036f0:	687b      	ldr	r3, [r7, #4]
 80036f2:	68db      	ldr	r3, [r3, #12]
 80036f4:	491a      	ldr	r1, [pc, #104]	@ (8003760 <HAL_RCC_ClockConfig+0x2cc>)
 80036f6:	4313      	orrs	r3, r2
 80036f8:	604b      	str	r3, [r1, #4]
  }
  
  /*-------------------------- PCLK2 Configuration ---------------------------*/ 
  if(((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 80036fa:	687b      	ldr	r3, [r7, #4]
 80036fc:	681b      	ldr	r3, [r3, #0]
 80036fe:	f003 0308 	and.w	r3, r3, #8
 8003702:	2b00      	cmp	r3, #0
 8003704:	d009      	beq.n	800371a <HAL_RCC_ClockConfig+0x286>
  {
    assert_param(IS_RCC_PCLK(RCC_ClkInitStruct->APB2CLKDivider));
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, ((RCC_ClkInitStruct->APB2CLKDivider) << 3U));
 8003706:	4b16      	ldr	r3, [pc, #88]	@ (8003760 <HAL_RCC_ClockConfig+0x2cc>)
 8003708:	685b      	ldr	r3, [r3, #4]
 800370a:	f423 5260 	bic.w	r2, r3, #14336	@ 0x3800
 800370e:	687b      	ldr	r3, [r7, #4]
 8003710:	691b      	ldr	r3, [r3, #16]
 8003712:	00db      	lsls	r3, r3, #3
 8003714:	4912      	ldr	r1, [pc, #72]	@ (8003760 <HAL_RCC_ClockConfig+0x2cc>)
 8003716:	4313      	orrs	r3, r2
 8003718:	604b      	str	r3, [r1, #4]
  }
 
  /* Update the SystemCoreClock global variable */
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE)>> RCC_CFGR_HPRE_BITNUMBER];
 800371a:	f000 f829 	bl	8003770 <HAL_RCC_GetSysClockFreq>
 800371e:	4601      	mov	r1, r0
 8003720:	4b0f      	ldr	r3, [pc, #60]	@ (8003760 <HAL_RCC_ClockConfig+0x2cc>)
 8003722:	685b      	ldr	r3, [r3, #4]
 8003724:	f003 03f0 	and.w	r3, r3, #240	@ 0xf0
 8003728:	22f0      	movs	r2, #240	@ 0xf0
 800372a:	613a      	str	r2, [r7, #16]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 800372c:	693a      	ldr	r2, [r7, #16]
 800372e:	fa92 f2a2 	rbit	r2, r2
 8003732:	60fa      	str	r2, [r7, #12]
  return result;
 8003734:	68fa      	ldr	r2, [r7, #12]
 8003736:	fab2 f282 	clz	r2, r2
 800373a:	b2d2      	uxtb	r2, r2
 800373c:	40d3      	lsrs	r3, r2
 800373e:	4a09      	ldr	r2, [pc, #36]	@ (8003764 <HAL_RCC_ClockConfig+0x2d0>)
 8003740:	5cd3      	ldrb	r3, [r2, r3]
 8003742:	fa21 f303 	lsr.w	r3, r1, r3
 8003746:	4a08      	ldr	r2, [pc, #32]	@ (8003768 <HAL_RCC_ClockConfig+0x2d4>)
 8003748:	6013      	str	r3, [r2, #0]

  /* Configure the source of time base considering new system clocks settings*/
  HAL_InitTick (uwTickPrio);
 800374a:	4b08      	ldr	r3, [pc, #32]	@ (800376c <HAL_RCC_ClockConfig+0x2d8>)
 800374c:	681b      	ldr	r3, [r3, #0]
 800374e:	4618      	mov	r0, r3
 8003750:	f7fd f912 	bl	8000978 <HAL_InitTick>
  
  return HAL_OK;
 8003754:	2300      	movs	r3, #0
}
 8003756:	4618      	mov	r0, r3
 8003758:	3778      	adds	r7, #120	@ 0x78
 800375a:	46bd      	mov	sp, r7
 800375c:	bd80      	pop	{r7, pc}
 800375e:	bf00      	nop
 8003760:	******** 	.word	0x********
 8003764:	08003be4 	.word	0x08003be4
 8003768:	20000000 	.word	0x20000000
 800376c:	20000004 	.word	0x20000004

08003770 <HAL_RCC_GetSysClockFreq>:
  *         right SYSCLK value. Otherwise, any configuration based on this function will be incorrect.
  *         
  * @retval SYSCLK frequency
  */
uint32_t HAL_RCC_GetSysClockFreq(void)
{
 8003770:	b480      	push	{r7}
 8003772:	b087      	sub	sp, #28
 8003774:	af00      	add	r7, sp, #0
  uint32_t tmpreg = 0U, prediv = 0U, pllclk = 0U, pllmul = 0U;
 8003776:	2300      	movs	r3, #0
 8003778:	60fb      	str	r3, [r7, #12]
 800377a:	2300      	movs	r3, #0
 800377c:	60bb      	str	r3, [r7, #8]
 800377e:	2300      	movs	r3, #0
 8003780:	617b      	str	r3, [r7, #20]
 8003782:	2300      	movs	r3, #0
 8003784:	607b      	str	r3, [r7, #4]
  uint32_t sysclockfreq = 0U;
 8003786:	2300      	movs	r3, #0
 8003788:	613b      	str	r3, [r7, #16]
  
  tmpreg = RCC->CFGR;
 800378a:	4b1e      	ldr	r3, [pc, #120]	@ (8003804 <HAL_RCC_GetSysClockFreq+0x94>)
 800378c:	685b      	ldr	r3, [r3, #4]
 800378e:	60fb      	str	r3, [r7, #12]
  
  /* Get SYSCLK source -------------------------------------------------------*/
  switch (tmpreg & RCC_CFGR_SWS)
 8003790:	68fb      	ldr	r3, [r7, #12]
 8003792:	f003 030c 	and.w	r3, r3, #12
 8003796:	2b04      	cmp	r3, #4
 8003798:	d002      	beq.n	80037a0 <HAL_RCC_GetSysClockFreq+0x30>
 800379a:	2b08      	cmp	r3, #8
 800379c:	d003      	beq.n	80037a6 <HAL_RCC_GetSysClockFreq+0x36>
 800379e:	e026      	b.n	80037ee <HAL_RCC_GetSysClockFreq+0x7e>
  {
    case RCC_SYSCLKSOURCE_STATUS_HSE:  /* HSE used as system clock */
    {
      sysclockfreq = HSE_VALUE;
 80037a0:	4b19      	ldr	r3, [pc, #100]	@ (8003808 <HAL_RCC_GetSysClockFreq+0x98>)
 80037a2:	613b      	str	r3, [r7, #16]
      break;
 80037a4:	e026      	b.n	80037f4 <HAL_RCC_GetSysClockFreq+0x84>
    }
    case RCC_SYSCLKSOURCE_STATUS_PLLCLK:  /* PLL used as system clock */
    {
      pllmul = aPLLMULFactorTable[(uint32_t)(tmpreg & RCC_CFGR_PLLMUL) >> RCC_CFGR_PLLMUL_Pos];
 80037a6:	68fb      	ldr	r3, [r7, #12]
 80037a8:	0c9b      	lsrs	r3, r3, #18
 80037aa:	f003 030f 	and.w	r3, r3, #15
 80037ae:	4a17      	ldr	r2, [pc, #92]	@ (800380c <HAL_RCC_GetSysClockFreq+0x9c>)
 80037b0:	5cd3      	ldrb	r3, [r2, r3]
 80037b2:	607b      	str	r3, [r7, #4]
      prediv = aPredivFactorTable[(uint32_t)(RCC->CFGR2 & RCC_CFGR2_PREDIV) >> RCC_CFGR2_PREDIV_Pos];
 80037b4:	4b13      	ldr	r3, [pc, #76]	@ (8003804 <HAL_RCC_GetSysClockFreq+0x94>)
 80037b6:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 80037b8:	f003 030f 	and.w	r3, r3, #15
 80037bc:	4a14      	ldr	r2, [pc, #80]	@ (8003810 <HAL_RCC_GetSysClockFreq+0xa0>)
 80037be:	5cd3      	ldrb	r3, [r2, r3]
 80037c0:	60bb      	str	r3, [r7, #8]
#if defined(RCC_CFGR_PLLSRC_HSI_DIV2)
      if ((tmpreg & RCC_CFGR_PLLSRC) != RCC_PLLSOURCE_HSI)
 80037c2:	68fb      	ldr	r3, [r7, #12]
 80037c4:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 80037c8:	2b00      	cmp	r3, #0
 80037ca:	d008      	beq.n	80037de <HAL_RCC_GetSysClockFreq+0x6e>
      {
        /* HSE used as PLL clock source : PLLCLK = HSE/PREDIV * PLLMUL */
        pllclk = (uint32_t)((uint64_t) HSE_VALUE / (uint64_t) (prediv)) * ((uint64_t) pllmul);
 80037cc:	4a0e      	ldr	r2, [pc, #56]	@ (8003808 <HAL_RCC_GetSysClockFreq+0x98>)
 80037ce:	68bb      	ldr	r3, [r7, #8]
 80037d0:	fbb2 f2f3 	udiv	r2, r2, r3
 80037d4:	687b      	ldr	r3, [r7, #4]
 80037d6:	fb02 f303 	mul.w	r3, r2, r3
 80037da:	617b      	str	r3, [r7, #20]
 80037dc:	e004      	b.n	80037e8 <HAL_RCC_GetSysClockFreq+0x78>
      }
      else
      {
        /* HSI used as PLL clock source : PLLCLK = HSI/2 * PLLMUL */
        pllclk = (uint32_t)((uint64_t) (HSI_VALUE >> 1U) * ((uint64_t) pllmul));
 80037de:	687b      	ldr	r3, [r7, #4]
 80037e0:	4a0c      	ldr	r2, [pc, #48]	@ (8003814 <HAL_RCC_GetSysClockFreq+0xa4>)
 80037e2:	fb02 f303 	mul.w	r3, r2, r3
 80037e6:	617b      	str	r3, [r7, #20]
      {
        /* HSI used as PLL clock source : PLLCLK = HSI/PREDIV * PLLMUL */
        pllclk = (uint32_t)((uint64_t) HSI_VALUE / (uint64_t) (prediv)) * ((uint64_t) pllmul);
      }
#endif /* RCC_CFGR_PLLSRC_HSI_DIV2 */
      sysclockfreq = pllclk;
 80037e8:	697b      	ldr	r3, [r7, #20]
 80037ea:	613b      	str	r3, [r7, #16]
      break;
 80037ec:	e002      	b.n	80037f4 <HAL_RCC_GetSysClockFreq+0x84>
    }
    case RCC_SYSCLKSOURCE_STATUS_HSI:  /* HSI used as system clock source */
    default: /* HSI used as system clock */
    {
      sysclockfreq = HSI_VALUE;
 80037ee:	4b06      	ldr	r3, [pc, #24]	@ (8003808 <HAL_RCC_GetSysClockFreq+0x98>)
 80037f0:	613b      	str	r3, [r7, #16]
      break;
 80037f2:	bf00      	nop
    }
  }
  return sysclockfreq;
 80037f4:	693b      	ldr	r3, [r7, #16]
}
 80037f6:	4618      	mov	r0, r3
 80037f8:	371c      	adds	r7, #28
 80037fa:	46bd      	mov	sp, r7
 80037fc:	f85d 7b04 	ldr.w	r7, [sp], #4
 8003800:	4770      	bx	lr
 8003802:	bf00      	nop
 8003804:	******** 	.word	0x********
 8003808:	007a1200 	.word	0x007a1200
 800380c:	08003bf4 	.word	0x08003bf4
 8003810:	08003c04 	.word	0x08003c04
 8003814:	003d0900 	.word	0x003d0900

08003818 <HAL_RCCEx_PeriphCLKConfig>:
  *         When the TIMx clock source is PLL clock, so the TIMx clock is PLL clock x 2.
  *
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_RCCEx_PeriphCLKConfig(RCC_PeriphCLKInitTypeDef  *PeriphClkInit)
{
 8003818:	b580      	push	{r7, lr}
 800381a:	b092      	sub	sp, #72	@ 0x48
 800381c:	af00      	add	r7, sp, #0
 800381e:	6078      	str	r0, [r7, #4]
  uint32_t tickstart = 0U;
 8003820:	2300      	movs	r3, #0
 8003822:	643b      	str	r3, [r7, #64]	@ 0x40
  uint32_t temp_reg = 0U;
 8003824:	2300      	movs	r3, #0
 8003826:	63fb      	str	r3, [r7, #60]	@ 0x3c
  FlagStatus       pwrclkchanged = RESET;
 8003828:	2300      	movs	r3, #0
 800382a:	f887 3047 	strb.w	r3, [r7, #71]	@ 0x47
    
  /* Check the parameters */
  assert_param(IS_RCC_PERIPHCLOCK(PeriphClkInit->PeriphClockSelection));
  
  /*---------------------------- RTC configuration -------------------------------*/
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_RTC) == (RCC_PERIPHCLK_RTC))
 800382e:	687b      	ldr	r3, [r7, #4]
 8003830:	681b      	ldr	r3, [r3, #0]
 8003832:	f403 3380 	and.w	r3, r3, #65536	@ 0x10000
 8003836:	2b00      	cmp	r3, #0
 8003838:	f000 80d2 	beq.w	80039e0 <HAL_RCCEx_PeriphCLKConfig+0x1c8>


    /* As soon as function is called to change RTC clock source, activation of the 
       power domain is done. */
    /* Requires to enable write access to Backup Domain of necessary */
    if(__HAL_RCC_PWR_IS_CLK_DISABLED())
 800383c:	4b4d      	ldr	r3, [pc, #308]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 800383e:	69db      	ldr	r3, [r3, #28]
 8003840:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8003844:	2b00      	cmp	r3, #0
 8003846:	d10e      	bne.n	8003866 <HAL_RCCEx_PeriphCLKConfig+0x4e>
    {
      __HAL_RCC_PWR_CLK_ENABLE();
 8003848:	4b4a      	ldr	r3, [pc, #296]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 800384a:	69db      	ldr	r3, [r3, #28]
 800384c:	4a49      	ldr	r2, [pc, #292]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 800384e:	f043 5380 	orr.w	r3, r3, #268435456	@ 0x10000000
 8003852:	61d3      	str	r3, [r2, #28]
 8003854:	4b47      	ldr	r3, [pc, #284]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 8003856:	69db      	ldr	r3, [r3, #28]
 8003858:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 800385c:	60bb      	str	r3, [r7, #8]
 800385e:	68bb      	ldr	r3, [r7, #8]
      pwrclkchanged = SET;
 8003860:	2301      	movs	r3, #1
 8003862:	f887 3047 	strb.w	r3, [r7, #71]	@ 0x47
    }
    
    if(HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8003866:	4b44      	ldr	r3, [pc, #272]	@ (8003978 <HAL_RCCEx_PeriphCLKConfig+0x160>)
 8003868:	681b      	ldr	r3, [r3, #0]
 800386a:	f403 7380 	and.w	r3, r3, #256	@ 0x100
 800386e:	2b00      	cmp	r3, #0
 8003870:	d118      	bne.n	80038a4 <HAL_RCCEx_PeriphCLKConfig+0x8c>
    {
      /* Enable write access to Backup domain */
      SET_BIT(PWR->CR, PWR_CR_DBP);
 8003872:	4b41      	ldr	r3, [pc, #260]	@ (8003978 <HAL_RCCEx_PeriphCLKConfig+0x160>)
 8003874:	681b      	ldr	r3, [r3, #0]
 8003876:	4a40      	ldr	r2, [pc, #256]	@ (8003978 <HAL_RCCEx_PeriphCLKConfig+0x160>)
 8003878:	f443 7380 	orr.w	r3, r3, #256	@ 0x100
 800387c:	6013      	str	r3, [r2, #0]
      
      /* Wait for Backup domain Write protection disable */
      tickstart = HAL_GetTick();
 800387e:	f7fd f8bf 	bl	8000a00 <HAL_GetTick>
 8003882:	6438      	str	r0, [r7, #64]	@ 0x40
      
      while(HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8003884:	e008      	b.n	8003898 <HAL_RCCEx_PeriphCLKConfig+0x80>
      {
          if((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 8003886:	f7fd f8bb 	bl	8000a00 <HAL_GetTick>
 800388a:	4602      	mov	r2, r0
 800388c:	6c3b      	ldr	r3, [r7, #64]	@ 0x40
 800388e:	1ad3      	subs	r3, r2, r3
 8003890:	2b64      	cmp	r3, #100	@ 0x64
 8003892:	d901      	bls.n	8003898 <HAL_RCCEx_PeriphCLKConfig+0x80>
        {
          return HAL_TIMEOUT;
 8003894:	2303      	movs	r3, #3
 8003896:	e167      	b.n	8003b68 <HAL_RCCEx_PeriphCLKConfig+0x350>
      while(HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8003898:	4b37      	ldr	r3, [pc, #220]	@ (8003978 <HAL_RCCEx_PeriphCLKConfig+0x160>)
 800389a:	681b      	ldr	r3, [r3, #0]
 800389c:	f403 7380 	and.w	r3, r3, #256	@ 0x100
 80038a0:	2b00      	cmp	r3, #0
 80038a2:	d0f0      	beq.n	8003886 <HAL_RCCEx_PeriphCLKConfig+0x6e>
        }
      }
    }
    
    /* Reset the Backup domain only if the RTC Clock source selection is modified from reset value */ 
    temp_reg = (RCC->BDCR & RCC_BDCR_RTCSEL);
 80038a4:	4b33      	ldr	r3, [pc, #204]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 80038a6:	6a1b      	ldr	r3, [r3, #32]
 80038a8:	f403 7340 	and.w	r3, r3, #768	@ 0x300
 80038ac:	63fb      	str	r3, [r7, #60]	@ 0x3c
    if((temp_reg != 0x00000000U) && (temp_reg != (PeriphClkInit->RTCClockSelection & RCC_BDCR_RTCSEL)))
 80038ae:	6bfb      	ldr	r3, [r7, #60]	@ 0x3c
 80038b0:	2b00      	cmp	r3, #0
 80038b2:	f000 8082 	beq.w	80039ba <HAL_RCCEx_PeriphCLKConfig+0x1a2>
 80038b6:	687b      	ldr	r3, [r7, #4]
 80038b8:	685b      	ldr	r3, [r3, #4]
 80038ba:	f403 7340 	and.w	r3, r3, #768	@ 0x300
 80038be:	6bfa      	ldr	r2, [r7, #60]	@ 0x3c
 80038c0:	429a      	cmp	r2, r3
 80038c2:	d07a      	beq.n	80039ba <HAL_RCCEx_PeriphCLKConfig+0x1a2>
    {
      /* Store the content of BDCR register before the reset of Backup Domain */
      temp_reg = (RCC->BDCR & ~(RCC_BDCR_RTCSEL));
 80038c4:	4b2b      	ldr	r3, [pc, #172]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 80038c6:	6a1b      	ldr	r3, [r3, #32]
 80038c8:	f423 7340 	bic.w	r3, r3, #768	@ 0x300
 80038cc:	63fb      	str	r3, [r7, #60]	@ 0x3c
 80038ce:	f44f 3380 	mov.w	r3, #65536	@ 0x10000
 80038d2:	633b      	str	r3, [r7, #48]	@ 0x30
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80038d4:	6b3b      	ldr	r3, [r7, #48]	@ 0x30
 80038d6:	fa93 f3a3 	rbit	r3, r3
 80038da:	62fb      	str	r3, [r7, #44]	@ 0x2c
  return result;
 80038dc:	6afb      	ldr	r3, [r7, #44]	@ 0x2c
      /* RTC Clock selection can be changed only if the Backup Domain is reset */
      __HAL_RCC_BACKUPRESET_FORCE();
 80038de:	fab3 f383 	clz	r3, r3
 80038e2:	b2db      	uxtb	r3, r3
 80038e4:	461a      	mov	r2, r3
 80038e6:	4b25      	ldr	r3, [pc, #148]	@ (800397c <HAL_RCCEx_PeriphCLKConfig+0x164>)
 80038e8:	4413      	add	r3, r2
 80038ea:	009b      	lsls	r3, r3, #2
 80038ec:	461a      	mov	r2, r3
 80038ee:	2301      	movs	r3, #1
 80038f0:	6013      	str	r3, [r2, #0]
 80038f2:	f44f 3380 	mov.w	r3, #65536	@ 0x10000
 80038f6:	63bb      	str	r3, [r7, #56]	@ 0x38
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 80038f8:	6bbb      	ldr	r3, [r7, #56]	@ 0x38
 80038fa:	fa93 f3a3 	rbit	r3, r3
 80038fe:	637b      	str	r3, [r7, #52]	@ 0x34
  return result;
 8003900:	6b7b      	ldr	r3, [r7, #52]	@ 0x34
      __HAL_RCC_BACKUPRESET_RELEASE();
 8003902:	fab3 f383 	clz	r3, r3
 8003906:	b2db      	uxtb	r3, r3
 8003908:	461a      	mov	r2, r3
 800390a:	4b1c      	ldr	r3, [pc, #112]	@ (800397c <HAL_RCCEx_PeriphCLKConfig+0x164>)
 800390c:	4413      	add	r3, r2
 800390e:	009b      	lsls	r3, r3, #2
 8003910:	461a      	mov	r2, r3
 8003912:	2300      	movs	r3, #0
 8003914:	6013      	str	r3, [r2, #0]
      /* Restore the Content of BDCR register */
      RCC->BDCR = temp_reg;
 8003916:	4a17      	ldr	r2, [pc, #92]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 8003918:	6bfb      	ldr	r3, [r7, #60]	@ 0x3c
 800391a:	6213      	str	r3, [r2, #32]
    
      /* Wait for LSERDY if LSE was enabled */
      if (HAL_IS_BIT_SET(temp_reg, RCC_BDCR_LSEON))
 800391c:	6bfb      	ldr	r3, [r7, #60]	@ 0x3c
 800391e:	f003 0301 	and.w	r3, r3, #1
 8003922:	2b00      	cmp	r3, #0
 8003924:	d049      	beq.n	80039ba <HAL_RCCEx_PeriphCLKConfig+0x1a2>
      {
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 8003926:	f7fd f86b 	bl	8000a00 <HAL_GetTick>
 800392a:	6438      	str	r0, [r7, #64]	@ 0x40
        
        /* Wait till LSE is ready */  
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 800392c:	e00a      	b.n	8003944 <HAL_RCCEx_PeriphCLKConfig+0x12c>
        {
            if((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 800392e:	f7fd f867 	bl	8000a00 <HAL_GetTick>
 8003932:	4602      	mov	r2, r0
 8003934:	6c3b      	ldr	r3, [r7, #64]	@ 0x40
 8003936:	1ad3      	subs	r3, r2, r3
 8003938:	f241 3288 	movw	r2, #5000	@ 0x1388
 800393c:	4293      	cmp	r3, r2
 800393e:	d901      	bls.n	8003944 <HAL_RCCEx_PeriphCLKConfig+0x12c>
          {
            return HAL_TIMEOUT;
 8003940:	2303      	movs	r3, #3
 8003942:	e111      	b.n	8003b68 <HAL_RCCEx_PeriphCLKConfig+0x350>
 8003944:	2302      	movs	r3, #2
 8003946:	62bb      	str	r3, [r7, #40]	@ 0x28
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8003948:	6abb      	ldr	r3, [r7, #40]	@ 0x28
 800394a:	fa93 f3a3 	rbit	r3, r3
 800394e:	627b      	str	r3, [r7, #36]	@ 0x24
 8003950:	2302      	movs	r3, #2
 8003952:	623b      	str	r3, [r7, #32]
 8003954:	6a3b      	ldr	r3, [r7, #32]
 8003956:	fa93 f3a3 	rbit	r3, r3
 800395a:	61fb      	str	r3, [r7, #28]
  return result;
 800395c:	69fb      	ldr	r3, [r7, #28]
        while(__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 800395e:	fab3 f383 	clz	r3, r3
 8003962:	b2db      	uxtb	r3, r3
 8003964:	f023 035f 	bic.w	r3, r3, #95	@ 0x5f
 8003968:	b2db      	uxtb	r3, r3
 800396a:	2b00      	cmp	r3, #0
 800396c:	d108      	bne.n	8003980 <HAL_RCCEx_PeriphCLKConfig+0x168>
 800396e:	4b01      	ldr	r3, [pc, #4]	@ (8003974 <HAL_RCCEx_PeriphCLKConfig+0x15c>)
 8003970:	6a1b      	ldr	r3, [r3, #32]
 8003972:	e00d      	b.n	8003990 <HAL_RCCEx_PeriphCLKConfig+0x178>
 8003974:	******** 	.word	0x********
 8003978:	40007000 	.word	0x40007000
 800397c:	10908100 	.word	0x10908100
 8003980:	2302      	movs	r3, #2
 8003982:	61bb      	str	r3, [r7, #24]
   __ASM volatile ("rbit %0, %1" : "=r" (result) : "r" (value) );
 8003984:	69bb      	ldr	r3, [r7, #24]
 8003986:	fa93 f3a3 	rbit	r3, r3
 800398a:	617b      	str	r3, [r7, #20]
 800398c:	4b78      	ldr	r3, [pc, #480]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 800398e:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8003990:	2202      	movs	r2, #2
 8003992:	613a      	str	r2, [r7, #16]
 8003994:	693a      	ldr	r2, [r7, #16]
 8003996:	fa92 f2a2 	rbit	r2, r2
 800399a:	60fa      	str	r2, [r7, #12]
  return result;
 800399c:	68fa      	ldr	r2, [r7, #12]
 800399e:	fab2 f282 	clz	r2, r2
 80039a2:	b2d2      	uxtb	r2, r2
 80039a4:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 80039a8:	b2d2      	uxtb	r2, r2
 80039aa:	f002 021f 	and.w	r2, r2, #31
 80039ae:	2101      	movs	r1, #1
 80039b0:	fa01 f202 	lsl.w	r2, r1, r2
 80039b4:	4013      	ands	r3, r2
 80039b6:	2b00      	cmp	r3, #0
 80039b8:	d0b9      	beq.n	800392e <HAL_RCCEx_PeriphCLKConfig+0x116>
          }      
        }  
      }
    }
    __HAL_RCC_RTC_CONFIG(PeriphClkInit->RTCClockSelection); 
 80039ba:	4b6d      	ldr	r3, [pc, #436]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 80039bc:	6a1b      	ldr	r3, [r3, #32]
 80039be:	f423 7240 	bic.w	r2, r3, #768	@ 0x300
 80039c2:	687b      	ldr	r3, [r7, #4]
 80039c4:	685b      	ldr	r3, [r3, #4]
 80039c6:	496a      	ldr	r1, [pc, #424]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 80039c8:	4313      	orrs	r3, r2
 80039ca:	620b      	str	r3, [r1, #32]

    /* Require to disable power clock if necessary */
    if(pwrclkchanged == SET)
 80039cc:	f897 3047 	ldrb.w	r3, [r7, #71]	@ 0x47
 80039d0:	2b01      	cmp	r3, #1
 80039d2:	d105      	bne.n	80039e0 <HAL_RCCEx_PeriphCLKConfig+0x1c8>
    {
      __HAL_RCC_PWR_CLK_DISABLE();
 80039d4:	4b66      	ldr	r3, [pc, #408]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 80039d6:	69db      	ldr	r3, [r3, #28]
 80039d8:	4a65      	ldr	r2, [pc, #404]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 80039da:	f023 5380 	bic.w	r3, r3, #268435456	@ 0x10000000
 80039de:	61d3      	str	r3, [r2, #28]
    }
  }

  /*------------------------------- USART1 Configuration ------------------------*/
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_USART1) == RCC_PERIPHCLK_USART1)
 80039e0:	687b      	ldr	r3, [r7, #4]
 80039e2:	681b      	ldr	r3, [r3, #0]
 80039e4:	f003 0301 	and.w	r3, r3, #1
 80039e8:	2b00      	cmp	r3, #0
 80039ea:	d008      	beq.n	80039fe <HAL_RCCEx_PeriphCLKConfig+0x1e6>
  {
    /* Check the parameters */
    assert_param(IS_RCC_USART1CLKSOURCE(PeriphClkInit->Usart1ClockSelection));
    
    /* Configure the USART1 clock source */
    __HAL_RCC_USART1_CONFIG(PeriphClkInit->Usart1ClockSelection);
 80039ec:	4b60      	ldr	r3, [pc, #384]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 80039ee:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 80039f0:	f023 0203 	bic.w	r2, r3, #3
 80039f4:	687b      	ldr	r3, [r7, #4]
 80039f6:	689b      	ldr	r3, [r3, #8]
 80039f8:	495d      	ldr	r1, [pc, #372]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 80039fa:	4313      	orrs	r3, r2
 80039fc:	630b      	str	r3, [r1, #48]	@ 0x30
  }

#if defined(RCC_CFGR3_USART2SW)
  /*----------------------------- USART2 Configuration --------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_USART2) == RCC_PERIPHCLK_USART2)
 80039fe:	687b      	ldr	r3, [r7, #4]
 8003a00:	681b      	ldr	r3, [r3, #0]
 8003a02:	f003 0302 	and.w	r3, r3, #2
 8003a06:	2b00      	cmp	r3, #0
 8003a08:	d008      	beq.n	8003a1c <HAL_RCCEx_PeriphCLKConfig+0x204>
  {
    /* Check the parameters */
    assert_param(IS_RCC_USART2CLKSOURCE(PeriphClkInit->Usart2ClockSelection));
    
    /* Configure the USART2 clock source */
    __HAL_RCC_USART2_CONFIG(PeriphClkInit->Usart2ClockSelection);
 8003a0a:	4b59      	ldr	r3, [pc, #356]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a0c:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003a0e:	f423 3240 	bic.w	r2, r3, #196608	@ 0x30000
 8003a12:	687b      	ldr	r3, [r7, #4]
 8003a14:	68db      	ldr	r3, [r3, #12]
 8003a16:	4956      	ldr	r1, [pc, #344]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a18:	4313      	orrs	r3, r2
 8003a1a:	630b      	str	r3, [r1, #48]	@ 0x30
  }
#endif /* RCC_CFGR3_USART2SW */

#if defined(RCC_CFGR3_USART3SW)
  /*------------------------------ USART3 Configuration ------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_USART3) == RCC_PERIPHCLK_USART3)
 8003a1c:	687b      	ldr	r3, [r7, #4]
 8003a1e:	681b      	ldr	r3, [r3, #0]
 8003a20:	f003 0304 	and.w	r3, r3, #4
 8003a24:	2b00      	cmp	r3, #0
 8003a26:	d008      	beq.n	8003a3a <HAL_RCCEx_PeriphCLKConfig+0x222>
  {
    /* Check the parameters */
    assert_param(IS_RCC_USART3CLKSOURCE(PeriphClkInit->Usart3ClockSelection));
    
    /* Configure the USART3 clock source */
    __HAL_RCC_USART3_CONFIG(PeriphClkInit->Usart3ClockSelection);
 8003a28:	4b51      	ldr	r3, [pc, #324]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a2a:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003a2c:	f423 2240 	bic.w	r2, r3, #786432	@ 0xc0000
 8003a30:	687b      	ldr	r3, [r7, #4]
 8003a32:	691b      	ldr	r3, [r3, #16]
 8003a34:	494e      	ldr	r1, [pc, #312]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a36:	4313      	orrs	r3, r2
 8003a38:	630b      	str	r3, [r1, #48]	@ 0x30
  }
#endif /* RCC_CFGR3_USART3SW */

  /*------------------------------ I2C1 Configuration ------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_I2C1) == RCC_PERIPHCLK_I2C1)
 8003a3a:	687b      	ldr	r3, [r7, #4]
 8003a3c:	681b      	ldr	r3, [r3, #0]
 8003a3e:	f003 0320 	and.w	r3, r3, #32
 8003a42:	2b00      	cmp	r3, #0
 8003a44:	d008      	beq.n	8003a58 <HAL_RCCEx_PeriphCLKConfig+0x240>
  {
    /* Check the parameters */
    assert_param(IS_RCC_I2C1CLKSOURCE(PeriphClkInit->I2c1ClockSelection));
    
    /* Configure the I2C1 clock source */
    __HAL_RCC_I2C1_CONFIG(PeriphClkInit->I2c1ClockSelection);
 8003a46:	4b4a      	ldr	r3, [pc, #296]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a48:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003a4a:	f023 0210 	bic.w	r2, r3, #16
 8003a4e:	687b      	ldr	r3, [r7, #4]
 8003a50:	69db      	ldr	r3, [r3, #28]
 8003a52:	4947      	ldr	r1, [pc, #284]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a54:	4313      	orrs	r3, r2
 8003a56:	630b      	str	r3, [r1, #48]	@ 0x30
#if defined(STM32F302xE) || defined(STM32F303xE)\
 || defined(STM32F302xC) || defined(STM32F303xC)\
 || defined(STM32F302x8)                        \
 || defined(STM32F373xC)
  /*------------------------------ USB Configuration ------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_USB) == RCC_PERIPHCLK_USB)
 8003a58:	687b      	ldr	r3, [r7, #4]
 8003a5a:	681b      	ldr	r3, [r3, #0]
 8003a5c:	f403 3300 	and.w	r3, r3, #131072	@ 0x20000
 8003a60:	2b00      	cmp	r3, #0
 8003a62:	d008      	beq.n	8003a76 <HAL_RCCEx_PeriphCLKConfig+0x25e>
  {
    /* Check the parameters */
    assert_param(IS_RCC_USBCLKSOURCE(PeriphClkInit->USBClockSelection));
    
    /* Configure the USB clock source */
    __HAL_RCC_USB_CONFIG(PeriphClkInit->USBClockSelection);
 8003a64:	4b42      	ldr	r3, [pc, #264]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a66:	685b      	ldr	r3, [r3, #4]
 8003a68:	f423 0280 	bic.w	r2, r3, #4194304	@ 0x400000
 8003a6c:	687b      	ldr	r3, [r7, #4]
 8003a6e:	6b9b      	ldr	r3, [r3, #56]	@ 0x38
 8003a70:	493f      	ldr	r1, [pc, #252]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a72:	4313      	orrs	r3, r2
 8003a74:	604b      	str	r3, [r1, #4]
 || defined(STM32F302xC) || defined(STM32F303xC) || defined(STM32F358xx)\
 || defined(STM32F301x8) || defined(STM32F302x8) || defined(STM32F318xx)\
 || defined(STM32F373xC) || defined(STM32F378xx)

  /*------------------------------ I2C2 Configuration ------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_I2C2) == RCC_PERIPHCLK_I2C2)
 8003a76:	687b      	ldr	r3, [r7, #4]
 8003a78:	681b      	ldr	r3, [r3, #0]
 8003a7a:	f003 0340 	and.w	r3, r3, #64	@ 0x40
 8003a7e:	2b00      	cmp	r3, #0
 8003a80:	d008      	beq.n	8003a94 <HAL_RCCEx_PeriphCLKConfig+0x27c>
  {
    /* Check the parameters */
    assert_param(IS_RCC_I2C2CLKSOURCE(PeriphClkInit->I2c2ClockSelection));
    
    /* Configure the I2C2 clock source */
    __HAL_RCC_I2C2_CONFIG(PeriphClkInit->I2c2ClockSelection);
 8003a82:	4b3b      	ldr	r3, [pc, #236]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a84:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003a86:	f023 0220 	bic.w	r2, r3, #32
 8003a8a:	687b      	ldr	r3, [r7, #4]
 8003a8c:	6a1b      	ldr	r3, [r3, #32]
 8003a8e:	4938      	ldr	r1, [pc, #224]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003a90:	4313      	orrs	r3, r2
 8003a92:	630b      	str	r3, [r1, #48]	@ 0x30
  
#if defined(STM32F302xE) || defined(STM32F303xE) || defined(STM32F398xx)\
 || defined(STM32F302xC) || defined(STM32F303xC) || defined(STM32F358xx)

  /*------------------------------ UART4 Configuration ------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_UART4) == RCC_PERIPHCLK_UART4)
 8003a94:	687b      	ldr	r3, [r7, #4]
 8003a96:	681b      	ldr	r3, [r3, #0]
 8003a98:	f003 0308 	and.w	r3, r3, #8
 8003a9c:	2b00      	cmp	r3, #0
 8003a9e:	d008      	beq.n	8003ab2 <HAL_RCCEx_PeriphCLKConfig+0x29a>
  {
    /* Check the parameters */
    assert_param(IS_RCC_UART4CLKSOURCE(PeriphClkInit->Uart4ClockSelection));
    
    /* Configure the UART4 clock source */
    __HAL_RCC_UART4_CONFIG(PeriphClkInit->Uart4ClockSelection);
 8003aa0:	4b33      	ldr	r3, [pc, #204]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003aa2:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003aa4:	f423 1240 	bic.w	r2, r3, #3145728	@ 0x300000
 8003aa8:	687b      	ldr	r3, [r7, #4]
 8003aaa:	695b      	ldr	r3, [r3, #20]
 8003aac:	4930      	ldr	r1, [pc, #192]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003aae:	4313      	orrs	r3, r2
 8003ab0:	630b      	str	r3, [r1, #48]	@ 0x30
  }

  /*------------------------------ UART5 Configuration ------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_UART5) == RCC_PERIPHCLK_UART5)
 8003ab2:	687b      	ldr	r3, [r7, #4]
 8003ab4:	681b      	ldr	r3, [r3, #0]
 8003ab6:	f003 0310 	and.w	r3, r3, #16
 8003aba:	2b00      	cmp	r3, #0
 8003abc:	d008      	beq.n	8003ad0 <HAL_RCCEx_PeriphCLKConfig+0x2b8>
  {
    /* Check the parameters */
    assert_param(IS_RCC_UART5CLKSOURCE(PeriphClkInit->Uart5ClockSelection));
    
    /* Configure the UART5 clock source */
    __HAL_RCC_UART5_CONFIG(PeriphClkInit->Uart5ClockSelection);
 8003abe:	4b2c      	ldr	r3, [pc, #176]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003ac0:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003ac2:	f423 0240 	bic.w	r2, r3, #12582912	@ 0xc00000
 8003ac6:	687b      	ldr	r3, [r7, #4]
 8003ac8:	699b      	ldr	r3, [r3, #24]
 8003aca:	4929      	ldr	r1, [pc, #164]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003acc:	4313      	orrs	r3, r2
 8003ace:	630b      	str	r3, [r1, #48]	@ 0x30

#if defined(STM32F302xE) || defined(STM32F303xE) || defined(STM32F398xx)\
 || defined(STM32F302xC) || defined(STM32F303xC) || defined(STM32F358xx)\
 || defined(STM32F301x8) || defined(STM32F302x8) || defined(STM32F318xx)
  /*------------------------------ I2S Configuration ------------------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_I2S) == RCC_PERIPHCLK_I2S)
 8003ad0:	687b      	ldr	r3, [r7, #4]
 8003ad2:	681b      	ldr	r3, [r3, #0]
 8003ad4:	f403 7300 	and.w	r3, r3, #512	@ 0x200
 8003ad8:	2b00      	cmp	r3, #0
 8003ada:	d008      	beq.n	8003aee <HAL_RCCEx_PeriphCLKConfig+0x2d6>
  {
    /* Check the parameters */
    assert_param(IS_RCC_I2SCLKSOURCE(PeriphClkInit->I2sClockSelection));
    
    /* Configure the I2S clock source */
    __HAL_RCC_I2S_CONFIG(PeriphClkInit->I2sClockSelection);
 8003adc:	4b24      	ldr	r3, [pc, #144]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003ade:	685b      	ldr	r3, [r3, #4]
 8003ae0:	f423 0200 	bic.w	r2, r3, #8388608	@ 0x800000
 8003ae4:	687b      	ldr	r3, [r7, #4]
 8003ae6:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8003ae8:	4921      	ldr	r1, [pc, #132]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003aea:	4313      	orrs	r3, r2
 8003aec:	604b      	str	r3, [r1, #4]
#if defined(STM32F302xE) || defined(STM32F303xE) || defined(STM32F398xx)\
 || defined(STM32F302xC) || defined(STM32F303xC) || defined(STM32F358xx)\
 || defined(STM32F303x8) || defined(STM32F334x8) || defined(STM32F328xx)
      
  /*------------------------------ ADC1 & ADC2 clock Configuration -------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_ADC12) == RCC_PERIPHCLK_ADC12)
 8003aee:	687b      	ldr	r3, [r7, #4]
 8003af0:	681b      	ldr	r3, [r3, #0]
 8003af2:	f003 0380 	and.w	r3, r3, #128	@ 0x80
 8003af6:	2b00      	cmp	r3, #0
 8003af8:	d008      	beq.n	8003b0c <HAL_RCCEx_PeriphCLKConfig+0x2f4>
  {
    /* Check the parameters */
    assert_param(IS_RCC_ADC12PLLCLK_DIV(PeriphClkInit->Adc12ClockSelection));
    
    /* Configure the ADC12 clock source */
    __HAL_RCC_ADC12_CONFIG(PeriphClkInit->Adc12ClockSelection);
 8003afa:	4b1d      	ldr	r3, [pc, #116]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003afc:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8003afe:	f423 72f8 	bic.w	r2, r3, #496	@ 0x1f0
 8003b02:	687b      	ldr	r3, [r7, #4]
 8003b04:	6a5b      	ldr	r3, [r3, #36]	@ 0x24
 8003b06:	491a      	ldr	r1, [pc, #104]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003b08:	4313      	orrs	r3, r2
 8003b0a:	62cb      	str	r3, [r1, #44]	@ 0x2c
  
#if defined(STM32F303xE) || defined(STM32F398xx)\
 || defined(STM32F303xC) || defined(STM32F358xx)

  /*------------------------------ ADC3 & ADC4 clock Configuration -------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_ADC34) == RCC_PERIPHCLK_ADC34)
 8003b0c:	687b      	ldr	r3, [r7, #4]
 8003b0e:	681b      	ldr	r3, [r3, #0]
 8003b10:	f403 7380 	and.w	r3, r3, #256	@ 0x100
 8003b14:	2b00      	cmp	r3, #0
 8003b16:	d008      	beq.n	8003b2a <HAL_RCCEx_PeriphCLKConfig+0x312>
  {
    /* Check the parameters */
    assert_param(IS_RCC_ADC34PLLCLK_DIV(PeriphClkInit->Adc34ClockSelection));
    
    /* Configure the ADC34 clock source */
    __HAL_RCC_ADC34_CONFIG(PeriphClkInit->Adc34ClockSelection);
 8003b18:	4b15      	ldr	r3, [pc, #84]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003b1a:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8003b1c:	f423 5278 	bic.w	r2, r3, #15872	@ 0x3e00
 8003b20:	687b      	ldr	r3, [r7, #4]
 8003b22:	6a9b      	ldr	r3, [r3, #40]	@ 0x28
 8003b24:	4912      	ldr	r1, [pc, #72]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003b26:	4313      	orrs	r3, r2
 8003b28:	62cb      	str	r3, [r1, #44]	@ 0x2c
 || defined(STM32F302xC) || defined(STM32F303xC) || defined(STM32F358xx)\
 || defined(STM32F303x8) || defined(STM32F334x8) || defined(STM32F328xx)\
 || defined(STM32F301x8) || defined(STM32F302x8) || defined(STM32F318xx)

  /*------------------------------ TIM1 clock Configuration ----------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_TIM1) == RCC_PERIPHCLK_TIM1)
 8003b2a:	687b      	ldr	r3, [r7, #4]
 8003b2c:	681b      	ldr	r3, [r3, #0]
 8003b2e:	f403 5380 	and.w	r3, r3, #4096	@ 0x1000
 8003b32:	2b00      	cmp	r3, #0
 8003b34:	d008      	beq.n	8003b48 <HAL_RCCEx_PeriphCLKConfig+0x330>
  {
    /* Check the parameters */
    assert_param(IS_RCC_TIM1CLKSOURCE(PeriphClkInit->Tim1ClockSelection));
    
    /* Configure the TIM1 clock source */
    __HAL_RCC_TIM1_CONFIG(PeriphClkInit->Tim1ClockSelection);
 8003b36:	4b0e      	ldr	r3, [pc, #56]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003b38:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003b3a:	f423 7280 	bic.w	r2, r3, #256	@ 0x100
 8003b3e:	687b      	ldr	r3, [r7, #4]
 8003b40:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003b42:	490b      	ldr	r1, [pc, #44]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003b44:	4313      	orrs	r3, r2
 8003b46:	630b      	str	r3, [r1, #48]	@ 0x30
  
#if defined(STM32F303xE) || defined(STM32F398xx)\
 || defined(STM32F303xC) || defined(STM32F358xx)

  /*------------------------------ TIM8 clock Configuration ----------------*/ 
  if(((PeriphClkInit->PeriphClockSelection) & RCC_PERIPHCLK_TIM8) == RCC_PERIPHCLK_TIM8)
 8003b48:	687b      	ldr	r3, [r7, #4]
 8003b4a:	681b      	ldr	r3, [r3, #0]
 8003b4c:	f403 5300 	and.w	r3, r3, #8192	@ 0x2000
 8003b50:	2b00      	cmp	r3, #0
 8003b52:	d008      	beq.n	8003b66 <HAL_RCCEx_PeriphCLKConfig+0x34e>
  {
    /* Check the parameters */
    assert_param(IS_RCC_TIM8CLKSOURCE(PeriphClkInit->Tim8ClockSelection));
    
    /* Configure the TIM8 clock source */
    __HAL_RCC_TIM8_CONFIG(PeriphClkInit->Tim8ClockSelection);
 8003b54:	4b06      	ldr	r3, [pc, #24]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003b56:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8003b58:	f423 7200 	bic.w	r2, r3, #512	@ 0x200
 8003b5c:	687b      	ldr	r3, [r7, #4]
 8003b5e:	6b5b      	ldr	r3, [r3, #52]	@ 0x34
 8003b60:	4903      	ldr	r1, [pc, #12]	@ (8003b70 <HAL_RCCEx_PeriphCLKConfig+0x358>)
 8003b62:	4313      	orrs	r3, r2
 8003b64:	630b      	str	r3, [r1, #48]	@ 0x30
    __HAL_RCC_TIM20_CONFIG(PeriphClkInit->Tim20ClockSelection);
  }
#endif /* STM32F303xE || STM32F398xx */  

  
  return HAL_OK;
 8003b66:	2300      	movs	r3, #0
}
 8003b68:	4618      	mov	r0, r3
 8003b6a:	3748      	adds	r7, #72	@ 0x48
 8003b6c:	46bd      	mov	sp, r7
 8003b6e:	bd80      	pop	{r7, pc}
 8003b70:	******** 	.word	0x********

08003b74 <memset>:
 8003b74:	4402      	add	r2, r0
 8003b76:	4603      	mov	r3, r0
 8003b78:	4293      	cmp	r3, r2
 8003b7a:	d100      	bne.n	8003b7e <memset+0xa>
 8003b7c:	4770      	bx	lr
 8003b7e:	f803 1b01 	strb.w	r1, [r3], #1
 8003b82:	e7f9      	b.n	8003b78 <memset+0x4>

08003b84 <__libc_init_array>:
 8003b84:	b570      	push	{r4, r5, r6, lr}
 8003b86:	4d0d      	ldr	r5, [pc, #52]	@ (8003bbc <__libc_init_array+0x38>)
 8003b88:	4c0d      	ldr	r4, [pc, #52]	@ (8003bc0 <__libc_init_array+0x3c>)
 8003b8a:	1b64      	subs	r4, r4, r5
 8003b8c:	10a4      	asrs	r4, r4, #2
 8003b8e:	2600      	movs	r6, #0
 8003b90:	42a6      	cmp	r6, r4
 8003b92:	d109      	bne.n	8003ba8 <__libc_init_array+0x24>
 8003b94:	4d0b      	ldr	r5, [pc, #44]	@ (8003bc4 <__libc_init_array+0x40>)
 8003b96:	4c0c      	ldr	r4, [pc, #48]	@ (8003bc8 <__libc_init_array+0x44>)
 8003b98:	f000 f818 	bl	8003bcc <_init>
 8003b9c:	1b64      	subs	r4, r4, r5
 8003b9e:	10a4      	asrs	r4, r4, #2
 8003ba0:	2600      	movs	r6, #0
 8003ba2:	42a6      	cmp	r6, r4
 8003ba4:	d105      	bne.n	8003bb2 <__libc_init_array+0x2e>
 8003ba6:	bd70      	pop	{r4, r5, r6, pc}
 8003ba8:	f855 3b04 	ldr.w	r3, [r5], #4
 8003bac:	4798      	blx	r3
 8003bae:	3601      	adds	r6, #1
 8003bb0:	e7ee      	b.n	8003b90 <__libc_init_array+0xc>
 8003bb2:	f855 3b04 	ldr.w	r3, [r5], #4
 8003bb6:	4798      	blx	r3
 8003bb8:	3601      	adds	r6, #1
 8003bba:	e7f2      	b.n	8003ba2 <__libc_init_array+0x1e>
 8003bbc:	08003c14 	.word	0x08003c14
 8003bc0:	08003c14 	.word	0x08003c14
 8003bc4:	08003c14 	.word	0x08003c14
 8003bc8:	08003c18 	.word	0x08003c18

08003bcc <_init>:
 8003bcc:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8003bce:	bf00      	nop
 8003bd0:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8003bd2:	bc08      	pop	{r3}
 8003bd4:	469e      	mov	lr, r3
 8003bd6:	4770      	bx	lr

08003bd8 <_fini>:
 8003bd8:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8003bda:	bf00      	nop
 8003bdc:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8003bde:	bc08      	pop	{r3}
 8003be0:	469e      	mov	lr, r3
 8003be2:	4770      	bx	lr
