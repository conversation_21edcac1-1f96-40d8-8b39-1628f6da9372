eclipse.preferences.version=1
properties/MotorCubeMx.null.756568418/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.1402041300=com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.1402041300\=rcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.571070960\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1571007084\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.560019302\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1025089204\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.784615386\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.655517549\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1813428501\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1983979079\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1025479887\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1421822502\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1226002535\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.1929413083\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.1245282663\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.804487232\=rebuildState\\\=false\\r\\n\r\n
properties/MotorCubeMx.null.756568418/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.2065865595=com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.939680611\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.475922552\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.1048635665\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.306029464\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.1550150577\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1292253942\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1855906297\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1831502216\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.181699715\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1687745188\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.416767859\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.1018971741\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.208859730\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release.1491091539\=rebuildState\\\=true\\r\\n\r\n
properties/STMH_CM4.null.1880048104/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.652942507=com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.652942507\=rcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.770607696\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.691489603\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.179343430\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1841869983\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.744920395\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1851905738\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.767556759\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1887836171\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1561967169\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.2080823496\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.873085318\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.1534544977\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.2043532355\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.487251438\=rebuildState\\\=false\\r\\n\r\n
properties/STMH_CM4.null.1880048104/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.1183377623=com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.808983962\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.573950411\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.2043797785\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.865978703\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.1286811503\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1512213212\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.203972110\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.898512001\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.2002040568\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.920198725\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.796117240\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.1016794029\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.470258140\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release.1406278735\=rebuildState\\\=true\\r\\n\r\n
properties/STMH_CM7.null.1405569481/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.1077766526=com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.1077766526\=rcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.396483553\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1774610224\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.43794895\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1260725019\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.1325924978\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1882671521\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1947426929\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1593031277\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1960766116\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.153244976\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1400082714\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.206234391\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.1958208124\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.799653947\=rebuildState\\\=false\\r\\n\r\n
properties/WDHW.null.1395720635/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.589205752=com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.589205752\=rcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.1089874155\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.728246378\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.1063634470\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.786470057\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.2135024482\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.266750714\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.678658684\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.704279417\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.312445133\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1295850028\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1926123261\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.86881066\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.314079894\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.1948335799\=rebuildState\\\=false\\r\\n\r\n
properties/WDHW.null.1395720635/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.2025785412=com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.2038919825\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.833875282\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.428071484\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.864895903\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.775972257\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1067303010\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1272596179\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1718288444\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.852291467\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1636318070\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.2099271411\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.648415402\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.1713658669\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release.2064840359\=rebuildState\\\=true\\r\\n\r\n
properties/ZKSD\ Motor.null.98213672/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.18585832=com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.18585832\=rcState\\\=0\\r\\nrebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.271019821\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1481884773\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.224348435\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.33633165\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.21612163\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1353157068\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.894445294\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1421623200\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1128820886\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.611682690\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1537547424\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.121705190\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.845245237\=rebuildState\\\=false\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.debug.1830462962\=rebuildState\\\=false\\r\\n\r\n
properties/ZKSD\ Motor.null.98213672/com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.1293035198=com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.1239166440\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.287152314\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.921946027\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1015494065\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.1124695985\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1649036851\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1392448006\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.396662940\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1705509171\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.2056318189\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1493587483\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.1036705905\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.1578948207\=rebuildState\\\=true\\r\\n\r\ncom.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release.1488427553\=rebuildState\\\=true\\r\\n\r\n
