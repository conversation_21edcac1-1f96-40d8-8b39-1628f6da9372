#ifndef __AUDIO_PROCESS_H__
#define __AUDIO_PROCESS_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "main.h"

/* 音频处理配置 */
#define AUDIO_GAIN_DEFAULT     3.0f    // 增加增益到3倍
#define AUDIO_NOISE_GATE       50      // 噪声门限

/* 函数声明 */
void AudioProcess_Init(void);
void AudioProcess_ApplyGain(uint16_t* buffer, uint16_t length, float gain);
void AudioProcess_ApplyNoiseGate(uint16_t* buffer, uint16_t length, uint16_t threshold);
void AudioProcess_Process(uint16_t* input, uint16_t* output, uint16_t length);

#ifdef __cplusplus
}
#endif

#endif /* __AUDIO_PROCESS_H__ */ 