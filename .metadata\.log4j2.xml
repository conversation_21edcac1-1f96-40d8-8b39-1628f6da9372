<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">
    <ThresholdFilter level="INFO"/>
    <Appenders>
        <RollingFile name="DEBUGFILE" fileName="C:/Users/<USER>/STM32CubeIDE/workspace_1.19.0/.metadata/.ide.log" filePattern="C:/Users/<USER>/STM32CubeIDE/workspace_1.19.0/.metadata/.ide.log-%d{yyyy-MM-dd}.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%p] %c{1}:%L - %m%n" />
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true" />
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
        </RollingFile>
    </Appenders>
    <Loggers>
        <Root level="ALL">
            <AppenderRef ref="DEBUGFILE" />
        </Root>
    </Loggers>
</Configuration>