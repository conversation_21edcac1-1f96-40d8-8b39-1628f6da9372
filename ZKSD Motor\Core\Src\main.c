/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
volatile uint16_t adc_dma_buf = 0; // ADC DMA缓冲区
static uint32_t period = 0;
// static float phase = 0.0f; // 当前相位，单位度  // 删除未使用变量
const float phase_step = 1.0f; // 固定频率（可根据油门调整）
// Hall模块变量定义
volatile uint8_t U = 0;
volatile uint8_t V = 0;
volatile uint8_t W = 0;

// 测试模式变量 - 可以手动修改这些值来测试不同换相状态
volatile uint8_t test_mode = 0;  // 0=正常模式，1=测试模式
volatile uint8_t test_U = 0;     // 测试用的U相霍尔信号
volatile uint8_t test_V = 0;     // 测试用的V相霍尔信号  
volatile uint8_t test_W = 0;     // 测试用的W相霍尔信号

// 全局变量，记录当前导通的PWM通道
uint32_t current_pwm_channel = 0;
// 六步换相查表（上桥、下桥分开）
// 顺序：U V W
// 标准六步换相顺序：001->010->011->100->101->110->001...
const uint8_t upper_table[8][3] = {
    // U V W
    {0,0,0}, // 000（无效）
    {0,0,1}, // 001 W上桥
    {0,1,0}, // 010 V上桥
    {0,1,0}, // 011 V上桥
    {1,0,0}, // 100 U上桥
    {1,0,0}, // 101 U上桥
    {1,0,0}, // 110 U上桥
    {0,0,0}  // 111（无效）
};
const uint8_t lower_table[8][3] = {
    // U V W
    {0,0,0}, // 000
    {0,1,0}, // 001 V下桥
    {1,0,0}, // 010 U下桥
    {1,0,0}, // 011 U下桥
    {0,0,1}, // 100 W下桥
    {0,0,1}, // 101 W下桥
    {0,0,1}, // 110 W下桥
    {0,0,0}  // 111
};
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_ADC1_Init();
  MX_TIM1_Init();
  /* USER CODE BEGIN 2 */

  // 启动ADC DMA
  HAL_ADC_Start_DMA(&hadc1, (uint32_t*)&adc_dma_buf, 1);

  // 启动三相PWM和互补
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2);
  HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_3);
  HAL_TIMEx_PWMN_Start(&htim1, TIM_CHANNEL_1);
  HAL_TIMEx_PWMN_Start(&htim1, TIM_CHANNEL_2);
  HAL_TIMEx_PWMN_Start(&htim1, TIM_CHANNEL_3);
  __HAL_TIM_MOE_ENABLE(&htim1);

  period = __HAL_TIM_GET_AUTORELOAD(&htim1);

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    Hall_Update();
    
    // 测试模式：手动控制换相状态
    if (test_mode) {
        U = test_U;
        V = test_V; 
        W = test_W;
    }
    
    float vref = 3.3f;
    float adc_voltage = (adc_dma_buf / 4095.0f) * vref; // 分压后电压
    float throttle_voltage = adc_voltage * 2.0f;        // 还原真实电压

    float low_th = 0.9f;
    float high_th = 5.0f;
    float percent = 0.0f;

    if (throttle_voltage < low_th) {
        percent = 0.0f;
    } else if (throttle_voltage >= high_th) {
        percent = 100.0f;
    } else {
        percent = (throttle_voltage - low_th) * 100.0f / (high_th - low_th);
    }

    float debug_percent = percent;
    uint32_t pwm = (uint32_t)(percent * period / 100.0f);
    uint32_t debug_pwm = pwm;

    Commutation_Update(U, V, W, pwm);
    
    // 测试示例：手动控制换相状态
    // 取消注释下面的代码来测试不同的换相状态
    
    // 测试1：手动设置霍尔信号为001状态
    // test_mode = 1;
    // test_U = 0; test_V = 0; test_W = 1;
    
    // 测试2：手动设置霍尔信号为010状态  
    // test_mode = 1;
    // test_U = 0; test_V = 1; test_W = 0;
    
    // 测试3：测试U相下桥全开
    // Test_Lower_Bridge_On(0, pwm); // 0=U相，pwm=占空比
    
    // 测试4：测试特定换相状态
    // Test_Commutation(1, pwm); // 1=001状态，pwm=占空比
    
    HAL_Delay(1);
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL16;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_TIM1|RCC_PERIPHCLK_ADC12;
  PeriphClkInit.Adc12ClockSelection = RCC_ADC12PLLCLK_DIV1;
  PeriphClkInit.Tim1ClockSelection = RCC_TIM1CLK_HCLK;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief 霍尔信号更新函数
 * @retval None
 */
void Hall_Update(void)
{
    // 读取PB4/PB5/PB8的霍尔信号
    // 高电平为1，低电平为0
    U = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_4) ? 1 : 0;
    V = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_5) ? 1 : 0;
    W = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_8) ? 1 : 0;
}

// 六步换相PWM输出控制函数
void Commutation_Update(uint8_t U_hall, uint8_t V_hall, uint8_t W_hall, uint32_t pwm)
{
    // 先全部关断所有上桥
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0); // W上桥
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 0); // V上桥
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, 0); // U上桥

    if (pwm == 0) return; // 油门为0，全部关断

    // 根据霍尔信号组合确定换相状态
    uint8_t hall_state = (U_hall << 2) | (V_hall << 1) | W_hall;
    
    // 使用查表法设置上桥PWM
    if (upper_table[hall_state][0]) { // U上桥
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, pwm);
    }
    if (upper_table[hall_state][1]) { // V上桥
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, pwm);
    }
    if (upper_table[hall_state][2]) { // W上桥
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, pwm);
    }
    
    // 下桥互补输出和死区由硬件自动完成，无需手动设置
}

// 测试函数：手动设置换相状态
void Test_Commutation(uint8_t hall_state, uint32_t pwm)
{
    // 解析霍尔状态
    uint8_t U_hall = (hall_state >> 2) & 0x01;
    uint8_t V_hall = (hall_state >> 1) & 0x01;
    uint8_t W_hall = hall_state & 0x01;
    
    // 先全部关断所有上桥
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0); // W上桥
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 0); // V上桥
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, 0); // U上桥

    if (pwm == 0) return; // PWM为0，全部关断

    // 使用查表法设置上桥PWM
    if (upper_table[hall_state][0]) { // U上桥
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, pwm);
    }
    if (upper_table[hall_state][1]) { // V上桥
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, pwm);
    }
    if (upper_table[hall_state][2]) { // W上桥
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, pwm);
    }
    
    // 下桥互补输出和死区由硬件自动完成
}

// 测试下桥全开功能
void Test_Lower_Bridge_On(uint8_t phase, uint32_t pwm)
{
    // 先全部关断
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, 0);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, 0);
    
    if (pwm == 0) return;
    
    // 设置指定相的上桥PWM，下桥会自动互补全开
    switch(phase) {
        case 0: // U相
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, pwm);
            break;
        case 1: // V相
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, pwm);
            break;
        case 2: // W相
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, pwm);
            break;
    }
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
