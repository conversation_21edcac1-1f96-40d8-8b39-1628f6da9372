2025-07-09 14:05:32,202 [INFO] Activator:176 - 


2025-07-09 14:05:32,206 [INFO] Activator:177 - !SESSION log4j initialized
2025-07-09 14:05:35,471 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 
2025-07-09 14:05:36,691 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-07-09 14:05:36,706 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-07-09 14:05:36,706 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-07-09 14:05:36,706 [WARN] ApiDb:259 - Overriding images path with different value:  => C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-07-09 14:05:36,711 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-09 14:05:36,711 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-09 14:05:36,713 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-09 14:05:36,788 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-07-09 14:05:36,837 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-07-09 14:05:36,837 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-07-09 14:05:36,837 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-07-09 14:05:36,837 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,837 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-09 14:05:36,837 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,837 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-09 14:05:36,837 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,838 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,838 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-09 14:05:36,890 [INFO] MainPanel:274 - HeapMemory: 268435456
2025-07-09 14:05:36,986 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-07-09 14:05:36,986 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-07-09 14:05:36,986 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-07-09 14:05:36,986 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,986 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-09 14:05:36,986 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,986 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-09 14:05:36,986 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,986 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-09 14:05:36,987 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-09 14:05:37,000 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-07-09 14:05:37,001 [INFO] PluginManage:196 - Search for loadable plugins [exclusion list=, ]
2025-07-09 14:05:37,002 [INFO] PluginManage:310 - Check plugin analytics
2025-07-09 14:05:37,144 [INFO] AnalyticsPlugin:253 - Accepted Software Licenses: STM32CubeMX.6.15.0
2025-07-09 14:05:37,145 [INFO] AnalyticsPlugin:255 - Accepted CMSIS Pack Licenses: 
2025-07-09 14:05:37,145 [INFO] AnalyticsPlugin:257 - Accepted Firmware Licenses: FW.G4.1.5.0,FW.H7.1.12.0
2025-07-09 14:05:37,148 [INFO] PluginManage:359 - Loaded plugin analytics (category:tool,tabindex:-1)
2025-07-09 14:05:37,148 [INFO] PluginManage:310 - Check plugin cadmodel
2025-07-09 14:05:37,155 [INFO] CADModel:105 - Init CAD model plugin
2025-07-09 14:05:37,155 [INFO] PluginManage:359 - Loaded plugin cadmodel (category:power,tabindex:5)
2025-07-09 14:05:37,155 [INFO] PluginManage:310 - Check plugin clock
2025-07-09 14:05:37,164 [INFO] PluginManage:359 - Loaded plugin clock (category:base,tabindex:2)
2025-07-09 14:05:37,164 [INFO] PluginManage:310 - Check plugin ddr
2025-07-09 14:05:37,166 [INFO] PluginManage:359 - Loaded plugin ddr (category:tool,tabindex:6)
2025-07-09 14:05:37,166 [INFO] PluginManage:310 - Check plugin filemanager
2025-07-09 14:05:37,282 [INFO] PluginManage:359 - Loaded plugin filemanager (category:base,tabindex:10)
2025-07-09 14:05:37,282 [INFO] PluginManage:310 - Check plugin ipmanager
2025-07-09 14:05:37,286 [INFO] PluginManage:359 - Loaded plugin ipmanager (category:base,tabindex:5)
2025-07-09 14:05:37,286 [INFO] PluginManage:310 - Check plugin lpbam
2025-07-09 14:05:37,292 [INFO] PluginManage:359 - Loaded plugin lpbam (category:base,tabindex:0)
2025-07-09 14:05:37,292 [INFO] PluginManage:310 - Check plugin memorymap
2025-07-09 14:05:37,303 [INFO] PluginManage:359 - Loaded plugin memorymap (category:base,tabindex:4)
2025-07-09 14:05:37,303 [INFO] PluginManage:310 - Check plugin pinoutandconfiguration
2025-07-09 14:05:37,310 [INFO] PluginManage:359 - Loaded plugin pinoutandconfiguration (category:base,tabindex:1)
2025-07-09 14:05:37,310 [INFO] PluginManage:310 - Check plugin pinoutconfig
2025-07-09 14:05:37,369 [WARN] SupportedApi:132 - Cannot load RTOS API schema: s4s-elt-must-match.1: The content of 'definitions' must match (annotation?, (simpleType | complexType)?, (unique | key | keyref)*)). A problem was found starting at: attribute.
2025-07-09 14:05:37,464 [INFO] PluginManage:359 - Loaded plugin pinoutconfig (category:base,tabindex:0)
2025-07-09 14:05:37,464 [INFO] PluginManage:310 - Check plugin power
2025-07-09 14:05:37,472 [INFO] PluginManage:359 - Loaded plugin power (category:power,tabindex:4)
2025-07-09 14:05:37,472 [INFO] PluginManage:310 - Check plugin projectmanager
2025-07-09 14:05:37,483 [INFO] PluginManage:359 - Loaded plugin projectmanager (category:projectmanager,tabindex:4)
2025-07-09 14:05:37,483 [INFO] PluginManage:310 - Check plugin rif
2025-07-09 14:05:37,491 [INFO] PluginManage:359 - Loaded plugin rif (category:base,tabindex:3)
2025-07-09 14:05:37,491 [INFO] PluginManage:310 - Check plugin thirdparty
2025-07-09 14:05:37,589 [INFO] PluginManage:359 - Loaded plugin thirdparty (category:base,tabindex:-1)
2025-07-09 14:05:37,589 [WARN] IntegrityCheckThread:84 - waiting for thirdparty lock release [integrity check]
2025-07-09 14:05:37,589 [INFO] PluginManage:310 - Check plugin tools
2025-07-09 14:05:37,589 [INFO] IntegrityCheckThread:86 - entering critical section [integrity check]
2025-07-09 14:05:37,590 [INFO] ThirdPartyUpdaterWithRetryManager:70 - Updater plugin not ready yet. [1/15]
2025-07-09 14:05:37,591 [INFO] PluginManage:359 - Loaded plugin tools (category:base,tabindex:7)
2025-07-09 14:05:37,591 [INFO] PluginManage:310 - Check plugin tutovideos
2025-07-09 14:05:37,754 [INFO] PluginManage:359 - Loaded plugin tutovideos (category:base,tabindex:-1)
2025-07-09 14:05:37,755 [INFO] PluginManage:310 - Check plugin updater
2025-07-09 14:05:37,773 [INFO] PluginManage:359 - Loaded plugin updater (category:base,tabindex:12)
2025-07-09 14:05:37,773 [INFO] PluginManage:310 - Check plugin userauth
2025-07-09 14:05:37,777 [INFO] UserAuth:118 - Init User Auth plugin
2025-07-09 14:05:37,777 [INFO] PluginManage:359 - Loaded plugin userauth (category:base,tabindex:14)
2025-07-09 14:05:37,777 [INFO] PluginManage:283 - PluginManage : Loaded plugins [18]
2025-07-09 14:05:37,927 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-09 14:05:37,976 [INFO] CADModel:165 - CPN selected for project level
2025-07-09 14:05:37,976 [INFO] CADModel:114 - Register for checkConnection events
2025-07-09 14:05:37,990 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:37,990 [INFO] PluginManager:220 - loadIPPluginJar : add adc
2025-07-09 14:05:37,992 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:37,992 [INFO] PluginManager:220 - loadIPPluginJar : add aes
2025-07-09 14:05:37,994 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:37,994 [INFO] PluginManager:220 - loadIPPluginJar : add can
2025-07-09 14:05:37,995 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:37,995 [INFO] PluginManager:220 - loadIPPluginJar : add comp
2025-07-09 14:05:37,997 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:37,997 [INFO] PluginManager:220 - loadIPPluginJar : add cryp
2025-07-09 14:05:38,000 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,000 [INFO] PluginManager:220 - loadIPPluginJar : add ddr_ctrl_phy
2025-07-09 14:05:38,001 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,002 [INFO] PluginManager:220 - loadIPPluginJar : add dfsdm
2025-07-09 14:05:38,005 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,006 [INFO] PluginManager:220 - loadIPPluginJar : add dma
2025-07-09 14:05:38,008 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,008 [INFO] PluginManager:220 - loadIPPluginJar : add dma3
2025-07-09 14:05:38,010 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,010 [INFO] PluginManager:220 - loadIPPluginJar : add extmemmanager
2025-07-09 14:05:38,011 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,011 [INFO] PluginManager:220 - loadIPPluginJar : add fatfs
2025-07-09 14:05:38,014 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,014 [INFO] PluginManager:220 - loadIPPluginJar : add fmc
2025-07-09 14:05:38,020 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,020 [INFO] PluginManager:220 - loadIPPluginJar : add freertos
2025-07-09 14:05:38,021 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,021 [INFO] PluginManager:220 - loadIPPluginJar : add genericplugin
2025-07-09 14:05:38,022 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,022 [INFO] PluginManager:220 - loadIPPluginJar : add gfxmmu
2025-07-09 14:05:38,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,026 [INFO] PluginManager:220 - loadIPPluginJar : add gic
2025-07-09 14:05:38,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,029 [INFO] PluginManager:220 - loadIPPluginJar : add gpio
2025-07-09 14:05:38,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,030 [INFO] PluginManager:220 - loadIPPluginJar : add gtzc
2025-07-09 14:05:38,032 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,032 [INFO] PluginManager:220 - loadIPPluginJar : add hash
2025-07-09 14:05:38,034 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,034 [INFO] PluginManager:220 - loadIPPluginJar : add i2c
2025-07-09 14:05:38,035 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,035 [INFO] PluginManager:220 - loadIPPluginJar : add i2s
2025-07-09 14:05:38,037 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,037 [INFO] PluginManager:220 - loadIPPluginJar : add i3c
2025-07-09 14:05:38,040 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,040 [INFO] PluginManager:220 - loadIPPluginJar : add ipddr
2025-07-09 14:05:38,045 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,045 [INFO] PluginManager:220 - loadIPPluginJar : add linkedlist
2025-07-09 14:05:38,048 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,048 [INFO] PluginManager:220 - loadIPPluginJar : add lorawan
2025-07-09 14:05:38,049 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,049 [INFO] PluginManager:220 - loadIPPluginJar : add ltdc
2025-07-09 14:05:38,054 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,054 [INFO] PluginManager:220 - loadIPPluginJar : add mdma
2025-07-09 14:05:38,057 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,057 [INFO] PluginManager:220 - loadIPPluginJar : add nvic
2025-07-09 14:05:38,059 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,059 [INFO] PluginManager:220 - loadIPPluginJar : add opamp
2025-07-09 14:05:38,061 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,062 [INFO] PluginManager:220 - loadIPPluginJar : add openamp
2025-07-09 14:05:38,064 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,064 [INFO] PluginManager:220 - loadIPPluginJar : add pdm2pcm
2025-07-09 14:05:38,068 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,069 [INFO] PluginManager:220 - loadIPPluginJar : add plateformsettings
2025-07-09 14:05:38,070 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,070 [INFO] PluginManager:220 - loadIPPluginJar : add quadspi
2025-07-09 14:05:38,072 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,072 [INFO] PluginManager:220 - loadIPPluginJar : add radio
2025-07-09 14:05:38,075 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,075 [INFO] PluginManager:220 - loadIPPluginJar : add resmgrutility
2025-07-09 14:05:38,077 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,077 [INFO] PluginManager:220 - loadIPPluginJar : add sai
2025-07-09 14:05:38,079 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,079 [INFO] PluginManager:220 - loadIPPluginJar : add spi
2025-07-09 14:05:38,084 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,084 [INFO] PluginManager:220 - loadIPPluginJar : add stm32_wpan
2025-07-09 14:05:38,085 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,085 [INFO] PluginManager:220 - loadIPPluginJar : add tim
2025-07-09 14:05:38,088 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,088 [INFO] PluginManager:220 - loadIPPluginJar : add touchsensing
2025-07-09 14:05:38,089 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,089 [INFO] PluginManager:220 - loadIPPluginJar : add tracer_emb
2025-07-09 14:05:38,091 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,091 [INFO] PluginManager:220 - loadIPPluginJar : add ts
2025-07-09 14:05:38,092 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,092 [INFO] PluginManager:220 - loadIPPluginJar : add tsc
2025-07-09 14:05:38,094 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,094 [INFO] PluginManager:220 - loadIPPluginJar : add ucpd
2025-07-09 14:05:38,096 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,096 [INFO] PluginManager:220 - loadIPPluginJar : add usart
2025-07-09 14:05:38,099 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:38,099 [INFO] PluginManager:220 - loadIPPluginJar : add usbx
2025-07-09 14:05:38,205 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,223 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-07-09 14:05:38,230 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-07-09 14:05:38,239 [INFO] CADModel:165 - CPN selected for project level
2025-07-09 14:05:38,239 [INFO] CADModel:114 - Register for checkConnection events
2025-07-09 14:05:38,239 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,239 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-09 14:05:38,343 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,344 [INFO] CADModel:165 - CPN selected for project level
2025-07-09 14:05:38,345 [INFO] CADModel:114 - Register for checkConnection events
2025-07-09 14:05:38,345 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,345 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-09 14:05:38,347 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,408 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,411 [INFO] DbMcusAds:53 - JSON generation date=Tue Jul 08 16:14:23 CST 2025 (1751962463506)
2025-07-09 14:05:38,411 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,437 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-09 14:05:38,486 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,487 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,488 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,488 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-09 14:05:38,488 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-09 14:05:38,515 [ERROR] Updater:1198 - MainUpdater not yet initialized. External WinMGr cannot be set.
2025-07-09 14:05:38,517 [INFO] Updater:1134 - Updater Version found : 6.15.0
2025-07-09 14:05:38,530 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-07-09 14:05:39,002 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-09 14:05:39,003 [INFO] MainUpdater:289 - Updater Check For Update Now.
2025-07-09 14:05:39,003 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-09 14:05:39,010 [INFO] McuFinderGlobals:63 - Set McuFinder mode to 2 (CubeIDE integrated)
2025-07-09 14:05:39,011 [INFO] UserAuth:487 - Internet connection configuration mode: 1
2025-07-09 14:05:39,033 [INFO] JxBrowserEngine:152 - Initiate JxBrowser Engine with user profile folder
2025-07-09 14:05:39,146 [INFO] CheckServerUpdateThread:120 - End of CheckServer Thread
2025-07-09 14:05:39,597 [INFO] WebApp:169 - Instantiating new browser for Auth
2025-07-09 14:05:40,044 [INFO] WebApp:463 - Apply proxy settings
2025-07-09 14:05:40,044 [INFO] WebApp:548 - Chromium requires no authentication
2025-07-09 14:05:40,051 [INFO] WebApp:491 - Direct internet connection detected
2025-07-09 14:05:40,069 [INFO] WebApp:900 - Register for checkConnection events
2025-07-09 14:05:40,069 [INFO] WebApp:463 - Apply proxy settings
2025-07-09 14:05:40,069 [INFO] WebApp:548 - Chromium requires no authentication
2025-07-09 14:05:40,069 [INFO] WebApp:491 - Direct internet connection detected
2025-07-09 14:05:40,195 [INFO] WebApp:225 - Starting web application
2025-07-09 14:05:40,195 [INFO] WebApp:593 - Web application path used C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\db\plugins\mcufinder\reactClient1\index.html
2025-07-09 14:05:40,249 [INFO] UserAuth:487 - Internet connection configuration mode: 1
2025-07-09 14:05:40,693 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENVWB1.1.4.0
2025-07-09 14:05:40,698 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-ASTRA1.2.0.2
2025-07-09 14:05:40,702 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SMBUS.2.1.0
2025-07-09 14:05:40,705 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ST60.1.0.0
2025-07-09 14:05:40,719 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F7.1.1.0
2025-07-09 14:05:40,732 [WARN] PackLoader:240 - Cannot read IP mode file for Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.7.0
2025-07-09 14:05:40,745 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7.3.4.0
2025-07-09 14:05:40,752 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DISPLAY.3.0.0
2025-07-09 14:05:40,761 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC10.1.0.0
2025-07-09 14:05:40,767 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLEMGR.4.0.0
2025-07-09 14:05:40,770 [WARN] PackLoader:240 - Cannot read IP mode file for emotas.I-CUBE-CANOPEN.1.3.0
2025-07-09 14:05:40,773 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : Cortex-A Device cause : null
2025-07-09 14:05:40,781 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-09 14:05:40,781 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-09 14:05:40,781 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-09 14:05:40,783 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-SMARTAG2.1.2.0
2025-07-09 14:05:40,786 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-FLIGHT1.5.1.0
2025-07-09 14:05:40,791 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-09 14:05:40,792 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-09 14:05:40,792 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-09 14:05:40,794 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-09 14:05:40,794 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-09 14:05:40,795 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WL.2.0.0
2025-07-09 14:05:40,798 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENV1.5.0.0
2025-07-09 14:05:40,802 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE2.3.3.0
2025-07-09 14:05:40,807 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC9.1.0.0
2025-07-09 14:05:40,811 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSL.5.8.0
2025-07-09 14:05:40,813 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE1.7.1.0
2025-07-09 14:05:40,817 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfMQTT.1.19.2
2025-07-09 14:05:40,819 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AI.10.1.0
2025-07-09 14:05:40,833 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G0.1.1.0
2025-07-09 14:05:40,840 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SAFEA1.1.2.2
2025-07-09 14:05:40,844 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC4.3.0.0
2025-07-09 14:05:40,863 [WARN] PackLoader:240 - Cannot read IP mode file for EmbeddedOffice.I-CUBE-FS-RTOS.1.0.1
2025-07-09 14:05:40,869 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-WB05N.2.0.0
2025-07-09 14:05:40,873 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfTPM.3.8.0
2025-07-09 14:05:40,878 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TCPP.4.2.0
2025-07-09 14:05:40,884 [WARN] PackLoader:240 - Cannot read IP mode file for RealThread.X-CUBE-RT-Thread_Nano.4.1.1
2025-07-09 14:05:40,887 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-SIGFOX1.3.2.0
2025-07-09 14:05:40,890 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-EEPRMA1.5.1.0
2025-07-09 14:05:40,895 [WARN] PackLoader:240 - Cannot read IP mode file for ITTIA_DB.I-CUBE-ITTIADB.8.9.0
2025-07-09 14:05:40,899 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ST67W61.1.0.0
2025-07-09 14:05:40,911 [WARN] PackLoader:240 - Cannot read IP mode file for SEGGER.I-CUBE-embOS.1.3.1
2025-07-09 14:05:40,929 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0
2025-07-09 14:05:40,946 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-MEMS1.11.3.0
2025-07-09 14:05:40,999 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-PM33A1.1.0.0
2025-07-09 14:05:41,006 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F4.1.1.0
2025-07-09 14:05:41,009 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ISPU.2.1.0
2025-07-09 14:05:41,016 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L5.2.0.0
2025-07-09 14:05:41,020 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC6.3.1.0
2025-07-09 14:05:41,024 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-FREERTOS.1.3.1
2025-07-09 14:05:41,027 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-STAIOTCFT.1.0.0
2025-07-09 14:05:41,030 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DPower.1.3.0
2025-07-09 14:05:41,037 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-09 14:05:41,037 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L4.2.0.0
2025-07-09 14:05:41,038 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-09 14:05:41,038 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-09 14:05:41,038 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-09 14:05:41,041 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SFXS2LP1.4.0.0
2025-07-09 14:05:41,052 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7.3.3.0
2025-07-09 14:05:41,059 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-09 14:05:41,059 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-09 14:05:41,060 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WB.2.0.0
2025-07-09 14:05:41,060 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-09 14:05:41,060 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-09 14:05:41,060 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-09 14:05:41,061 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-09 14:05:41,061 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-09 14:05:41,064 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-GNSS1.7.0.1
2025-07-09 14:05:41,067 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0
2025-07-09 14:05:41,070 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-STBOX1.2.0.0
2025-07-09 14:05:41,076 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SUBG2.5.0.0
2025-07-09 14:05:41,085 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7RS.1.1.0
2025-07-09 14:05:41,088 [WARN] PackLoader:240 - Cannot read IP mode file for Cesanta.I-CUBE-Mongoose.7.13.0
2025-07-09 14:05:41,094 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G4.2.0.0
2025-07-09 14:05:41,098 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSH.1.4.20
2025-07-09 14:05:41,100 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC7.2.0.0
2025-07-09 14:05:41,107 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-IPS.3.1.0
2025-07-09 14:05:41,111 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALS.1.0.2
2025-07-09 14:05:41,115 [WARN] PackLoader:240 - Cannot read IP mode file for portGmbH.I-Cube-SoM-uGOAL.1.1.0
2025-07-09 14:05:41,119 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOF1.3.4.3
2025-07-09 14:05:41,124 [INFO] ThirdParty:978 - Integrity check success = true
2025-07-09 14:05:41,124 [INFO] IntegrityCheckThread:100 - exiting critical section [integrity check]
2025-07-09 14:05:41,124 [INFO] IntegrityCheckThread:103 - End integrity checks thread
2025-07-09 14:05:42,470 [INFO] McuFinderGlobals:63 - Set McuFinder mode to 2 (CubeIDE integrated)
2025-07-09 14:05:42,473 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-09 14:05:42,474 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-09 14:05:42,498 [INFO] MicroXplorer:468 - Change Database Path : 
2025-07-09 14:05:42,499 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-09 14:05:42,502 [WARN] ThirdParty:871 - waiting for thirdparty lock release [close project]
2025-07-09 14:05:42,502 [INFO] ThirdParty:873 - entering critical section [close project]
2025-07-09 14:05:42,503 [INFO] ThirdParty:883 - exiting critical section [close project]
2025-07-09 14:05:42,505 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-09 14:05:42,506 [INFO] UtilMem:75 - Begin LoadConfig() Used Memory: 218883792 Bytes (274726912)
2025-07-09 14:05:42,507 [INFO] MicroXplorer:468 - Change Database Path : 
2025-07-09 14:05:42,507 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-09 14:05:42,507 [INFO] OpenFileManager:355 - Change cursor
2025-07-09 14:05:42,520 [INFO] Mcu:2029 - Initializing MCU STM32F303C(B-C)Tx STM32F303CCTx STM32F303CCT6
2025-07-09 14:05:43,362 [INFO] Context:786 - Trying to add GPIOservice into a context  which must be forbidden
2025-07-09 14:05:43,599 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) Pin13 (VP_RIF_VS_RIF1) cannot be retrieved for this MCU
2025-07-09 14:05:43,651 [INFO] RtosManager:558 - Registered RTOS mode: class=CMSIS, group=RTOS, mode=CMSIS_V1, owner=FREERTOS
2025-07-09 14:05:43,651 [INFO] RtosManager:558 - Registered RTOS mode: class=CMSIS, group=RTOS2, mode=CMSIS_V2, owner=FREERTOS
2025-07-09 14:05:43,651 [INFO] RtosManager:558 - Registered RTOS mode: class=RTOS, group=Core, mode=CMSIS_V1, owner=FREERTOS
2025-07-09 14:05:43,651 [INFO] RtosManager:558 - Registered RTOS mode: class=RTOS, group=Core, mode=CMSIS_V2, owner=FREERTOS
2025-07-09 14:05:43,651 [WARN] ModelIntegratedComponent:184 - Missing modes for component STMicroelectronics:FreeRTOS:0.0.1:STMicroelectronics:RTOS:FreeRTOS:Core:::10.2.0:
2025-07-09 14:05:43,664 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,664 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,664 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,664 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,664 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,664 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,664 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:05:43,665 [WARN] ModelPack:524 - Component already loaded: STMicroelectronics:HAL Drivers:0.0.0:STMicroelectronics:Device:STMicro_Driver:XSPI:HAL::0.0.1:HAL_XSPI
2025-07-09 14:05:43,718 [INFO] ThirdPartyModel:298 - Start build external matchings
2025-07-09 14:05:43,865 [INFO] ThirdPartyModel:316 - End build external matchings
2025-07-09 14:05:43,885 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (Instance) has Id (ADC1) currently not set
2025-07-09 14:05:43,886 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (Direction) has Id (ADC1) currently not set
2025-07-09 14:05:43,886 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (PeriphInc) has Id (ADC1) currently not set
2025-07-09 14:05:43,886 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (MemInc) has Id (ADC1) currently not set
2025-07-09 14:05:43,886 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (PeriphDataAlignment) has Id (ADC1) currently not set
2025-07-09 14:05:43,886 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (MemDataAlignment) has Id (ADC1) currently not set
2025-07-09 14:05:43,887 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (Mode) has Id (ADC1) currently not set
2025-07-09 14:05:43,887 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) IP (DMA) : Parameter (Priority) has Id (ADC1) currently not set
2025-07-09 14:05:44,104 [INFO] ApiDb:581 - Connected to CubeFinder SQLite database (C:\Users\<USER>\.stmcufinder\plugins\mcufinder\mcu\cube-finder-db.db)
2025-07-09 14:05:44,197 [INFO] ApiDb:668 - CubeFinder database Data Model version=2.1
2025-07-09 14:05:44,197 [INFO] ApiDb:669 - CubeFinder database Configuration version=3.0.39
2025-07-09 14:05:44,197 [INFO] ApiDb:670 - CubeFinder database generation date=2025-06-24 (1750757100)
2025-07-09 14:05:44,197 [INFO] ApiDb:671 - CubeFinder database FW Pack versions=[FP-ATR-ASTRA1_V2.0.0, FP-SNS-FLIGHT1_V5.1.0, FP-SNS-MOTENV1_V5.0.0, FP-SNS-MOTENVWB1_V1.4.0, FP-SNS-SMARTAG2_V1.2.0, FP-SNS-STBOX1_V2.0.0, STM32Cube_FW_C0_V1.4.0, STM32Cube_FW_F4_V1.28.2, STM32Cube_FW_F7_V1.17.3, STM32Cube_FW_G0_V1.6.2, STM32Cube_FW_G4_V1.6.1, STM32Cube_FW_H5_V1.5.0, STM32Cube_FW_H7RS_V1.2.0, STM32Cube_FW_H7_V1.12.1, STM32Cube_FW_L0_V1.12.2, STM32Cube_FW_L4_V1.18.1, STM32Cube_FW_L5_V1.5.1, STM32Cube_FW_N6_V1.2.0, STM32Cube_FW_U0_V1.3.0, STM32Cube_FW_U3_V1.2.0, STM32Cube_FW_U5_V1.8.0, STM32Cube_FW_WB0_V1.3.0, STM32Cube_FW_WBA_V1.7.0, STM32Cube_FW_WB_V1.23.0, STM32Cube_FW_WL3_V1.2.0, STM32Cube_FW_WL_V1.3.1, X-CUBE-ALGOBUILD_V1.4.0, X-CUBE-ALS_V1.0.2, X-CUBE-AZRTOS-F4_V1.1.0, X-CUBE-AZRTOS-F7_V1.1.0, X-CUBE-AZRTOS-G0_V1.1.0, X-CUBE-AZRTOS-G4_V2.0.0, X-CUBE-AZRTOS-H7RS_V1.1.0, X-CUBE-AZRTOS-H7_V3.3.0, X-CUBE-AZRTOS-L4_V2.0.0, X-CUBE-AZRTOS-L5_V2.0.0, X-CUBE-AZRTOS-WB_V2.0.0, X-CUBE-AZRTOS-WL_V2.0.0, X-CUBE-BLE1_V7.1.0, X-CUBE-BLE2_V3.3.0, X-CUBE-BLEMGR_V4.0.0, X-CUBE-EEPRMA1_V5.1.0, X-CUBE-FREERTOS_V1.3.1, X-CUBE-GNSS1_V6.0.0, X-CUBE-MEMS1_V11.3.0, X-CUBE-NFC4_V3.0.0, X-CUBE-NFC7_V2.0.0, X-CUBE-SFXS2LP1_V4.0.0, X-CUBE-SUBG2_V5.0.0, X-CUBE-TOF1_V3.4.3]
2025-07-09 14:05:44,304 [INFO] DbBoardsSqlite:226 - include board P-NUCLEO-WB55-NUCLEO as a kit item of type 'Nucleo-64'
2025-07-09 14:05:44,304 [INFO] DbBoardsSqlite:226 - include board P-NUCLEO-WB55-USBDONGLE as a kit item of type 'Nucleo USB Dongle'
2025-07-09 14:05:44,305 [INFO] DbBoardsSqlite:226 - include board STEVAL-IDP005V1 as a kit item of type 'Evaluation Board'
2025-07-09 14:05:44,305 [INFO] DbBoardsSqlite:226 - include board STEVAL-IDP005V2 as a kit item of type 'Evaluation Board'
2025-07-09 14:05:44,600 [INFO] ApiDb:240 - Found 880 in-development CPN: [B-G473E-ZEST1S, B-WB1M-WPAN1, B-WBA5M-WPAN, B-WL5M-SUBG1, NUCLEO-C031C6, NUCLEO-C051C8, NUCLEO-C071RB, NUCLEO-C092RC, NUCLEO-H503RB, NUCLEO-H533RE, NUCLEO-H563ZI, NUCLEO-H7S3L8, NUCLEO-N657X0-Q, NUCLEO-U031R8, NUCLEO-U083RC, NUCLEO-U385RG-Q, NUCLEO-U545RE-Q, NUCLEO-U5A5ZJ-Q, NUCLEO-WB05KZ, NUCLEO-WB07CC, NUCLEO-WB09KE, NUCLEO-WBA52CG, NUCLEO-WBA55CG, NUCLEO-WL33CC1, NUCLEO-WL33CC2, STEVAL-PROTEUS1, STEVAL-SMARTAG2, STEVAL-STWINBX1, STM320518-EVAL, STM32C0116-DK, STM32C011D6Y3TR, STM32C011D6Y6TR, STM32C011F4P3, STM32C011F4P6, STM32C011F4U3, STM32C011F4U6TR, STM32C011F6P3, STM32C011F6P6, STM32C011F6U3, STM32C011F6U6TR, STM32C011J4M3, STM32C011J4M6, STM32C011J6M3, STM32C011J6M6, STM32C0316-DK, STM32C031C4T3, STM32C031C4T6, STM32C031C4U3, STM32C031C4U6, STM32C031C6T3, STM32C031C6T6, STM32C031C6U3, STM32C031C6U6, STM32C031F4P3, STM32C031F4P6, STM32C031F6P3, STM32C031F6P6, STM32C031G4U3, STM32C031G4U6, STM32C031G6U3, STM32C031G6U6, STM32C031K4T3, STM32C031K4T6, STM32C031K4U3, STM32C031K4U6, STM32C031K6T3, STM32C031K6T6, STM32C031K6U3, STM32C031K6U6, STM32C051C6T6, STM32C051C6U6, STM32C051C8T6, STM32C051C8U6, STM32C051D8Y6TR, STM32C051F6P6, STM32C051F8P6, STM32C051G6U6, STM32C051G8U6, STM32C051K6T6, STM32C051K6U6, STM32C051K8T6, STM32C071C8T6, STM32C071C8T6N, STM32C071C8U6, STM32C071C8U6N, STM32C071CBT6, STM32C071CBT6N, STM32C071CBU6, STM32C071CBU6N, STM32C071F8P6, STM32C071F8P6N, STM32C071FBP6, STM32C071FBP6N, STM32C071FBY6TR, STM32C071G8U6, STM32C071G8U6N, STM32C071GBU6, STM32C071GBU6N, STM32C071K8T6, STM32C071K8T6N, STM32C071K8U6, STM32C071K8U6N, STM32C071KBT6, STM32C071KBT6N, STM32C071KBU6, STM32C071KBU6N, STM32C071R8T6, STM32C071R8T6N, STM32C071RBI6N, STM32C071RBT6, STM32C071RBT6N, STM32C091CBT6, STM32C091CBU6, STM32C091CCT6, STM32C091CCU6, STM32C091ECY6TR, STM32C091FBP6, STM32C091FCP6, STM32C091GBU6, STM32C091GCU6, STM32C091KBT6, STM32C091KBU6, STM32C091KCT6, STM32C091KCU6, STM32C091RBT6, STM32C091RCI6, STM32C091RCT6, STM32C092CBT6, STM32C092CBU6, STM32C092CCT6, STM32C092CCU6, STM32C092ECY6TR, STM32C092FBP6, STM32C092FCP6, STM32C092GBU6, STM32C092GCU6, STM32C092KBT6, STM32C092KBU6, STM32C092KCT6, STM32C092KCU6, STM32C092RBT6, STM32C092RCI6, STM32C092RCT6, STM32G071K8TXN, STM32G071K8UXN, STM32G081GBU6N, STM32G081KBT6N, STM32G081KBUXN, STM32G0B1CCT6N, STM32G0B1KCT6, STM32G0B1NEY6TR, STM32G0B1RCT6N, STM32G0C1CCT6, STM32G0C1CCT6N, STM32G0C1CCU6N, STM32G0C1CET6N, STM32G0C1CEU6N, STM32G0C1KCT6, STM32G0C1NEY6TR, STM32G0C1RCI6N, STM32G0C1RCT6N, STM32G0C1REI6N, STM32G0C1RET6N, STM32G0C1VCI6, STM32G0C1VEI6, STM32G411C6T3, STM32G411C6T6, STM32G411C6U3, STM32G411C6U6, STM32G411C8T3, STM32G411C8T6, STM32G411C8U3, STM32G411C8U6, STM32G411CBT3, STM32G411CBT6, STM32G411CBU3, STM32G411CBU6, STM32G411K6T3, STM32G411K6T6, STM32G411K6U3, STM32G411K6U6, STM32G411K8T3, STM32G411K8T6, STM32G411K8U3, STM32G411K8U6, STM32G411KBT3, STM32G411KBT6, STM32G411KBU3, STM32G411KBU6, STM32G411M6T3, STM32G411M6T6, STM32G411M8T3, STM32G411M8T6, STM32G411MBT3, STM32G411MBT6, STM32G411R6T3, STM32G411R6T6, STM32G411R8T3, STM32G411R8T6, STM32G411RBT3, STM32G411RBT6, STM32G414CBT3, STM32G414CBT6, STM32G414CBU3, STM32G414CBU6, STM32G414CCT3, STM32G414CCT6, STM32G414CCU3, STM32G414CCU6, STM32G414MBT3, STM32G414MBT6, STM32G414MCT3, STM32G414MCT6, STM32G414RBT3, STM32G414RBT6, STM32G414RCT3, STM32G414RCT6, STM32G414VBT3, STM32G414VBT6, STM32G414VCT3, STM32G414VCT6, STM32G431CBT3Z, STM32G431RBT3Z, STM32G471CCT6, STM32G471CCU6, STM32G471CET3, STM32G471CET6, STM32G471CEU3, STM32G471CEU6, STM32G471MCT6, STM32G471MET3, STM32G471MET6, STM32G471MEY6TR, STM32G471QCT6, STM32G471QET3, STM32G471RCT6, STM32G471RET3, STM32G471RET6, STM32G471VCH6, STM32G471VCI6, STM32G471VCT6, STM32G471VEH3, STM32G471VEH6, STM32G471VEI3, STM32G471VEI6, STM32G471VET3, STM32G471VET6, STM32G473QET3Z, STM32G473RET3Z, STM32G474CCT6, STM32G491RET3Z, STM32H503CBT6, STM32H503CBU6, STM32H503EBY6TR, STM32H503KBU6, STM32H503RBT6, STM32H523CCT6, STM32H523CCU6, STM32H523CET6, STM32H523CEU6, STM32H523HEY6TR, STM32H523RCT6, STM32H523RET6, STM32H523VCI6, STM32H523VCT6, STM32H523VEI6, STM32H523VET6, STM32H523ZCJ6, STM32H523ZCT6, STM32H523ZEJ6, STM32H523ZET6, STM32H533CET6, STM32H533CEU6, STM32H533HEY6TR, STM32H533RET6, STM32H533VEI6, STM32H533VET6, STM32H533ZEJ6, STM32H533ZET6, STM32H562AGI6, STM32H562AII6, STM32H562IGK6, STM32H562IGT6, STM32H562IIK6, STM32H562IIT6, STM32H562RGT6, STM32H562RGV6, STM32H562RIT6, STM32H562RIV6, STM32H562VGT6, STM32H562VIT6, STM32H562ZGT6, STM32H562ZIT6, STM32H563AGI6, STM32H563AII3Q, STM32H563AII6, STM32H563IGK6, STM32H563IGT6, STM32H563IIK3Q, STM32H563IIK6, STM32H563IIT3Q, STM32H563IIT6, STM32H563MIY3QTR, STM32H563RGT6, STM32H563RGV6, STM32H563RIT6, STM32H563RIV6, STM32H563VGT6, STM32H563VIT3Q, STM32H563VIT6, STM32H563ZGT6, STM32H563ZIT3Q, STM32H563ZIT6, STM32H573AII3Q, STM32H573AII6, STM32H573I-DK, STM32H573IIK3Q, STM32H573IIK6, STM32H573IIT3Q, STM32H573IIT6, STM32H573MIY3QTR, STM32H573RIT6, STM32H573RIV6, STM32H573VIT3Q, STM32H573VIT6, STM32H573ZIT3Q, STM32H573ZIT6, STM32H7R3A8I6, STM32H7R3I8K6, STM32H7R3I8T6, STM32H7R3L8H6, STM32H7R3L8H6H, STM32H7R3R8V6, STM32H7R3V8H6, STM32H7R3V8T6, STM32H7R3V8Y6TR, STM32H7R3Z8J6, STM32H7R3Z8T6, STM32H7R7A8I6, STM32H7R7I8K6, STM32H7R7I8T6, STM32H7R7L8H6, STM32H7R7L8H6H, STM32H7R7Z8J6, STM32H7S3A8I6, STM32H7S3I8K6, STM32H7S3I8T6, STM32H7S3L8H6, STM32H7S3L8H6H, STM32H7S3R8V6, STM32H7S3V8H6, STM32H7S3V8T6, STM32H7S3V8Y6TR, STM32H7S3Z8J6, STM32H7S3Z8T6, STM32H7S78-DK, STM32H7S7A8I6, STM32H7S7I8K6, STM32H7S7I8T6, STM32H7S7L8H6, STM32H7S7L8H6H, STM32H7S7Z8J6, STM32L4R5QGI6STR, STM32MP131AAE3, STM32MP131AAF3, STM32MP131AAG3, STM32MP131CAE3, STM32MP131CAF3, STM32MP131CAG3, STM32MP131DAE7, STM32MP131DAF7, STM32MP131DAG7, STM32MP131FAE7, STM32MP131FAF7, STM32MP131FAG7, STM32MP133AAE3, STM32MP133AAF3, STM32MP133AAG3, STM32MP133CAE3, STM32MP133CAF3, STM32MP133CAG3, STM32MP133DAE7, STM32MP133DAF7, STM32MP133DAG7, STM32MP133FAE7, STM32MP133FAF7, STM32MP133FAG7, STM32MP135AAE3, STM32MP135AAF3, STM32MP135AAG3, STM32MP135CAE3, STM32MP135CAF3, STM32MP135CAG3, STM32MP135DAE7, STM32MP135DAF7, STM32MP135DAG7, STM32MP135F-DK, STM32MP135FAE7, STM32MP135FAF7, STM32MP135FAF7T, STM32MP135FAF7U, STM32MP135FAG7, STM32MP211AAL3, STM32MP211AAM3, STM32MP211AAN3, STM32MP211AAO3, STM32MP211CAL3, STM32MP211CAM3, STM32MP211CAN3, STM32MP211CAO3, STM32MP211DAL3, STM32MP211DAM3, STM32MP211DAN3, STM32MP211DAO3, STM32MP211FAL3, STM32MP211FAM3, STM32MP211FAN3, STM32MP211FAO3, STM32MP213AAL3, STM32MP213AAM3, STM32MP213AAN3, STM32MP213AAO3, STM32MP213CAL3, STM32MP213CAM3, STM32MP213CAN3, STM32MP213CAO3, STM32MP213DAL3, STM32MP213DAM3, STM32MP213DAN3, STM32MP213DAO3, STM32MP213FAL3, STM32MP213FAM3, STM32MP213FAN3, STM32MP213FAO3, STM32MP215AAL3, STM32MP215AAM3, STM32MP215AAN3, STM32MP215AAO3, STM32MP215CAL3, STM32MP215CAM3, STM32MP215CAN3, STM32MP215CAO3, STM32MP215DAL3, STM32MP215DAM3, STM32MP215DAN3, STM32MP215DAO3, STM32MP215F-DK, STM32MP215FAL3, STM32MP215FAM3, STM32MP215FAN3, STM32MP215FAO3, STM32MP231AAJ3, STM32MP231AAK3, STM32MP231AAL3, STM32MP231CAJ3, STM32MP231CAK3, STM32MP231CAL3, STM32MP231DAJ3, STM32MP231DAK3, STM32MP231DAL3, STM32MP231FAJ3, STM32MP231FAK3, STM32MP231FAL3, STM32MP233AAJ3, STM32MP233AAK3, STM32MP233AAL3, STM32MP233CAJ3, STM32MP233CAK3, STM32MP233CAL3, STM32MP233DAJ3, STM32MP233DAK3, STM32MP233DAL3, STM32MP233FAJ3, STM32MP233FAK3, STM32MP233FAL3, STM32MP235AAJ3, STM32MP235AAK3, STM32MP235AAL3, STM32MP235CAJ3, STM32MP235CAK3, STM32MP235CAL3, STM32MP235DAJ3, STM32MP235DAK3, STM32MP235DAL3, STM32MP235FAJ3, STM32MP235FAK3, STM32MP235FAL3, STM32MP251AAI3, STM32MP251AAK3, STM32MP251AAL3, STM32MP251CAI3, STM32MP251CAK3, STM32MP251CAL3, STM32MP251DAI3, STM32MP251DAK3, STM32MP251DAL3, STM32MP251FAI3, STM32MP251FAK3, STM32MP251FAL3, STM32MP253AAI3, STM32MP253AAK3, STM32MP253AAL3, STM32MP253CAI3, STM32MP253CAK3, STM32MP253CAL3, STM32MP253DAI3, STM32MP253DAK3, STM32MP253DAL3, STM32MP253FAI3, STM32MP253FAK3, STM32MP253FAL3, STM32MP255AAI3, STM32MP255AAK3, STM32MP255AAL3, STM32MP255CAI3, STM32MP255CAK3, STM32MP255CAL3, STM32MP255DAI3, STM32MP255DAK3, STM32MP255DAL3, STM32MP255FAI3, STM32MP255FAK3, STM32MP255FAL3, STM32MP257AAI3, STM32MP257AAK3, STM32MP257AAL3, STM32MP257CAI3, STM32MP257CAK3, STM32MP257CAL3, STM32MP257DAI3, STM32MP257DAK3, STM32MP257DAL3, STM32MP257F-DK, STM32MP257F-EV1, STM32MP257FAI3, STM32MP257FAK3, STM32MP257FAL3, STM32N645A0H3Q, STM32N645B0H3Q, STM32N645I0H3Q, STM32N645L0H3Q, STM32N645X0H3Q, STM32N645Z0H3Q, STM32N647A0H3Q, STM32N647B0H3Q, STM32N647I0H3Q, STM32N647L0H3Q, STM32N647X0H3Q, STM32N647Z0H3Q, STM32N655A0H3Q, STM32N655B0H3Q, STM32N655I0H3Q, STM32N655L0H3Q, STM32N655X0H3Q, STM32N655Z0H3Q, STM32N6570-DK, STM32N657A0H3Q, STM32N657B0H3Q, STM32N657I0H3Q, STM32N657L0H3Q, STM32N657X0H3Q, STM32N657Z0H3Q, STM32U031C6T6, STM32U031C6U6, STM32U031C8T6, STM32U031C8U6, STM32U031F4P6, STM32U031F6P6, STM32U031F8P6, STM32U031G6Y6TR, STM32U031G8Y6TR, STM32U031K4U6, STM32U031K6U6, STM32U031K8U6, STM32U031R6I6, STM32U031R6T6, STM32U031R8I6, STM32U031R8T6, STM32U073C8T6, STM32U073C8U6, STM32U073CBT6, STM32U073CBU6, STM32U073CCT6, STM32U073CCU6, STM32U073H8Y6TR, STM32U073HBY6TR, STM32U073HCY6TR, STM32U073K8U6, STM32U073KBU6, STM32U073KCU6, STM32U073M8I6, STM32U073M8T6, STM32U073MBI6, STM32U073MBT6, STM32U073MCI6, STM32U073MCT6, STM32U073R8I6, STM32U073R8T6, STM32U073RBI6, STM32U073RBT6, STM32U073RCI6, STM32U073RCT6, STM32U083C-DK, STM32U083CCT6, STM32U083CCU6, STM32U083HCY6TR, STM32U083KCU6, STM32U083MCI6, STM32U083MCT6, STM32U083RCI6, STM32U083RCT6, STM32U375CET6, STM32U375CET6Q, STM32U375CEU6, STM32U375CEU6Q, STM32U375CEY6QTR, STM32U375CGT6, STM32U375CGT6Q, STM32U375CGU6, STM32U375CGU6Q, STM32U375CGY6QTR, STM32U375KEU6, STM32U375KGU6, STM32U375REI6, STM32U375REI6Q, STM32U375RET6, STM32U375RET6Q, STM32U375REY6GTR, STM32U375REY6QTR, STM32U375RGI6, STM32U375RGI6Q, STM32U375RGT6, STM32U375RGT6Q, STM32U375RGY6GTR, STM32U375RGY6QTR, STM32U375VEI6, STM32U375VEI6Q, STM32U375VET6, STM32U375VET6Q, STM32U375VGI6, STM32U375VGI6Q, STM32U375VGT6, STM32U375VGT6Q, STM32U385CGT6, STM32U385CGT6Q, STM32U385CGU6, STM32U385CGU6Q, STM32U385CGY6QTR, STM32U385KGU6, STM32U385RGI6, STM32U385RGI6Q, STM32U385RGT6, STM32U385RGT6Q, STM32U385RGY6GTR, STM32U385RGY6QTR, STM32U385VGI6, STM32U385VGI6Q, STM32U385VGT6, STM32U385VGT6Q, STM32U535CBT6, STM32U535CBT6Q, STM32U535CBU6, STM32U535CBU6Q, STM32U535CCT6, STM32U535CCT6Q, STM32U535CCU6, STM32U535CCU6Q, STM32U535CET6, STM32U535CET6Q, STM32U535CEU6, STM32U535CEU6Q, STM32U535JEY6QTR, STM32U535NCY6QTR, STM32U535NEY6QTR, STM32U535RBI6, STM32U535RBI6Q, STM32U535RBT6, STM32U535RBT6Q, STM32U535RCI6, STM32U535RCI6Q, STM32U535RCT6, STM32U535RCT6Q, STM32U535REI6, STM32U535REI6Q, STM32U535RET6, STM32U535RET6Q, STM32U535VCI6, STM32U535VCI6Q, STM32U535VCT6, STM32U535VCT6Q, STM32U535VEI6, STM32U535VEI6Q, STM32U535VET6, STM32U535VET6Q, STM32U545CET6, STM32U545CET6Q, STM32U545CEU6, STM32U545CEU6Q, STM32U545JEY6QTR, STM32U545NEY6QTR, STM32U545REI6, STM32U545REI6Q, STM32U545RET6, STM32U545RET6Q, STM32U545VEI6, STM32U545VEI6Q, STM32U545VET6, STM32U545VET6Q, STM32U595AIH6, STM32U595AIH6Q, STM32U595AJH6, STM32U595AJH6Q, STM32U595QII6, STM32U595QII6Q, STM32U595QJI6, STM32U595QJI6Q, STM32U595RIT6, STM32U595RIT6Q, STM32U595RJT6, STM32U595RJT6Q, STM32U595VIT6, STM32U595VIT6Q, STM32U595VJT6, STM32U595VJT6Q, STM32U595ZIT6, STM32U595ZIT6Q, STM32U595ZIY6QTR, STM32U595ZJT6, STM32U595ZJT6Q, STM32U595ZJY6QTR, STM32U599BJY6QTR, STM32U599NIH6Q, STM32U599NJH6Q, STM32U599VIT6Q, STM32U599VJT6, STM32U599VJT6Q, STM32U599ZIT6Q, STM32U599ZIY6QTR, STM32U599ZJT6Q, STM32U599ZJY6QTR, STM32U5A5AJH6, STM32U5A5AJH6Q, STM32U5A5QII3Q , STM32U5A5QJI6, STM32U5A5QJI6Q, STM32U5A5RJT6, STM32U5A5RJT6Q, STM32U5A5VJT6, STM32U5A5VJT6Q, STM32U5A5ZJT6, STM32U5A5ZJT6Q, STM32U5A5ZJY6QTR, STM32U5A9BJY6QTR, STM32U5A9J-DK, STM32U5A9NJH6Q, STM32U5A9VJT6Q, STM32U5A9ZJT6Q, STM32U5A9ZJY6QTR, STM32U5F7VIT6, STM32U5F7VIT6Q, STM32U5F7VJT6, STM32U5F7VJT6Q, STM32U5F9BJY6QTR, STM32U5F9NJH6Q, STM32U5F9VIT6Q, STM32U5F9VJT6Q, STM32U5F9ZIJ6QTR, STM32U5F9ZIT6Q, STM32U5F9ZJJ6QTR, STM32U5F9ZJT6Q, STM32U5G7VJT6, STM32U5G7VJT6Q, STM32U5G9BJY6QTR, STM32U5G9J-DK1, STM32U5G9J-DK2, STM32U5G9NJH6Q, STM32U5G9VJT6Q, STM32U5G9ZJJ6QTR, STM32U5G9ZJT6Q, STM32WB05KZV6TR, STM32WB05KZV7TR, STM32WB05TZF6TR, STM32WB05TZF7TR, STM32WB06CCF6TR, STM32WB06CCF7TR, STM32WB06CCV6TR, STM32WB06CCV7TR, STM32WB06KCV6TR, STM32WB06KCV7TR, STM32WB07CCF6TR, STM32WB07CCF7TR, STM32WB07CCV6TR, STM32WB07CCV7TR, STM32WB07KCV6TR, STM32WB07KCV7TR, STM32WB09KEV6TR, STM32WB09KEV7TR, STM32WB09TEF6TR, STM32WB09TEF7TR, STM32WB1MMCH6, STM32WBA50KGU6, STM32WBA50KGU6TR, STM32WBA52CEU6, STM32WBA52CEU6TR, STM32WBA52CEU7, STM32WBA52CEU7TR, STM32WBA52CGU6, STM32WBA52CGU6TR, STM32WBA52CGU6U, STM32WBA52CGU7, STM32WBA52CGU7TR, STM32WBA52KEU6, STM32WBA52KEU6TR, STM32WBA52KGU6, STM32WBA52KGU6TR, STM32WBA54CEU6, STM32WBA54CEU6TR, STM32WBA54CEU7, STM32WBA54CEU7TR, STM32WBA54CGU6, STM32WBA54CGU6TR, STM32WBA54CGU7, STM32WBA54CGU7TR, STM32WBA54KEU6, STM32WBA54KEU6TR, STM32WBA54KEU7, STM32WBA54KEU7TR, STM32WBA54KGU6, STM32WBA54KGU6TR, STM32WBA54KGU7, STM32WBA54KGU7TR, STM32WBA55CEU6, STM32WBA55CEU6TR, STM32WBA55CEU7, STM32WBA55CEU7TR, STM32WBA55CGU6, STM32WBA55CGU6TR, STM32WBA55CGU6U, STM32WBA55CGU7, STM32WBA55CGU7TR, STM32WBA55G-DK1, STM32WBA55HEF6, STM32WBA55HEF7, STM32WBA55HGF6, STM32WBA55HGF7, STM32WBA55UEI6, STM32WBA55UEI6TR, STM32WBA55UEI7, STM32WBA55UEI7TR, STM32WBA55UGI6, STM32WBA55UGI6TR, STM32WBA55UGI7, STM32WBA55UGI7TR, STM32WBA5MMGH6TR, STM32WBA62MGF6, STM32WBA62MIF6, STM32WBA65MGF7, STM32WBA65MIF6, STM32WBA65MIF7, STM32WL30K8V6, STM32WL30KBV6, STM32WL31C8V6, STM32WL31CBV6, STM32WL31K8V6, STM32WL31KBV6, STM32WL33C8V6, STM32WL33C8V6X, STM32WL33CBV6, STM32WL33CBV6X, STM32WL33CCV6, STM32WL33CCV6A, STM32WL33CCV6X, STM32WL33K8V7, STM32WL33K8V7X, STM32WL33KBV7 , STM32WL33KBV7X, STM32WL33KCV7, STM32WL33KCV7X, STM32WL5MOCH6, STM32WL5MOCH6TR]
2025-07-09 14:05:44,744 [INFO] BoardInfo:889 - No configuration file found for board P-NUCLEO-WB55
2025-07-09 14:05:44,745 [INFO] DbBoards:161 - Kit is not supported: P-NUCLEO-WB55
2025-07-09 14:05:44,750 [INFO] BoardInfo:889 - No configuration file found for board STEVAL-BFA001V1B
2025-07-09 14:05:44,751 [INFO] DbBoards:161 - Kit is not supported: STEVAL-BFA001V1B
2025-07-09 14:05:44,751 [INFO] BoardInfo:889 - No configuration file found for board STEVAL-BFA001V2B
2025-07-09 14:05:44,752 [INFO] DbBoards:161 - Kit is not supported: STEVAL-BFA001V2B
2025-07-09 14:05:44,898 [INFO] DbBoards:168 - Found 212 boards, 209 are supported
2025-07-09 14:05:44,898 [INFO] DbBoards:169 - Found 212 boards, 43 of them is supported for  Bsp
2025-07-09 14:05:44,901 [INFO] ApiDb:668 - CubeFinder database Data Model version=2.1
2025-07-09 14:05:44,901 [INFO] ApiDb:669 - CubeFinder database Configuration version=3.0.39
2025-07-09 14:05:44,901 [INFO] ApiDb:670 - CubeFinder database generation date=2025-06-24 (1750757100)
2025-07-09 14:05:44,901 [INFO] ApiDb:671 - CubeFinder database FW Pack versions=[FP-ATR-ASTRA1_V2.0.0, FP-SNS-FLIGHT1_V5.1.0, FP-SNS-MOTENV1_V5.0.0, FP-SNS-MOTENVWB1_V1.4.0, FP-SNS-SMARTAG2_V1.2.0, FP-SNS-STBOX1_V2.0.0, STM32Cube_FW_C0_V1.4.0, STM32Cube_FW_F4_V1.28.2, STM32Cube_FW_F7_V1.17.3, STM32Cube_FW_G0_V1.6.2, STM32Cube_FW_G4_V1.6.1, STM32Cube_FW_H5_V1.5.0, STM32Cube_FW_H7RS_V1.2.0, STM32Cube_FW_H7_V1.12.1, STM32Cube_FW_L0_V1.12.2, STM32Cube_FW_L4_V1.18.1, STM32Cube_FW_L5_V1.5.1, STM32Cube_FW_N6_V1.2.0, STM32Cube_FW_U0_V1.3.0, STM32Cube_FW_U3_V1.2.0, STM32Cube_FW_U5_V1.8.0, STM32Cube_FW_WB0_V1.3.0, STM32Cube_FW_WBA_V1.7.0, STM32Cube_FW_WB_V1.23.0, STM32Cube_FW_WL3_V1.2.0, STM32Cube_FW_WL_V1.3.1, X-CUBE-ALGOBUILD_V1.4.0, X-CUBE-ALS_V1.0.2, X-CUBE-AZRTOS-F4_V1.1.0, X-CUBE-AZRTOS-F7_V1.1.0, X-CUBE-AZRTOS-G0_V1.1.0, X-CUBE-AZRTOS-G4_V2.0.0, X-CUBE-AZRTOS-H7RS_V1.1.0, X-CUBE-AZRTOS-H7_V3.3.0, X-CUBE-AZRTOS-L4_V2.0.0, X-CUBE-AZRTOS-L5_V2.0.0, X-CUBE-AZRTOS-WB_V2.0.0, X-CUBE-AZRTOS-WL_V2.0.0, X-CUBE-BLE1_V7.1.0, X-CUBE-BLE2_V3.3.0, X-CUBE-BLEMGR_V4.0.0, X-CUBE-EEPRMA1_V5.1.0, X-CUBE-FREERTOS_V1.3.1, X-CUBE-GNSS1_V6.0.0, X-CUBE-MEMS1_V11.3.0, X-CUBE-NFC4_V3.0.0, X-CUBE-NFC7_V2.0.0, X-CUBE-SFXS2LP1_V4.0.0, X-CUBE-SUBG2_V5.0.0, X-CUBE-TOF1_V3.4.3]
2025-07-09 14:05:46,272 [INFO] ApiDb:240 - Found 880 in-development CPN: [B-G473E-ZEST1S, B-WB1M-WPAN1, B-WBA5M-WPAN, B-WL5M-SUBG1, NUCLEO-C031C6, NUCLEO-C051C8, NUCLEO-C071RB, NUCLEO-C092RC, NUCLEO-H503RB, NUCLEO-H533RE, NUCLEO-H563ZI, NUCLEO-H7S3L8, NUCLEO-N657X0-Q, NUCLEO-U031R8, NUCLEO-U083RC, NUCLEO-U385RG-Q, NUCLEO-U545RE-Q, NUCLEO-U5A5ZJ-Q, NUCLEO-WB05KZ, NUCLEO-WB07CC, NUCLEO-WB09KE, NUCLEO-WBA52CG, NUCLEO-WBA55CG, NUCLEO-WL33CC1, NUCLEO-WL33CC2, STEVAL-PROTEUS1, STEVAL-SMARTAG2, STEVAL-STWINBX1, STM320518-EVAL, STM32C0116-DK, STM32C011D6Y3TR, STM32C011D6Y6TR, STM32C011F4P3, STM32C011F4P6, STM32C011F4U3, STM32C011F4U6TR, STM32C011F6P3, STM32C011F6P6, STM32C011F6U3, STM32C011F6U6TR, STM32C011J4M3, STM32C011J4M6, STM32C011J6M3, STM32C011J6M6, STM32C0316-DK, STM32C031C4T3, STM32C031C4T6, STM32C031C4U3, STM32C031C4U6, STM32C031C6T3, STM32C031C6T6, STM32C031C6U3, STM32C031C6U6, STM32C031F4P3, STM32C031F4P6, STM32C031F6P3, STM32C031F6P6, STM32C031G4U3, STM32C031G4U6, STM32C031G6U3, STM32C031G6U6, STM32C031K4T3, STM32C031K4T6, STM32C031K4U3, STM32C031K4U6, STM32C031K6T3, STM32C031K6T6, STM32C031K6U3, STM32C031K6U6, STM32C051C6T6, STM32C051C6U6, STM32C051C8T6, STM32C051C8U6, STM32C051D8Y6TR, STM32C051F6P6, STM32C051F8P6, STM32C051G6U6, STM32C051G8U6, STM32C051K6T6, STM32C051K6U6, STM32C051K8T6, STM32C071C8T6, STM32C071C8T6N, STM32C071C8U6, STM32C071C8U6N, STM32C071CBT6, STM32C071CBT6N, STM32C071CBU6, STM32C071CBU6N, STM32C071F8P6, STM32C071F8P6N, STM32C071FBP6, STM32C071FBP6N, STM32C071FBY6TR, STM32C071G8U6, STM32C071G8U6N, STM32C071GBU6, STM32C071GBU6N, STM32C071K8T6, STM32C071K8T6N, STM32C071K8U6, STM32C071K8U6N, STM32C071KBT6, STM32C071KBT6N, STM32C071KBU6, STM32C071KBU6N, STM32C071R8T6, STM32C071R8T6N, STM32C071RBI6N, STM32C071RBT6, STM32C071RBT6N, STM32C091CBT6, STM32C091CBU6, STM32C091CCT6, STM32C091CCU6, STM32C091ECY6TR, STM32C091FBP6, STM32C091FCP6, STM32C091GBU6, STM32C091GCU6, STM32C091KBT6, STM32C091KBU6, STM32C091KCT6, STM32C091KCU6, STM32C091RBT6, STM32C091RCI6, STM32C091RCT6, STM32C092CBT6, STM32C092CBU6, STM32C092CCT6, STM32C092CCU6, STM32C092ECY6TR, STM32C092FBP6, STM32C092FCP6, STM32C092GBU6, STM32C092GCU6, STM32C092KBT6, STM32C092KBU6, STM32C092KCT6, STM32C092KCU6, STM32C092RBT6, STM32C092RCI6, STM32C092RCT6, STM32G071K8TXN, STM32G071K8UXN, STM32G081GBU6N, STM32G081KBT6N, STM32G081KBUXN, STM32G0B1CCT6N, STM32G0B1KCT6, STM32G0B1NEY6TR, STM32G0B1RCT6N, STM32G0C1CCT6, STM32G0C1CCT6N, STM32G0C1CCU6N, STM32G0C1CET6N, STM32G0C1CEU6N, STM32G0C1KCT6, STM32G0C1NEY6TR, STM32G0C1RCI6N, STM32G0C1RCT6N, STM32G0C1REI6N, STM32G0C1RET6N, STM32G0C1VCI6, STM32G0C1VEI6, STM32G411C6T3, STM32G411C6T6, STM32G411C6U3, STM32G411C6U6, STM32G411C8T3, STM32G411C8T6, STM32G411C8U3, STM32G411C8U6, STM32G411CBT3, STM32G411CBT6, STM32G411CBU3, STM32G411CBU6, STM32G411K6T3, STM32G411K6T6, STM32G411K6U3, STM32G411K6U6, STM32G411K8T3, STM32G411K8T6, STM32G411K8U3, STM32G411K8U6, STM32G411KBT3, STM32G411KBT6, STM32G411KBU3, STM32G411KBU6, STM32G411M6T3, STM32G411M6T6, STM32G411M8T3, STM32G411M8T6, STM32G411MBT3, STM32G411MBT6, STM32G411R6T3, STM32G411R6T6, STM32G411R8T3, STM32G411R8T6, STM32G411RBT3, STM32G411RBT6, STM32G414CBT3, STM32G414CBT6, STM32G414CBU3, STM32G414CBU6, STM32G414CCT3, STM32G414CCT6, STM32G414CCU3, STM32G414CCU6, STM32G414MBT3, STM32G414MBT6, STM32G414MCT3, STM32G414MCT6, STM32G414RBT3, STM32G414RBT6, STM32G414RCT3, STM32G414RCT6, STM32G414VBT3, STM32G414VBT6, STM32G414VCT3, STM32G414VCT6, STM32G431CBT3Z, STM32G431RBT3Z, STM32G471CCT6, STM32G471CCU6, STM32G471CET3, STM32G471CET6, STM32G471CEU3, STM32G471CEU6, STM32G471MCT6, STM32G471MET3, STM32G471MET6, STM32G471MEY6TR, STM32G471QCT6, STM32G471QET3, STM32G471RCT6, STM32G471RET3, STM32G471RET6, STM32G471VCH6, STM32G471VCI6, STM32G471VCT6, STM32G471VEH3, STM32G471VEH6, STM32G471VEI3, STM32G471VEI6, STM32G471VET3, STM32G471VET6, STM32G473QET3Z, STM32G473RET3Z, STM32G474CCT6, STM32G491RET3Z, STM32H503CBT6, STM32H503CBU6, STM32H503EBY6TR, STM32H503KBU6, STM32H503RBT6, STM32H523CCT6, STM32H523CCU6, STM32H523CET6, STM32H523CEU6, STM32H523HEY6TR, STM32H523RCT6, STM32H523RET6, STM32H523VCI6, STM32H523VCT6, STM32H523VEI6, STM32H523VET6, STM32H523ZCJ6, STM32H523ZCT6, STM32H523ZEJ6, STM32H523ZET6, STM32H533CET6, STM32H533CEU6, STM32H533HEY6TR, STM32H533RET6, STM32H533VEI6, STM32H533VET6, STM32H533ZEJ6, STM32H533ZET6, STM32H562AGI6, STM32H562AII6, STM32H562IGK6, STM32H562IGT6, STM32H562IIK6, STM32H562IIT6, STM32H562RGT6, STM32H562RGV6, STM32H562RIT6, STM32H562RIV6, STM32H562VGT6, STM32H562VIT6, STM32H562ZGT6, STM32H562ZIT6, STM32H563AGI6, STM32H563AII3Q, STM32H563AII6, STM32H563IGK6, STM32H563IGT6, STM32H563IIK3Q, STM32H563IIK6, STM32H563IIT3Q, STM32H563IIT6, STM32H563MIY3QTR, STM32H563RGT6, STM32H563RGV6, STM32H563RIT6, STM32H563RIV6, STM32H563VGT6, STM32H563VIT3Q, STM32H563VIT6, STM32H563ZGT6, STM32H563ZIT3Q, STM32H563ZIT6, STM32H573AII3Q, STM32H573AII6, STM32H573I-DK, STM32H573IIK3Q, STM32H573IIK6, STM32H573IIT3Q, STM32H573IIT6, STM32H573MIY3QTR, STM32H573RIT6, STM32H573RIV6, STM32H573VIT3Q, STM32H573VIT6, STM32H573ZIT3Q, STM32H573ZIT6, STM32H7R3A8I6, STM32H7R3I8K6, STM32H7R3I8T6, STM32H7R3L8H6, STM32H7R3L8H6H, STM32H7R3R8V6, STM32H7R3V8H6, STM32H7R3V8T6, STM32H7R3V8Y6TR, STM32H7R3Z8J6, STM32H7R3Z8T6, STM32H7R7A8I6, STM32H7R7I8K6, STM32H7R7I8T6, STM32H7R7L8H6, STM32H7R7L8H6H, STM32H7R7Z8J6, STM32H7S3A8I6, STM32H7S3I8K6, STM32H7S3I8T6, STM32H7S3L8H6, STM32H7S3L8H6H, STM32H7S3R8V6, STM32H7S3V8H6, STM32H7S3V8T6, STM32H7S3V8Y6TR, STM32H7S3Z8J6, STM32H7S3Z8T6, STM32H7S78-DK, STM32H7S7A8I6, STM32H7S7I8K6, STM32H7S7I8T6, STM32H7S7L8H6, STM32H7S7L8H6H, STM32H7S7Z8J6, STM32L4R5QGI6STR, STM32MP131AAE3, STM32MP131AAF3, STM32MP131AAG3, STM32MP131CAE3, STM32MP131CAF3, STM32MP131CAG3, STM32MP131DAE7, STM32MP131DAF7, STM32MP131DAG7, STM32MP131FAE7, STM32MP131FAF7, STM32MP131FAG7, STM32MP133AAE3, STM32MP133AAF3, STM32MP133AAG3, STM32MP133CAE3, STM32MP133CAF3, STM32MP133CAG3, STM32MP133DAE7, STM32MP133DAF7, STM32MP133DAG7, STM32MP133FAE7, STM32MP133FAF7, STM32MP133FAG7, STM32MP135AAE3, STM32MP135AAF3, STM32MP135AAG3, STM32MP135CAE3, STM32MP135CAF3, STM32MP135CAG3, STM32MP135DAE7, STM32MP135DAF7, STM32MP135DAG7, STM32MP135F-DK, STM32MP135FAE7, STM32MP135FAF7, STM32MP135FAF7T, STM32MP135FAF7U, STM32MP135FAG7, STM32MP211AAL3, STM32MP211AAM3, STM32MP211AAN3, STM32MP211AAO3, STM32MP211CAL3, STM32MP211CAM3, STM32MP211CAN3, STM32MP211CAO3, STM32MP211DAL3, STM32MP211DAM3, STM32MP211DAN3, STM32MP211DAO3, STM32MP211FAL3, STM32MP211FAM3, STM32MP211FAN3, STM32MP211FAO3, STM32MP213AAL3, STM32MP213AAM3, STM32MP213AAN3, STM32MP213AAO3, STM32MP213CAL3, STM32MP213CAM3, STM32MP213CAN3, STM32MP213CAO3, STM32MP213DAL3, STM32MP213DAM3, STM32MP213DAN3, STM32MP213DAO3, STM32MP213FAL3, STM32MP213FAM3, STM32MP213FAN3, STM32MP213FAO3, STM32MP215AAL3, STM32MP215AAM3, STM32MP215AAN3, STM32MP215AAO3, STM32MP215CAL3, STM32MP215CAM3, STM32MP215CAN3, STM32MP215CAO3, STM32MP215DAL3, STM32MP215DAM3, STM32MP215DAN3, STM32MP215DAO3, STM32MP215F-DK, STM32MP215FAL3, STM32MP215FAM3, STM32MP215FAN3, STM32MP215FAO3, STM32MP231AAJ3, STM32MP231AAK3, STM32MP231AAL3, STM32MP231CAJ3, STM32MP231CAK3, STM32MP231CAL3, STM32MP231DAJ3, STM32MP231DAK3, STM32MP231DAL3, STM32MP231FAJ3, STM32MP231FAK3, STM32MP231FAL3, STM32MP233AAJ3, STM32MP233AAK3, STM32MP233AAL3, STM32MP233CAJ3, STM32MP233CAK3, STM32MP233CAL3, STM32MP233DAJ3, STM32MP233DAK3, STM32MP233DAL3, STM32MP233FAJ3, STM32MP233FAK3, STM32MP233FAL3, STM32MP235AAJ3, STM32MP235AAK3, STM32MP235AAL3, STM32MP235CAJ3, STM32MP235CAK3, STM32MP235CAL3, STM32MP235DAJ3, STM32MP235DAK3, STM32MP235DAL3, STM32MP235FAJ3, STM32MP235FAK3, STM32MP235FAL3, STM32MP251AAI3, STM32MP251AAK3, STM32MP251AAL3, STM32MP251CAI3, STM32MP251CAK3, STM32MP251CAL3, STM32MP251DAI3, STM32MP251DAK3, STM32MP251DAL3, STM32MP251FAI3, STM32MP251FAK3, STM32MP251FAL3, STM32MP253AAI3, STM32MP253AAK3, STM32MP253AAL3, STM32MP253CAI3, STM32MP253CAK3, STM32MP253CAL3, STM32MP253DAI3, STM32MP253DAK3, STM32MP253DAL3, STM32MP253FAI3, STM32MP253FAK3, STM32MP253FAL3, STM32MP255AAI3, STM32MP255AAK3, STM32MP255AAL3, STM32MP255CAI3, STM32MP255CAK3, STM32MP255CAL3, STM32MP255DAI3, STM32MP255DAK3, STM32MP255DAL3, STM32MP255FAI3, STM32MP255FAK3, STM32MP255FAL3, STM32MP257AAI3, STM32MP257AAK3, STM32MP257AAL3, STM32MP257CAI3, STM32MP257CAK3, STM32MP257CAL3, STM32MP257DAI3, STM32MP257DAK3, STM32MP257DAL3, STM32MP257F-DK, STM32MP257F-EV1, STM32MP257FAI3, STM32MP257FAK3, STM32MP257FAL3, STM32N645A0H3Q, STM32N645B0H3Q, STM32N645I0H3Q, STM32N645L0H3Q, STM32N645X0H3Q, STM32N645Z0H3Q, STM32N647A0H3Q, STM32N647B0H3Q, STM32N647I0H3Q, STM32N647L0H3Q, STM32N647X0H3Q, STM32N647Z0H3Q, STM32N655A0H3Q, STM32N655B0H3Q, STM32N655I0H3Q, STM32N655L0H3Q, STM32N655X0H3Q, STM32N655Z0H3Q, STM32N6570-DK, STM32N657A0H3Q, STM32N657B0H3Q, STM32N657I0H3Q, STM32N657L0H3Q, STM32N657X0H3Q, STM32N657Z0H3Q, STM32U031C6T6, STM32U031C6U6, STM32U031C8T6, STM32U031C8U6, STM32U031F4P6, STM32U031F6P6, STM32U031F8P6, STM32U031G6Y6TR, STM32U031G8Y6TR, STM32U031K4U6, STM32U031K6U6, STM32U031K8U6, STM32U031R6I6, STM32U031R6T6, STM32U031R8I6, STM32U031R8T6, STM32U073C8T6, STM32U073C8U6, STM32U073CBT6, STM32U073CBU6, STM32U073CCT6, STM32U073CCU6, STM32U073H8Y6TR, STM32U073HBY6TR, STM32U073HCY6TR, STM32U073K8U6, STM32U073KBU6, STM32U073KCU6, STM32U073M8I6, STM32U073M8T6, STM32U073MBI6, STM32U073MBT6, STM32U073MCI6, STM32U073MCT6, STM32U073R8I6, STM32U073R8T6, STM32U073RBI6, STM32U073RBT6, STM32U073RCI6, STM32U073RCT6, STM32U083C-DK, STM32U083CCT6, STM32U083CCU6, STM32U083HCY6TR, STM32U083KCU6, STM32U083MCI6, STM32U083MCT6, STM32U083RCI6, STM32U083RCT6, STM32U375CET6, STM32U375CET6Q, STM32U375CEU6, STM32U375CEU6Q, STM32U375CEY6QTR, STM32U375CGT6, STM32U375CGT6Q, STM32U375CGU6, STM32U375CGU6Q, STM32U375CGY6QTR, STM32U375KEU6, STM32U375KGU6, STM32U375REI6, STM32U375REI6Q, STM32U375RET6, STM32U375RET6Q, STM32U375REY6GTR, STM32U375REY6QTR, STM32U375RGI6, STM32U375RGI6Q, STM32U375RGT6, STM32U375RGT6Q, STM32U375RGY6GTR, STM32U375RGY6QTR, STM32U375VEI6, STM32U375VEI6Q, STM32U375VET6, STM32U375VET6Q, STM32U375VGI6, STM32U375VGI6Q, STM32U375VGT6, STM32U375VGT6Q, STM32U385CGT6, STM32U385CGT6Q, STM32U385CGU6, STM32U385CGU6Q, STM32U385CGY6QTR, STM32U385KGU6, STM32U385RGI6, STM32U385RGI6Q, STM32U385RGT6, STM32U385RGT6Q, STM32U385RGY6GTR, STM32U385RGY6QTR, STM32U385VGI6, STM32U385VGI6Q, STM32U385VGT6, STM32U385VGT6Q, STM32U535CBT6, STM32U535CBT6Q, STM32U535CBU6, STM32U535CBU6Q, STM32U535CCT6, STM32U535CCT6Q, STM32U535CCU6, STM32U535CCU6Q, STM32U535CET6, STM32U535CET6Q, STM32U535CEU6, STM32U535CEU6Q, STM32U535JEY6QTR, STM32U535NCY6QTR, STM32U535NEY6QTR, STM32U535RBI6, STM32U535RBI6Q, STM32U535RBT6, STM32U535RBT6Q, STM32U535RCI6, STM32U535RCI6Q, STM32U535RCT6, STM32U535RCT6Q, STM32U535REI6, STM32U535REI6Q, STM32U535RET6, STM32U535RET6Q, STM32U535VCI6, STM32U535VCI6Q, STM32U535VCT6, STM32U535VCT6Q, STM32U535VEI6, STM32U535VEI6Q, STM32U535VET6, STM32U535VET6Q, STM32U545CET6, STM32U545CET6Q, STM32U545CEU6, STM32U545CEU6Q, STM32U545JEY6QTR, STM32U545NEY6QTR, STM32U545REI6, STM32U545REI6Q, STM32U545RET6, STM32U545RET6Q, STM32U545VEI6, STM32U545VEI6Q, STM32U545VET6, STM32U545VET6Q, STM32U595AIH6, STM32U595AIH6Q, STM32U595AJH6, STM32U595AJH6Q, STM32U595QII6, STM32U595QII6Q, STM32U595QJI6, STM32U595QJI6Q, STM32U595RIT6, STM32U595RIT6Q, STM32U595RJT6, STM32U595RJT6Q, STM32U595VIT6, STM32U595VIT6Q, STM32U595VJT6, STM32U595VJT6Q, STM32U595ZIT6, STM32U595ZIT6Q, STM32U595ZIY6QTR, STM32U595ZJT6, STM32U595ZJT6Q, STM32U595ZJY6QTR, STM32U599BJY6QTR, STM32U599NIH6Q, STM32U599NJH6Q, STM32U599VIT6Q, STM32U599VJT6, STM32U599VJT6Q, STM32U599ZIT6Q, STM32U599ZIY6QTR, STM32U599ZJT6Q, STM32U599ZJY6QTR, STM32U5A5AJH6, STM32U5A5AJH6Q, STM32U5A5QII3Q , STM32U5A5QJI6, STM32U5A5QJI6Q, STM32U5A5RJT6, STM32U5A5RJT6Q, STM32U5A5VJT6, STM32U5A5VJT6Q, STM32U5A5ZJT6, STM32U5A5ZJT6Q, STM32U5A5ZJY6QTR, STM32U5A9BJY6QTR, STM32U5A9J-DK, STM32U5A9NJH6Q, STM32U5A9VJT6Q, STM32U5A9ZJT6Q, STM32U5A9ZJY6QTR, STM32U5F7VIT6, STM32U5F7VIT6Q, STM32U5F7VJT6, STM32U5F7VJT6Q, STM32U5F9BJY6QTR, STM32U5F9NJH6Q, STM32U5F9VIT6Q, STM32U5F9VJT6Q, STM32U5F9ZIJ6QTR, STM32U5F9ZIT6Q, STM32U5F9ZJJ6QTR, STM32U5F9ZJT6Q, STM32U5G7VJT6, STM32U5G7VJT6Q, STM32U5G9BJY6QTR, STM32U5G9J-DK1, STM32U5G9J-DK2, STM32U5G9NJH6Q, STM32U5G9VJT6Q, STM32U5G9ZJJ6QTR, STM32U5G9ZJT6Q, STM32WB05KZV6TR, STM32WB05KZV7TR, STM32WB05TZF6TR, STM32WB05TZF7TR, STM32WB06CCF6TR, STM32WB06CCF7TR, STM32WB06CCV6TR, STM32WB06CCV7TR, STM32WB06KCV6TR, STM32WB06KCV7TR, STM32WB07CCF6TR, STM32WB07CCF7TR, STM32WB07CCV6TR, STM32WB07CCV7TR, STM32WB07KCV6TR, STM32WB07KCV7TR, STM32WB09KEV6TR, STM32WB09KEV7TR, STM32WB09TEF6TR, STM32WB09TEF7TR, STM32WB1MMCH6, STM32WBA50KGU6, STM32WBA50KGU6TR, STM32WBA52CEU6, STM32WBA52CEU6TR, STM32WBA52CEU7, STM32WBA52CEU7TR, STM32WBA52CGU6, STM32WBA52CGU6TR, STM32WBA52CGU6U, STM32WBA52CGU7, STM32WBA52CGU7TR, STM32WBA52KEU6, STM32WBA52KEU6TR, STM32WBA52KGU6, STM32WBA52KGU6TR, STM32WBA54CEU6, STM32WBA54CEU6TR, STM32WBA54CEU7, STM32WBA54CEU7TR, STM32WBA54CGU6, STM32WBA54CGU6TR, STM32WBA54CGU7, STM32WBA54CGU7TR, STM32WBA54KEU6, STM32WBA54KEU6TR, STM32WBA54KEU7, STM32WBA54KEU7TR, STM32WBA54KGU6, STM32WBA54KGU6TR, STM32WBA54KGU7, STM32WBA54KGU7TR, STM32WBA55CEU6, STM32WBA55CEU6TR, STM32WBA55CEU7, STM32WBA55CEU7TR, STM32WBA55CGU6, STM32WBA55CGU6TR, STM32WBA55CGU6U, STM32WBA55CGU7, STM32WBA55CGU7TR, STM32WBA55G-DK1, STM32WBA55HEF6, STM32WBA55HEF7, STM32WBA55HGF6, STM32WBA55HGF7, STM32WBA55UEI6, STM32WBA55UEI6TR, STM32WBA55UEI7, STM32WBA55UEI7TR, STM32WBA55UGI6, STM32WBA55UGI6TR, STM32WBA55UGI7, STM32WBA55UGI7TR, STM32WBA5MMGH6TR, STM32WBA62MGF6, STM32WBA62MIF6, STM32WBA65MGF7, STM32WBA65MIF6, STM32WBA65MIF7, STM32WL30K8V6, STM32WL30KBV6, STM32WL31C8V6, STM32WL31CBV6, STM32WL31K8V6, STM32WL31KBV6, STM32WL33C8V6, STM32WL33C8V6X, STM32WL33CBV6, STM32WL33CBV6X, STM32WL33CCV6, STM32WL33CCV6A, STM32WL33CCV6X, STM32WL33K8V7, STM32WL33K8V7X, STM32WL33KBV7 , STM32WL33KBV7X, STM32WL33KCV7, STM32WL33KCV7X, STM32WL5MOCH6, STM32WL5MOCH6TR]
2025-07-09 14:05:46,276 [INFO] DbMcus:218 - Found 4801 MCUs, 4801 are supported
2025-07-09 14:05:46,277 [INFO] ApiDb:423 - Load user favorites file C:\Users\<USER>\Users\l1543/.stm32cubeide/favorites.boards.txt: 0 item(s)
2025-07-09 14:05:46,569 [INFO] ApiDb:427 - User favorites Boards=[]
2025-07-09 14:05:46,569 [INFO] DbBoards:198 - Set 0 / 0 favorites Boards
2025-07-09 14:05:46,618 [INFO] UtilMem:75 - End LoadConfig() Used Memory: 231568072 Bytes (309329920)
2025-07-09 14:05:46,711 [WARN] ThirdParty:833 - waiting for thirdparty lock release [change project]
2025-07-09 14:05:46,711 [INFO] ThirdParty:835 - entering critical section [change project]
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-PM33A1 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USBPD 4.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-H7 3.3.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC9 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfSSL 5.8.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_HOST 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-MOTENVWB1 1.4.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-F4 1.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics LIBJPEG 8.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-ATR-ASTRA1 2.0.2
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-BLE1 7.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SMBUS 2.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfMQTT 1.19.2
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_DEVICE 3.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ISPU 2.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-WB 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ST60 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-GNSS1 7.0.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-F7 1.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-L5 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-TOUCHGFX 4.25.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_DEVICE 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC6 3.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AI 10.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-STBOX1 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FreeRTOS 0.0.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-G0 1.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SAFEA1 1.2.2
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC4 3.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SUBG2 5.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-H7RS 1.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics STM32_WPAN 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :EmbeddedOffice I-CUBE-FS-RTOS 1.0.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics lwIP 2.0.3
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :Cesanta I-CUBE-Mongoose 7.13.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_HOST 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :Infineon AIROC-Wi-Fi-Bluetooth-STM32 1.7.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-G4 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-WB05N 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics PDM2PCM 3.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics PDM2PCM 3.3.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfTPM 3.8.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-H7 3.4.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-DISPLAY 3.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfSSH 1.4.20
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC7 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-TCPP 4.2.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :RealThread X-CUBE-RT-Thread_Nano 4.1.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-ATR-SIGFOX1 3.2.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC10 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-BLEMGR 4.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-EEPRMA1 5.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-FREERTOS 1.3.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics HAL Drivers 0.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics MBEDTLS 2.16.2
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-IPS 3.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ALS 1.0.2
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :emotas I-CUBE-CANOPEN 1.3.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics MBEDTLS 2.14.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :ITTIA_DB I-CUBE-ITTIADB 8.9.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :portGmbH I-Cube-SoM-uGOAL 1.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :quantropi X-CUBE-qispace-sdk-base 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ST67W61 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-STAIOTCFT 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics ThreadX 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-SMARTAG2 1.2.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-FLIGHT1 5.1.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-WL 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :SEGGER I-CUBE-embOS 1.3.1
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ALGOBUILD 1.4.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-DPower 1.3.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-MEMS1 11.3.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-MOTENV1 5.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_DEVICE 1.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-L4 2.0.0
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics ThreadX 0.0.2
2025-07-09 14:05:46,712 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics lwIP 2.1.2
2025-07-09 14:05:46,713 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SFXS2LP1 4.0.0
2025-07-09 14:05:46,713 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-BLE2 3.3.0
2025-07-09 14:05:46,713 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-TOF1 3.4.3
2025-07-09 14:05:46,713 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics PDM2PCM 3.2.0
2025-07-09 14:05:46,713 [INFO] ThirdParty:841 - exiting critical section [change project]
2025-07-09 14:05:46,954 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-09 14:05:46,954 [INFO] PinOutPanel:1589 - setPackage(STM32F303CCTx,LQFP48)
2025-07-09 14:05:47,217 [INFO] UtilMem:75 - Before build in PCC Used Memory: 214677480 Bytes (309329920)
2025-07-09 14:05:47,658 [INFO] UtilMem:75 - After build in PCC Used Memory: 185154976 Bytes (309329920)
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,675 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,676 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,677 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,678 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,679 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:05:47,784 [INFO] CADModel:165 - CPN selected for project levelSTM32F303CCT6
2025-07-09 14:05:47,784 [INFO] CADModel:114 - Register for checkConnection events
2025-07-09 14:05:47,829 [INFO] OpenFileManager:386 - Restore cursor
2025-07-09 14:50:11,864 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-09 14:50:11,976 [INFO] McuFinderGlobals:76 - Set McuFinderConnectedMode to true
2025-07-09 14:50:11,985 [INFO] MultiScanPanel:200 - Auto-refresh data requested => check proxy status ongoing
2025-07-09 14:50:11,989 [INFO] FinderPluginLoader:96 - Searching for filters in installed packs
2025-07-09 14:50:12,703 [INFO] LoadUrlFilesThread:185 - End of LoadServerUrlFiles without Thread
2025-07-09 14:50:12,780 [INFO] DetailPanel:341 - Set advertising image to C:/Users/<USER>/STM32Cube/Repository/\en.2400x1350px.jpeg
2025-07-09 14:50:32,745 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-09 14:50:32,745 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-09 14:50:32,861 [INFO] MicroXplorer:468 - Change Database Path : 
2025-07-09 14:50:32,861 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-09 14:50:32,889 [ERROR] ProjectManagerView:395 - 
java.lang.NullPointerException: Cannot invoke "javax.swing.JTextField.getText()" because the return value of "java.util.List.get(int)" is null
	at com.st.microxplorer.plugins.projectmanager.gui.ProjectChoiceTab$9.caretUpdate(ProjectChoiceTab.java:2706) ~[filemanager.jar:?]
	at javax.swing.text.JTextComponent.fireCaretUpdate(JTextComponent.java:408) ~[?:?]
	at javax.swing.text.JTextComponent$MutableCaretEvent.fire(JTextComponent.java:4484) ~[?:?]
	at javax.swing.text.JTextComponent$MutableCaretEvent.stateChanged(JTextComponent.java:4506) ~[?:?]
	at javax.swing.text.DefaultCaret.fireStateChanged(DefaultCaret.java:857) ~[?:?]
	at javax.swing.text.DefaultCaret.changeCaretPosition(DefaultCaret.java:1343) ~[?:?]
	at javax.swing.text.DefaultCaret.handleSetDot(DefaultCaret.java:1242) ~[?:?]
	at javax.swing.text.DefaultCaret.setDot(DefaultCaret.java:1223) ~[?:?]
	at javax.swing.text.DefaultCaret$Handler.insertUpdate(DefaultCaret.java:1819) ~[?:?]
	at javax.swing.text.AbstractDocument.fireInsertUpdate(AbstractDocument.java:227) ~[?:?]
	at javax.swing.text.AbstractDocument.handleInsertString(AbstractDocument.java:781) ~[?:?]
	at javax.swing.text.AbstractDocument.insertString(AbstractDocument.java:740) ~[?:?]
	at javax.swing.text.PlainDocument.insertString(PlainDocument.java:131) ~[?:?]
	at javax.swing.text.AbstractDocument.replace(AbstractDocument.java:699) ~[?:?]
	at javax.swing.text.JTextComponent.setText(JTextComponent.java:1725) ~[?:?]
	at com.st.microxplorer.plugins.projectmanager.gui.ProjectChoiceTab.createHeapStackFields(ProjectChoiceTab.java:993) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.projectmanager.gui.ProjectChoiceTab.buildLinkSettingsPanel(ProjectChoiceTab.java:3813) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.projectmanager.gui.ProjectChoiceTab.defineWindowsFields(ProjectChoiceTab.java:1987) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.projectmanager.gui.ProjectChoiceTab.updateSettings(ProjectChoiceTab.java:558) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.projectmanager.gui.ProjectSettingsPanel.UpdateDialog(ProjectSettingsPanel.java:247) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.projectmanager.ProjectManagerView.propertyChange(ProjectManagerView.java:392) ~[filemanager.jar:?]
	at java.beans.PropertyChangeSupport.fire(PropertyChangeSupport.java:343) ~[?:?]
	at java.beans.PropertyChangeSupport.firePropertyChange(PropertyChangeSupport.java:335) ~[?:?]
	at java.beans.PropertyChangeSupport.firePropertyChange(PropertyChangeSupport.java:268) ~[?:?]
	at com.st.microxplorer.util.MXPropertyChangeSupport.firePropertyChange(MXPropertyChangeSupport.java:54) ~[STM32CubeMX.jar:?]
	at com.st.microxplorer.mxsystem.MxSystem.closeConfig(MxSystem.java:899) ~[STM32CubeMX.jar:?]
	at com.st.microxplorer.maingui.MainPanel.closeConfig(MainPanel.java:792) ~[STM32CubeMX.jar:?]
	at com.st.microxplorer.plugins.filemanager.engine.OpenFileManager.loadConfigurationFile(OpenFileManager.java:288) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.filemanager.engine.MainFileManager.userLoadConfig(MainFileManager.java:364) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.filemanager.engine.MainFileManager.userLoadConfig(MainFileManager.java:342) ~[filemanager.jar:?]
	at com.st.microxplorer.plugins.filemanager.FileManagerView.getSpecificTask(FileManagerView.java:264) ~[filemanager.jar:?]
	at com.st.stm32cube.common.mx.editor.CubeMxEditor.getMxTabbedPaneInstance(CubeMxEditor.java:1198) ~[com.st.stm32cube.common.mx_6.15.0.202507011659/:?]
	at com.st.stm32cube.common.mx.editor.CubeMxEditor$12$1.createSwingComponent(CubeMxEditor.java:1068) ~[com.st.stm32cube.common.mx_6.15.0.202507011659/:?]
	at com.st.stm32cube.common.mx.oss.core.awtswtbridge.EmbeddedSwingComposite.doComponentCreation(EmbeddedSwingComposite.java:492) ~[com.st.stm32cube.common.mx.oss_6.15.0.202507011659/:?]
	at com.st.stm32cube.common.mx.oss.core.awtswtbridge.EmbeddedSwingComposite$4.run(EmbeddedSwingComposite.java:291) ~[com.st.stm32cube.common.mx.oss_6.15.0.202507011659/:?]
	at com.st.stm32cube.common.mx.oss.core.awtswtbridge.AwtEnvironment$2.run(AwtEnvironment.java:166) ~[com.st.stm32cube.common.mx.oss_6.15.0.202507011659/:?]
	at java.awt.event.InvocationEvent.dispatch(InvocationEvent.java:318) ~[?:?]
	at java.awt.EventQueue.dispatchEventImpl(EventQueue.java:773) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:720) ~[?:?]
	at java.awt.EventQueue$4.run(EventQueue.java:714) ~[?:?]
	at java.security.AccessController.doPrivileged(AccessController.java:400) ~[?:?]
	at java.security.ProtectionDomain$JavaSecurityAccessImpl.doIntersectionPrivilege(ProtectionDomain.java:87) ~[?:?]
	at java.awt.EventQueue.dispatchEvent(EventQueue.java:742) ~[?:?]
	at java.awt.EventDispatchThread.pumpOneEventForFilters(EventDispatchThread.java:203) ~[?:?]
	at java.awt.EventDispatchThread.pumpEventsForFilter(EventDispatchThread.java:124) ~[?:?]
	at java.awt.EventDispatchThread.pumpEventsForHierarchy(EventDispatchThread.java:113) ~[?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:109) ~[?:?]
	at java.awt.EventDispatchThread.pumpEvents(EventDispatchThread.java:101) ~[?:?]
	at java.awt.EventDispatchThread.run(EventDispatchThread.java:90) ~[?:?]
2025-07-09 14:50:32,899 [WARN] ThirdParty:871 - waiting for thirdparty lock release [close project]
2025-07-09 14:50:32,899 [INFO] ThirdParty:873 - entering critical section [close project]
2025-07-09 14:50:32,899 [INFO] ThirdParty:883 - exiting critical section [close project]
2025-07-09 14:50:32,901 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-09 14:50:32,909 [INFO] UtilMem:75 - Begin LoadConfig() Used Memory: 691548200 Bytes (1073741824)
2025-07-09 14:50:32,909 [INFO] MicroXplorer:468 - Change Database Path : 
2025-07-09 14:50:32,909 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-09 14:50:32,909 [INFO] OpenFileManager:355 - Change cursor
2025-07-09 14:50:32,967 [INFO] Mcu:2029 - Initializing MCU STM32F303CCTx STM32F303CCTx STM32F303CCT6
2025-07-09 14:50:33,841 [INFO] Context:786 - Trying to add GPIOservice into a context  which must be forbidden
2025-07-09 14:50:34,108 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) Unable to find key (Mcu.ThirdPartyNb) in loaded IOC file
2025-07-09 14:50:34,108 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) Unable to find key (Mcu.PinsNb) in loaded IOC file
2025-07-09 14:50:34,108 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) Pin0 (VP_RIF_VS_RIF1) cannot be retrieved for this MCU
2025-07-09 14:50:34,108 [INFO] ImportTextPane:234 - (OptionalMessage_ERROR) Unable to find key (Mcu.IPNb) in loaded IOC file
2025-07-09 14:50:34,130 [INFO] RtosManager:558 - Registered RTOS mode: class=CMSIS, group=RTOS, mode=CMSIS_V1, owner=FREERTOS
2025-07-09 14:50:34,130 [INFO] RtosManager:558 - Registered RTOS mode: class=CMSIS, group=RTOS2, mode=CMSIS_V2, owner=FREERTOS
2025-07-09 14:50:34,130 [INFO] RtosManager:558 - Registered RTOS mode: class=RTOS, group=Core, mode=CMSIS_V1, owner=FREERTOS
2025-07-09 14:50:34,130 [INFO] RtosManager:558 - Registered RTOS mode: class=RTOS, group=Core, mode=CMSIS_V2, owner=FREERTOS
2025-07-09 14:50:34,130 [WARN] ModelIntegratedComponent:184 - Missing modes for component STMicroelectronics:FreeRTOS:0.0.1:STMicroelectronics:RTOS:FreeRTOS:Core:::10.2.0:
2025-07-09 14:50:34,147 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,147 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,147 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,147 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,147 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,147 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,147 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelIntegratedComponent:63 - No mode defined for component null
2025-07-09 14:50:34,148 [WARN] ModelPack:524 - Component already loaded: STMicroelectronics:HAL Drivers:0.0.0:STMicroelectronics:Device:STMicro_Driver:XSPI:HAL::0.0.1:HAL_XSPI
2025-07-09 14:50:34,199 [INFO] ThirdPartyModel:298 - Start build external matchings
2025-07-09 14:50:34,409 [INFO] ThirdPartyModel:316 - End build external matchings
2025-07-09 14:50:34,571 [INFO] UtilMem:75 - End LoadConfig() Used Memory: 398699616 Bytes (1059061760)
2025-07-09 14:50:34,586 [WARN] ThirdParty:833 - waiting for thirdparty lock release [change project]
2025-07-09 14:50:34,586 [INFO] ThirdParty:835 - entering critical section [change project]
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-PM33A1 1.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USBPD 4.1
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-H7 3.3.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC9 1.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfSSL 5.8.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_HOST 2.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-MOTENVWB1 1.4.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-F4 1.1.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics LIBJPEG 8.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-ATR-ASTRA1 2.0.2
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-BLE1 7.1.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SMBUS 2.1.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfMQTT 1.19.2
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_DEVICE 3.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ISPU 2.1.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-WB 2.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ST60 1.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-GNSS1 7.0.1
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-F7 1.1.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-L5 2.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-TOUCHGFX 4.25.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_DEVICE 2.0.0
2025-07-09 14:50:34,586 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC6 3.1.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AI 10.1.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-STBOX1 2.0.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FreeRTOS 0.0.1
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-G0 1.1.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SAFEA1 1.2.2
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC4 3.0.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SUBG2 5.0.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-H7RS 1.1.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics STM32_WPAN 1.0.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :EmbeddedOffice I-CUBE-FS-RTOS 1.0.1
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics lwIP 2.0.3
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :Cesanta I-CUBE-Mongoose 7.13.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_HOST 1.0.0
2025-07-09 14:50:34,587 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :Infineon AIROC-Wi-Fi-Bluetooth-STM32 1.7.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-G4 2.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-WB05N 2.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics PDM2PCM 3.1.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics PDM2PCM 3.3.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfTPM 3.8.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-H7 3.4.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-DISPLAY 3.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :wolfSSL I-CUBE-wolfSSH 1.4.20
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC7 2.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-TCPP 4.2.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :RealThread X-CUBE-RT-Thread_Nano 4.1.1
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-ATR-SIGFOX1 3.2.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-NFC10 1.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-BLEMGR 4.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-EEPRMA1 5.1.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-FREERTOS 1.3.1
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics HAL Drivers 0.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics MBEDTLS 2.16.2
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-IPS 3.1.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ALS 1.0.2
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :emotas I-CUBE-CANOPEN 1.3.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics MBEDTLS 2.14.1
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :ITTIA_DB I-CUBE-ITTIADB 8.9.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :portGmbH I-Cube-SoM-uGOAL 1.1.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :quantropi X-CUBE-qispace-sdk-base 2.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ST67W61 1.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-STAIOTCFT 1.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics ThreadX 1.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-SMARTAG2 1.2.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-FLIGHT1 5.1.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-WL 2.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :SEGGER I-CUBE-embOS 1.3.1
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-ALGOBUILD 1.4.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-DPower 1.3.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-MEMS1 11.3.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics FP-SNS-MOTENV1 5.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics USB_DEVICE 1.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-AZRTOS-L4 2.0.0
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics ThreadX 0.0.2
2025-07-09 14:50:34,588 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics lwIP 2.1.2
2025-07-09 14:50:34,589 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-SFXS2LP1 4.0.0
2025-07-09 14:50:34,589 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-BLE2 3.3.0
2025-07-09 14:50:34,589 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics X-CUBE-TOF1 3.4.3
2025-07-09 14:50:34,589 [INFO] ThirdParty:1030 - CMSIS Pack Vendor :STMicroelectronics PDM2PCM 3.2.0
2025-07-09 14:50:34,589 [INFO] ThirdParty:841 - exiting critical section [change project]
2025-07-09 14:50:34,702 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-09 14:50:34,702 [INFO] PinOutPanel:1589 - setPackage(STM32F303CCTx,LQFP48)
2025-07-09 14:50:34,882 [INFO] UtilMem:75 - Before build in PCC Used Memory: 288834064 Bytes (919601152)
2025-07-09 14:50:35,013 [INFO] UtilMem:75 - After build in PCC Used Memory: 336019984 Bytes (919601152)
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,026 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,027 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,028 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,030 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-09 14:50:35,049 [INFO] CADModel:165 - CPN selected for project levelSTM32F303CCT6
2025-07-09 14:50:35,049 [INFO] CADModel:114 - Register for checkConnection events
2025-07-09 14:50:35,096 [INFO] OpenFileManager:386 - Restore cursor
2025-07-09 14:50:35,305 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 439829008 Bytes (919601152)
2025-07-09 14:50:35,337 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 461322768 Bytes (919601152)
2025-07-09 14:50:35,537 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: RCC
2025-07-09 14:50:35,537 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: NVIC
2025-07-09 14:50:35,538 [INFO] CodeGenerator:892 - code generatio: config db path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 14:50:35,638 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 14:50:35,853 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 14:50:35,867 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 14:50:35,963 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 14:50:35,966 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 14:50:36,011 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 14:50:36,096 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 14:50:36,171 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 14:50:36,174 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 14:50:36,209 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 14:50:36,215 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 14:50:36,250 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 14:50:36,269 [INFO] ApiDbMcu:532 - Load IP Config File for RCC
2025-07-09 14:50:36,274 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 14:50:36,320 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 14:50:36,323 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 14:50:36,388 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 14:50:36,543 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 72mS.
2025-07-09 14:50:36,547 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 14:50:36,590 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 14:50:36,594 [INFO] ApiDbMcu:532 - Load IP Config File for FATFS
2025-07-09 14:50:36,599 [INFO] ApiDbMcu:532 - Load IP Config File for FREERTOS
2025-07-09 14:50:36,606 [INFO] ApiDbMcu:532 - Load IP Config File for TOUCHSENSING
2025-07-09 14:50:36,610 [INFO] ApiDbMcu:532 - Load IP Config File for USB_DEVICE
2025-07-09 14:50:36,822 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 
2025-07-09 14:50:37,785 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 1238mS.
2025-07-09 14:50:37,786 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 14:50:37,786 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 14:50:38,551 [INFO] McuFinderGlobals:76 - Set McuFinderConnectedMode to true
2025-07-09 14:50:38,553 [INFO] ApiDb:448 - Save user favorites file C:\Users\<USER>\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 14:59:24,753 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 14:59:24,848 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 14:59:24,881 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp_save
2025-07-09 14:59:24,929 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp
2025-07-09 14:59:24,995 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp_save
2025-07-09 14:59:25,035 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp
2025-07-09 14:59:25,037 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp_save
2025-07-09 14:59:25,075 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp
2025-07-09 14:59:25,081 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 14:59:25,136 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 14:59:25,140 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 14:59:25,177 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 14:59:25,184 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 14:59:25,248 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 14:59:25,251 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 14:59:25,285 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 14:59:25,295 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 14:59:25,331 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 14:59:25,344 [INFO] ApiDbMcu:532 - Load IP Config File for ADC1
2025-07-09 14:59:25,345 [INFO] ApiDbMcu:532 - Load IP Config File for DAC
2025-07-09 14:59:25,351 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 14:59:25,385 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 14:59:25,388 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 14:59:25,455 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 14:59:25,609 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 68mS.
2025-07-09 14:59:25,611 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 14:59:25,662 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 14:59:26,193 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 582mS.
2025-07-09 14:59:26,193 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 14:59:26,194 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 15:15:20,560 [INFO] NvicIntPanel:101 - NVIC parent = com.st.microxplorer.plugins.ip.nvic.MultiNvicIntPanel[,0,0,450x248,layout=javax.swing.BoxLayout,alignmentX=0.0,alignmentY=0.0,border=,flags=9,maximumSize=,minimumSize=,preferredSize=]
2025-07-09 15:19:09,699 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 446576200 Bytes (989855744)
2025-07-09 15:19:09,833 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: SYS
2025-07-09 15:19:09,833 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: ADC
2025-07-09 15:19:09,834 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: RCC
2025-07-09 15:19:09,834 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: NVIC
2025-07-09 15:19:09,834 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DMA
2025-07-09 15:19:09,834 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DAC
2025-07-09 15:19:09,835 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: GPIO
2025-07-09 15:19:09,835 [INFO] CodeGenerator:892 - code generatio: config db path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 15:19:09,841 [INFO] Argument:325 - Argument : hdmavalue: null
2025-07-09 15:19:09,841 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:19:09,841 [INFO] Argument:325 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:19:09,841 [INFO] Argument:335 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:19:09,841 [INFO] Argument:325 - Argument : Initvalue: null
2025-07-09 15:19:09,842 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : Lockvalue: null
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : Lockvalue: null
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : Statevalue: null
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : Statevalue: null
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : Parentvalue: null
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : Parentvalue: null
2025-07-09 15:19:09,842 [INFO] Argument:325 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:19:09,842 [INFO] Argument:335 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:19:09,843 [INFO] Argument:325 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:19:09,843 [INFO] Argument:335 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:19:09,843 [INFO] Argument:325 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:19:09,843 [INFO] Argument:335 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:19:09,843 [INFO] Argument:325 - Argument : ErrorCodevalue: null
2025-07-09 15:19:09,843 [INFO] Argument:335 - Argument : ErrorCodevalue: null
2025-07-09 15:19:09,843 [INFO] Argument:325 - Argument : DMA_Remapvalue: null
2025-07-09 15:19:09,843 [INFO] Argument:335 - Argument : DMA_Remapvalue: null
2025-07-09 15:19:09,901 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 15:19:10,061 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 15:19:10,082 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp_save
2025-07-09 15:19:10,161 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp
2025-07-09 15:19:10,228 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp_save
2025-07-09 15:19:10,289 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp
2025-07-09 15:19:10,293 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp_save
2025-07-09 15:19:10,349 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp
2025-07-09 15:19:10,357 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 15:19:10,502 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 15:19:10,505 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 15:19:10,543 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 15:19:10,552 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 15:19:10,632 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 15:19:10,636 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 15:19:10,681 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 15:19:10,690 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 15:19:10,729 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 15:19:10,747 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 15:19:10,802 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 15:19:10,805 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 15:19:10,878 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 15:19:11,071 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 99mS.
2025-07-09 15:19:11,074 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 15:19:11,119 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 15:19:11,667 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 593mS.
2025-07-09 15:19:11,668 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 1mS.
2025-07-09 15:19:11,670 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 1mS.
2025-07-09 15:19:32,510 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 938742688 Bytes (989855744)
2025-07-09 15:19:32,766 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 983301728 Bytes (989855744)
2025-07-09 15:19:32,900 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: SYS
2025-07-09 15:19:32,900 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: ADC
2025-07-09 15:19:32,901 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: RCC
2025-07-09 15:19:32,901 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: NVIC
2025-07-09 15:19:32,901 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DMA
2025-07-09 15:19:32,901 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DAC
2025-07-09 15:19:32,901 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: GPIO
2025-07-09 15:19:32,902 [INFO] CodeGenerator:892 - code generatio: config db path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 15:19:32,907 [INFO] Argument:325 - Argument : hdmavalue: null
2025-07-09 15:19:32,907 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:19:32,907 [INFO] Argument:325 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:19:32,907 [INFO] Argument:335 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:19:32,907 [INFO] Argument:325 - Argument : Initvalue: null
2025-07-09 15:19:32,907 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:19:32,907 [INFO] Argument:325 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:19:32,908 [INFO] Argument:335 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:19:32,908 [INFO] Argument:325 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:19:32,908 [INFO] Argument:335 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:19:32,908 [INFO] Argument:325 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:19:32,908 [INFO] Argument:335 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:19:32,908 [INFO] Argument:325 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:19:32,909 [INFO] Argument:335 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:19:32,909 [INFO] Argument:325 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:19:32,909 [INFO] Argument:335 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:19:32,909 [INFO] Argument:325 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:19:32,909 [INFO] Argument:335 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:19:32,909 [INFO] Argument:325 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:19:32,909 [INFO] Argument:335 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:19:32,909 [INFO] Argument:325 - Argument : Lockvalue: null
2025-07-09 15:19:32,909 [INFO] Argument:335 - Argument : Lockvalue: null
2025-07-09 15:19:32,909 [INFO] Argument:325 - Argument : Statevalue: null
2025-07-09 15:19:32,910 [INFO] Argument:335 - Argument : Statevalue: null
2025-07-09 15:19:32,910 [INFO] Argument:325 - Argument : Parentvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:335 - Argument : Parentvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:325 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:335 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:325 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:335 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:325 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:335 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:19:32,910 [INFO] Argument:325 - Argument : ErrorCodevalue: null
2025-07-09 15:19:32,910 [INFO] Argument:335 - Argument : ErrorCodevalue: null
2025-07-09 15:19:32,910 [INFO] Argument:325 - Argument : DMA_Remapvalue: null
2025-07-09 15:19:32,911 [INFO] Argument:335 - Argument : DMA_Remapvalue: null
2025-07-09 15:19:32,955 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 15:19:32,990 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 15:19:33,003 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\gpio.c_save
2025-07-09 15:19:33,037 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\gpio.c
2025-07-09 15:19:33,039 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\gpio.h_save
2025-07-09 15:19:33,069 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\gpio.h
2025-07-09 15:19:33,104 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\adc.c_save
2025-07-09 15:19:33,159 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\adc.c
2025-07-09 15:19:33,162 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\adc.h_save
2025-07-09 15:19:33,194 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\adc.h
2025-07-09 15:19:33,200 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dac.c_save
2025-07-09 15:19:33,246 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dac.c
2025-07-09 15:19:33,249 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dac.h_save
2025-07-09 15:19:33,279 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dac.h
2025-07-09 15:19:33,285 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dma.c_save
2025-07-09 15:19:33,316 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dma.c
2025-07-09 15:19:33,319 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dma.h_save
2025-07-09 15:19:33,347 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dma.h
2025-07-09 15:19:33,352 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 15:19:33,386 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 15:19:33,390 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 15:19:33,419 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 15:19:33,426 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 15:19:33,469 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 15:19:33,473 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 15:19:33,503 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 15:19:33,508 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 15:19:33,538 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 15:19:33,551 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 15:19:33,584 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 15:19:33,587 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 15:19:33,630 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 15:19:33,758 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 54mS.
2025-07-09 15:19:33,759 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 15:19:33,811 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 15:19:34,228 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 469mS.
2025-07-09 15:19:34,229 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 1mS.
2025-07-09 15:19:34,230 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 15:29:26,368 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 679526168 Bytes (961544192)
2025-07-09 15:29:26,625 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: SYS
2025-07-09 15:29:26,625 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: ADC
2025-07-09 15:29:26,625 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: RCC
2025-07-09 15:29:26,625 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: NVIC
2025-07-09 15:29:26,625 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DMA
2025-07-09 15:29:26,626 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DAC
2025-07-09 15:29:26,626 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: GPIO
2025-07-09 15:29:26,627 [INFO] CodeGenerator:892 - code generatio: config db path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : hdmavalue: null
2025-07-09 15:29:26,639 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:29:26,639 [INFO] Argument:335 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : Initvalue: null
2025-07-09 15:29:26,639 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:29:26,639 [INFO] Argument:335 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:29:26,639 [INFO] Argument:335 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:29:26,639 [INFO] Argument:335 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:29:26,639 [INFO] Argument:335 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:29:26,639 [INFO] Argument:325 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:29:26,639 [INFO] Argument:335 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : Lockvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : Lockvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : Statevalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : Statevalue: null
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : Parentvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : Parentvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : ErrorCodevalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : ErrorCodevalue: null
2025-07-09 15:29:26,640 [INFO] Argument:325 - Argument : DMA_Remapvalue: null
2025-07-09 15:29:26,640 [INFO] Argument:335 - Argument : DMA_Remapvalue: null
2025-07-09 15:29:26,780 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 15:29:26,886 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 15:29:26,909 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\gpio.c_save
2025-07-09 15:29:26,968 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\gpio.c
2025-07-09 15:29:26,971 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\gpio.h_save
2025-07-09 15:29:27,033 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\gpio.h
2025-07-09 15:29:27,085 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\adc.c_save
2025-07-09 15:29:27,154 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\adc.c
2025-07-09 15:29:27,158 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\adc.h_save
2025-07-09 15:29:27,202 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\adc.h
2025-07-09 15:29:27,209 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dac.c_save
2025-07-09 15:29:27,258 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dac.c
2025-07-09 15:29:27,262 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dac.h_save
2025-07-09 15:29:27,295 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dac.h
2025-07-09 15:29:27,304 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dma.c_save
2025-07-09 15:29:27,339 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dma.c
2025-07-09 15:29:27,343 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dma.h_save
2025-07-09 15:29:27,383 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dma.h
2025-07-09 15:29:27,390 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 15:29:27,426 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 15:29:27,429 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 15:29:27,462 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 15:29:27,469 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 15:29:27,517 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 15:29:27,519 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 15:29:27,550 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 15:29:27,558 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 15:29:27,610 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 15:29:27,633 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 15:29:27,669 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 15:29:27,673 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 15:29:27,722 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 15:29:27,897 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 63mS.
2025-07-09 15:29:27,899 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 15:29:27,940 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 15:29:28,358 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 459mS.
2025-07-09 15:29:28,359 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 15:29:28,361 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 15:48:55,668 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 716749040 Bytes (954204160)
2025-07-09 15:48:55,987 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 762364144 Bytes (954204160)
2025-07-09 15:48:56,214 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: SYS
2025-07-09 15:48:56,214 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: ADC
2025-07-09 15:48:56,214 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: RCC
2025-07-09 15:48:56,214 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: NVIC
2025-07-09 15:48:56,214 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DMA
2025-07-09 15:48:56,214 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DAC
2025-07-09 15:48:56,214 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: GPIO
2025-07-09 15:48:56,215 [INFO] CodeGenerator:892 - code generatio: config db path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 15:48:56,218 [INFO] Argument:325 - Argument : hdmavalue: null
2025-07-09 15:48:56,219 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Initvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Lockvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : Lockvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Statevalue: null
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : Statevalue: null
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : Parentvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : Parentvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:48:56,219 [INFO] Argument:325 - Argument : ErrorCodevalue: null
2025-07-09 15:48:56,219 [INFO] Argument:335 - Argument : ErrorCodevalue: null
2025-07-09 15:48:56,220 [INFO] Argument:325 - Argument : DMA_Remapvalue: null
2025-07-09 15:48:56,220 [INFO] Argument:335 - Argument : DMA_Remapvalue: null
2025-07-09 15:48:56,261 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 15:48:56,424 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 15:48:56,443 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp_save
2025-07-09 15:48:56,524 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp
2025-07-09 15:48:56,582 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp_save
2025-07-09 15:48:56,641 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp
2025-07-09 15:48:56,643 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp_save
2025-07-09 15:48:56,693 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp
2025-07-09 15:48:56,700 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 15:48:56,855 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 15:48:56,859 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 15:48:56,895 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 15:48:56,902 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 15:48:56,968 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 15:48:56,971 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 15:48:57,022 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 15:48:57,029 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 15:48:57,066 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 15:48:57,085 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 15:48:57,134 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 15:48:57,138 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 15:48:57,204 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 15:48:57,359 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 67mS.
2025-07-09 15:48:57,361 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 15:48:57,408 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 15:48:57,826 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 465mS.
2025-07-09 15:48:57,827 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 1mS.
2025-07-09 15:48:57,829 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 15:51:43,788 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 819949208 Bytes (962592768)
2025-07-09 15:51:43,898 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: SYS
2025-07-09 15:51:43,899 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: ADC
2025-07-09 15:51:43,899 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: RCC
2025-07-09 15:51:43,899 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: NVIC
2025-07-09 15:51:43,899 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DMA
2025-07-09 15:51:43,899 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DAC
2025-07-09 15:51:43,899 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: GPIO
2025-07-09 15:51:43,900 [INFO] CodeGenerator:892 - code generatio: config db path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 15:51:43,904 [INFO] Argument:325 - Argument : hdmavalue: null
2025-07-09 15:51:43,904 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:51:43,904 [INFO] Argument:325 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:51:43,904 [INFO] Argument:335 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:51:43,904 [INFO] Argument:325 - Argument : Initvalue: null
2025-07-09 15:51:43,904 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:51:43,904 [INFO] Argument:325 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:51:43,904 [INFO] Argument:335 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:51:43,904 [INFO] Argument:325 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:51:43,904 [INFO] Argument:335 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:51:43,904 [INFO] Argument:325 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:51:43,904 [INFO] Argument:335 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : Lockvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : Lockvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : Statevalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : Statevalue: null
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : Parentvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : Parentvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : ErrorCodevalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : ErrorCodevalue: null
2025-07-09 15:51:43,905 [INFO] Argument:325 - Argument : DMA_Remapvalue: null
2025-07-09 15:51:43,905 [INFO] Argument:335 - Argument : DMA_Remapvalue: null
2025-07-09 15:51:43,950 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 15:51:43,983 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 15:51:43,996 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp_save
2025-07-09 15:51:44,027 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\gpio.tmp
2025-07-09 15:51:44,063 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp_save
2025-07-09 15:51:44,096 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma.tmp
2025-07-09 15:51:44,099 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp_save
2025-07-09 15:51:44,130 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\dma_GV.tmp
2025-07-09 15:51:44,140 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 15:51:44,176 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 15:51:44,179 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 15:51:44,209 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 15:51:44,215 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 15:51:44,263 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 15:51:44,265 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 15:51:44,294 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 15:51:44,299 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 15:51:44,330 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 15:51:44,348 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 15:51:44,386 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 15:51:44,390 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 15:51:44,442 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 15:51:44,577 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 58mS.
2025-07-09 15:51:44,579 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 15:51:44,618 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 15:51:44,937 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 358mS.
2025-07-09 15:51:44,937 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 15:51:44,938 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 15:51:51,036 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 893860200 Bytes (962592768)
2025-07-09 15:51:51,280 [INFO] UtilMem:75 - End SaveConfig() Used Memory: 368024552 Bytes (962592768)
2025-07-09 15:51:51,407 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: SYS
2025-07-09 15:51:51,407 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: ADC
2025-07-09 15:51:51,407 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: RCC
2025-07-09 15:51:51,407 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: NVIC
2025-07-09 15:51:51,407 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DMA
2025-07-09 15:51:51,407 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: DAC
2025-07-09 15:51:51,407 [INFO] BlockDiagram:2775 - set Specific Code input for plugin: GPIO
2025-07-09 15:51:51,407 [INFO] CodeGenerator:892 - code generatio: config db path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : hdmavalue: null
2025-07-09 15:51:51,410 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:51:51,410 [INFO] Argument:335 - Argument : Instancevalue: DMA1_Channel1
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : Initvalue: null
2025-07-09 15:51:51,410 [INFO] Argument:327 - 	 sub Args: 
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:51:51,410 [INFO] Argument:335 - Argument : Directionvalue: DMA_PERIPH_TO_MEMORY
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:51:51,410 [INFO] Argument:335 - Argument : PeriphIncvalue: DMA_PINC_DISABLE
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:51:51,410 [INFO] Argument:335 - Argument : MemIncvalue: DMA_MINC_ENABLE
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:51:51,410 [INFO] Argument:335 - Argument : PeriphDataAlignmentvalue: DMA_PDATAALIGN_HALFWORD
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:51:51,410 [INFO] Argument:335 - Argument : MemDataAlignmentvalue: DMA_MDATAALIGN_HALFWORD
2025-07-09 15:51:51,410 [INFO] Argument:325 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:51:51,410 [INFO] Argument:335 - Argument : Modevalue: DMA_CIRCULAR
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : Priorityvalue: DMA_PRIORITY_HIGH
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : Lockvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : Lockvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : Statevalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : Statevalue: null
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : Parentvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : Parentvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : XferCpltCallbackvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : XferHalfCpltCallbackvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : XferErrorCallbackvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : ErrorCodevalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : ErrorCodevalue: null
2025-07-09 15:51:51,411 [INFO] Argument:325 - Argument : DMA_Remapvalue: null
2025-07-09 15:51:51,411 [INFO] Argument:335 - Argument : DMA_Remapvalue: null
2025-07-09 15:51:51,447 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp_save
2025-07-09 15:51:51,476 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\license.tmp
2025-07-09 15:51:51,484 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\gpio.c_save
2025-07-09 15:51:51,514 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\gpio.c
2025-07-09 15:51:51,516 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\gpio.h_save
2025-07-09 15:51:51,546 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\gpio.h
2025-07-09 15:51:51,572 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\adc.c_save
2025-07-09 15:51:51,615 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\adc.c
2025-07-09 15:51:51,616 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\adc.h_save
2025-07-09 15:51:51,646 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\adc.h
2025-07-09 15:51:51,651 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dac.c_save
2025-07-09 15:51:51,690 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dac.c
2025-07-09 15:51:51,692 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dac.h_save
2025-07-09 15:51:51,720 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dac.h
2025-07-09 15:51:51,725 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dma.c_save
2025-07-09 15:51:51,754 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\dma.c
2025-07-09 15:51:51,756 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dma.h_save
2025-07-09 15:51:51,784 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\dma.h
2025-07-09 15:51:51,790 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c_save
2025-07-09 15:51:51,822 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_it.c
2025-07-09 15:51:51,825 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h_save
2025-07-09 15:51:51,857 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_it.h
2025-07-09 15:51:51,863 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c_save
2025-07-09 15:51:51,905 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\stm32f3xx_hal_msp.c
2025-07-09 15:51:51,909 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp_save
2025-07-09 15:51:51,937 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\MXTmpFiles\system.tmp
2025-07-09 15:51:51,943 [INFO] CodeEngine:321 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h_save
2025-07-09 15:51:51,974 [INFO] CodeEngine:345 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\stm32f3xx_hal_conf.h
2025-07-09 15:51:51,987 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h_save
2025-07-09 15:51:52,020 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Inc\main.h
2025-07-09 15:51:52,023 [INFO] CodeEngine:265 - oldGeneratedFile, C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c_save
2025-07-09 15:51:52,067 [INFO] CodeEngine:289 - Generated code: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\Core\Src\main.c
2025-07-09 15:51:52,189 [INFO] ProjectBuilder:3606 - Time for Copy HAL[1] : 51mS.
2025-07-09 15:51:52,191 [INFO] ProjectBuilder:5216 - Project Generator version: 4.7.0-B52
2025-07-09 15:51:52,226 [INFO] ConfigFileManager:1595 - The Die is : DIE422
2025-07-09 15:51:52,509 [INFO] ProjectBuilder:5496 - Time for Generating toolchain IDE Files: 318mS.
2025-07-09 15:51:52,510 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 1mS.
2025-07-09 15:51:52,511 [INFO] ProjectBuilder:3463 - Time for Copy CMSIS : 0mS.
2025-07-09 16:22:01,059 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,059 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,059 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,059 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,059 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,059 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,060 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,061 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,097 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,097 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,098 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,099 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,099 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,099 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,100 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,100 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,155 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,155 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,155 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,155 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,155 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,155 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,156 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 16:22:01,184 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,185 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,185 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,185 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,185 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,185 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,186 [INFO] ADCParametersView:116 -                SequencerNbRanks 1 : true
2025-07-09 16:22:01,186 [INFO] ADCParametersView:116 -                Rank 1 : false
2025-07-09 21:17:48,028 [ERROR] LogOutputStream:75 - [STDERR_REDIRECT] 
