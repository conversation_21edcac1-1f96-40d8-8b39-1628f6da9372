2025-07-10 03:20:42,391 [INFO] Activator:176 - 


2025-07-10 03:20:42,394 [INFO] Activator:177 - !SESSION log4j initialized
2025-07-10 03:20:46,401 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 
2025-07-10 03:20:48,445 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-07-10 03:20:48,470 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-07-10 03:20:48,470 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-07-10 03:20:48,470 [WARN] ApiDb:259 - Overriding images path with different value:  => C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-07-10 03:20:48,480 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-10 03:20:48,483 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-10 03:20:48,486 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-10 03:20:48,607 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-07-10 03:20:48,725 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-07-10 03:20:48,726 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-07-10 03:20:48,726 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-07-10 03:20:48,726 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,726 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-10 03:20:48,726 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,726 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-10 03:20:48,726 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,726 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,726 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-10 03:20:48,793 [INFO] McuFinderGlobals:63 - Set McuFinder mode to 2 (CubeIDE integrated)
2025-07-10 03:20:48,813 [INFO] MainPanel:274 - HeapMemory: 376438784
2025-07-10 03:20:48,958 [INFO] DbMcusXml:78 - Set database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-07-10 03:20:48,958 [INFO] ApiDb:274 - Set plugin database path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-07-10 03:20:48,958 [INFO] ApiDb:261 - Set plugin images path to: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-07-10 03:20:48,959 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,959 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-10 03:20:48,959 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,959 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-07-10 03:20:48,959 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,959 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-07-10 03:20:48,959 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-07-10 03:20:48,979 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-07-10 03:20:48,981 [INFO] PluginManage:196 - Search for loadable plugins [exclusion list=, ]
2025-07-10 03:20:48,984 [INFO] PluginManage:310 - Check plugin analytics
2025-07-10 03:20:49,217 [INFO] AnalyticsPlugin:253 - Accepted Software Licenses: STM32CubeMX.6.15.0
2025-07-10 03:20:49,218 [INFO] AnalyticsPlugin:255 - Accepted CMSIS Pack Licenses: 
2025-07-10 03:20:49,218 [INFO] AnalyticsPlugin:257 - Accepted Firmware Licenses: FW.G4.1.5.0,FW.H7.1.12.0
2025-07-10 03:20:49,221 [INFO] PluginManage:359 - Loaded plugin analytics (category:tool,tabindex:-1)
2025-07-10 03:20:49,221 [INFO] PluginManage:310 - Check plugin cadmodel
2025-07-10 03:20:49,228 [INFO] CADModel:105 - Init CAD model plugin
2025-07-10 03:20:49,228 [INFO] PluginManage:359 - Loaded plugin cadmodel (category:power,tabindex:5)
2025-07-10 03:20:49,228 [INFO] PluginManage:310 - Check plugin clock
2025-07-10 03:20:49,241 [INFO] PluginManage:359 - Loaded plugin clock (category:base,tabindex:2)
2025-07-10 03:20:49,241 [INFO] PluginManage:310 - Check plugin ddr
2025-07-10 03:20:49,245 [INFO] PluginManage:359 - Loaded plugin ddr (category:tool,tabindex:6)
2025-07-10 03:20:49,245 [INFO] PluginManage:310 - Check plugin filemanager
2025-07-10 03:20:49,385 [INFO] PluginManage:359 - Loaded plugin filemanager (category:base,tabindex:10)
2025-07-10 03:20:49,385 [INFO] PluginManage:310 - Check plugin ipmanager
2025-07-10 03:20:49,390 [INFO] PluginManage:359 - Loaded plugin ipmanager (category:base,tabindex:5)
2025-07-10 03:20:49,390 [INFO] PluginManage:310 - Check plugin lpbam
2025-07-10 03:20:49,397 [INFO] PluginManage:359 - Loaded plugin lpbam (category:base,tabindex:0)
2025-07-10 03:20:49,397 [INFO] PluginManage:310 - Check plugin memorymap
2025-07-10 03:20:49,411 [INFO] PluginManage:359 - Loaded plugin memorymap (category:base,tabindex:4)
2025-07-10 03:20:49,411 [INFO] PluginManage:310 - Check plugin pinoutandconfiguration
2025-07-10 03:20:49,419 [INFO] PluginManage:359 - Loaded plugin pinoutandconfiguration (category:base,tabindex:1)
2025-07-10 03:20:49,419 [INFO] PluginManage:310 - Check plugin pinoutconfig
2025-07-10 03:20:49,505 [WARN] SupportedApi:132 - Cannot load RTOS API schema: s4s-elt-must-match.1: The content of 'definitions' must match (annotation?, (simpleType | complexType)?, (unique | key | keyref)*)). A problem was found starting at: attribute.
2025-07-10 03:20:49,662 [INFO] PluginManage:359 - Loaded plugin pinoutconfig (category:base,tabindex:0)
2025-07-10 03:20:49,663 [INFO] PluginManage:310 - Check plugin power
2025-07-10 03:20:49,673 [INFO] PluginManage:359 - Loaded plugin power (category:power,tabindex:4)
2025-07-10 03:20:49,673 [INFO] PluginManage:310 - Check plugin projectmanager
2025-07-10 03:20:49,686 [INFO] PluginManage:359 - Loaded plugin projectmanager (category:projectmanager,tabindex:4)
2025-07-10 03:20:49,686 [INFO] PluginManage:310 - Check plugin rif
2025-07-10 03:20:49,700 [INFO] PluginManage:359 - Loaded plugin rif (category:base,tabindex:3)
2025-07-10 03:20:49,700 [INFO] PluginManage:310 - Check plugin thirdparty
2025-07-10 03:20:49,832 [INFO] PluginManage:359 - Loaded plugin thirdparty (category:base,tabindex:-1)
2025-07-10 03:20:49,832 [INFO] PluginManage:310 - Check plugin tools
2025-07-10 03:20:49,832 [WARN] IntegrityCheckThread:84 - waiting for thirdparty lock release [integrity check]
2025-07-10 03:20:49,832 [INFO] IntegrityCheckThread:86 - entering critical section [integrity check]
2025-07-10 03:20:49,832 [INFO] ThirdPartyUpdaterWithRetryManager:70 - Updater plugin not ready yet. [1/15]
2025-07-10 03:20:49,834 [INFO] PluginManage:359 - Loaded plugin tools (category:base,tabindex:7)
2025-07-10 03:20:49,834 [INFO] PluginManage:310 - Check plugin tutovideos
2025-07-10 03:20:50,034 [INFO] PluginManage:359 - Loaded plugin tutovideos (category:base,tabindex:-1)
2025-07-10 03:20:50,034 [INFO] PluginManage:310 - Check plugin updater
2025-07-10 03:20:50,055 [INFO] PluginManage:359 - Loaded plugin updater (category:base,tabindex:12)
2025-07-10 03:20:50,055 [INFO] PluginManage:310 - Check plugin userauth
2025-07-10 03:20:50,060 [INFO] UserAuth:118 - Init User Auth plugin
2025-07-10 03:20:50,060 [INFO] PluginManage:359 - Loaded plugin userauth (category:base,tabindex:14)
2025-07-10 03:20:50,060 [INFO] PluginManage:283 - PluginManage : Loaded plugins [18]
2025-07-10 03:20:50,222 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-10 03:20:50,276 [INFO] CADModel:165 - CPN selected for project level
2025-07-10 03:20:50,277 [INFO] CADModel:114 - Register for checkConnection events
2025-07-10 03:20:50,293 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,293 [INFO] PluginManager:220 - loadIPPluginJar : add adc
2025-07-10 03:20:50,296 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,296 [INFO] PluginManager:220 - loadIPPluginJar : add aes
2025-07-10 03:20:50,299 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,299 [INFO] PluginManager:220 - loadIPPluginJar : add can
2025-07-10 03:20:50,300 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,300 [INFO] PluginManager:220 - loadIPPluginJar : add comp
2025-07-10 03:20:50,302 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,303 [INFO] PluginManager:220 - loadIPPluginJar : add cryp
2025-07-10 03:20:50,306 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,306 [INFO] PluginManager:220 - loadIPPluginJar : add ddr_ctrl_phy
2025-07-10 03:20:50,308 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,308 [INFO] PluginManager:220 - loadIPPluginJar : add dfsdm
2025-07-10 03:20:50,312 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,313 [INFO] PluginManager:220 - loadIPPluginJar : add dma
2025-07-10 03:20:50,315 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,315 [INFO] PluginManager:220 - loadIPPluginJar : add dma3
2025-07-10 03:20:50,317 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,317 [INFO] PluginManager:220 - loadIPPluginJar : add extmemmanager
2025-07-10 03:20:50,319 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,319 [INFO] PluginManager:220 - loadIPPluginJar : add fatfs
2025-07-10 03:20:50,323 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,323 [INFO] PluginManager:220 - loadIPPluginJar : add fmc
2025-07-10 03:20:50,329 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,329 [INFO] PluginManager:220 - loadIPPluginJar : add freertos
2025-07-10 03:20:50,330 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,330 [INFO] PluginManager:220 - loadIPPluginJar : add genericplugin
2025-07-10 03:20:50,332 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,332 [INFO] PluginManager:220 - loadIPPluginJar : add gfxmmu
2025-07-10 03:20:50,335 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,335 [INFO] PluginManager:220 - loadIPPluginJar : add gic
2025-07-10 03:20:50,338 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,339 [INFO] PluginManager:220 - loadIPPluginJar : add gpio
2025-07-10 03:20:50,341 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,341 [INFO] PluginManager:220 - loadIPPluginJar : add gtzc
2025-07-10 03:20:50,343 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,343 [INFO] PluginManager:220 - loadIPPluginJar : add hash
2025-07-10 03:20:50,345 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,345 [INFO] PluginManager:220 - loadIPPluginJar : add i2c
2025-07-10 03:20:50,348 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,348 [INFO] PluginManager:220 - loadIPPluginJar : add i2s
2025-07-10 03:20:50,352 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,352 [INFO] PluginManager:220 - loadIPPluginJar : add i3c
2025-07-10 03:20:50,354 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,354 [INFO] PluginManager:220 - loadIPPluginJar : add ipddr
2025-07-10 03:20:50,360 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,360 [INFO] PluginManager:220 - loadIPPluginJar : add linkedlist
2025-07-10 03:20:50,363 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,363 [INFO] PluginManager:220 - loadIPPluginJar : add lorawan
2025-07-10 03:20:50,365 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,365 [INFO] PluginManager:220 - loadIPPluginJar : add ltdc
2025-07-10 03:20:50,369 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,369 [INFO] PluginManager:220 - loadIPPluginJar : add mdma
2025-07-10 03:20:50,372 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,372 [INFO] PluginManager:220 - loadIPPluginJar : add nvic
2025-07-10 03:20:50,374 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,374 [INFO] PluginManager:220 - loadIPPluginJar : add opamp
2025-07-10 03:20:50,376 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,376 [INFO] PluginManager:220 - loadIPPluginJar : add openamp
2025-07-10 03:20:50,378 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,379 [INFO] PluginManager:220 - loadIPPluginJar : add pdm2pcm
2025-07-10 03:20:50,385 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,386 [INFO] PluginManager:220 - loadIPPluginJar : add plateformsettings
2025-07-10 03:20:50,388 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,389 [INFO] PluginManager:220 - loadIPPluginJar : add quadspi
2025-07-10 03:20:50,391 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,391 [INFO] PluginManager:220 - loadIPPluginJar : add radio
2025-07-10 03:20:50,395 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,395 [INFO] PluginManager:220 - loadIPPluginJar : add resmgrutility
2025-07-10 03:20:50,399 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,399 [INFO] PluginManager:220 - loadIPPluginJar : add sai
2025-07-10 03:20:50,401 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,401 [INFO] PluginManager:220 - loadIPPluginJar : add spi
2025-07-10 03:20:50,406 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,406 [INFO] PluginManager:220 - loadIPPluginJar : add stm32_wpan
2025-07-10 03:20:50,408 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,408 [INFO] PluginManager:220 - loadIPPluginJar : add tim
2025-07-10 03:20:50,411 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,411 [INFO] PluginManager:220 - loadIPPluginJar : add touchsensing
2025-07-10 03:20:50,413 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,413 [INFO] PluginManager:220 - loadIPPluginJar : add tracer_emb
2025-07-10 03:20:50,415 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,415 [INFO] PluginManager:220 - loadIPPluginJar : add ts
2025-07-10 03:20:50,416 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,416 [INFO] PluginManager:220 - loadIPPluginJar : add tsc
2025-07-10 03:20:50,419 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,419 [INFO] PluginManager:220 - loadIPPluginJar : add ucpd
2025-07-10 03:20:50,421 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,421 [INFO] PluginManager:220 - loadIPPluginJar : add usart
2025-07-10 03:20:50,431 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-07-10 03:20:50,431 [INFO] PluginManager:220 - loadIPPluginJar : add usbx
2025-07-10 03:20:50,541 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,560 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-07-10 03:20:50,566 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-07-10 03:20:50,574 [INFO] CADModel:165 - CPN selected for project level
2025-07-10 03:20:50,574 [INFO] CADModel:114 - Register for checkConnection events
2025-07-10 03:20:50,574 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,574 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-10 03:20:50,744 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,747 [INFO] CADModel:165 - CPN selected for project level
2025-07-10 03:20:50,747 [INFO] CADModel:114 - Register for checkConnection events
2025-07-10 03:20:50,747 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,747 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-07-10 03:20:50,750 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,831 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,835 [INFO] DbMcusAds:53 - JSON generation date=Tue Jul 08 16:14:23 CST 2025 (1751962463506)
2025-07-10 03:20:50,835 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,867 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-10 03:20:50,919 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,921 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,921 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,922 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-07-10 03:20:50,922 [FATAL] Updater:351 - Updater called before beeing initialized
2025-07-10 03:20:50,953 [ERROR] Updater:1198 - MainUpdater not yet initialized. External WinMGr cannot be set.
2025-07-10 03:20:50,955 [INFO] Updater:1134 - Updater Version found : 6.15.0
2025-07-10 03:20:50,969 [INFO] ApplicationProperties:184 - Using Application install path: C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-07-10 03:20:51,353 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-10 03:20:51,353 [INFO] MainUpdater:289 - Updater Check For Update Now.
2025-07-10 03:20:51,354 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-10 03:20:51,362 [INFO] McuFinderGlobals:63 - Set McuFinder mode to 2 (CubeIDE integrated)
2025-07-10 03:20:51,363 [INFO] UserAuth:487 - Internet connection configuration mode: 1
2025-07-10 03:20:51,388 [INFO] JxBrowserEngine:152 - Initiate JxBrowser Engine with user profile folder
2025-07-10 03:20:51,499 [INFO] CheckServerUpdateThread:120 - End of CheckServer Thread
2025-07-10 03:20:51,566 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-10 03:20:51,567 [INFO] MainUpdater:2872 - connection check result : 10
2025-07-10 03:20:51,639 [INFO] MicroXplorer:468 - Change Database Path : 
2025-07-10 03:20:51,639 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-10 03:20:51,644 [WARN] ThirdParty:871 - waiting for thirdparty lock release [close project]
2025-07-10 03:20:52,205 [INFO] WebApp:169 - Instantiating new browser for Auth
2025-07-10 03:20:52,857 [INFO] WebApp:463 - Apply proxy settings
2025-07-10 03:20:52,857 [INFO] WebApp:548 - Chromium requires no authentication
2025-07-10 03:20:52,873 [INFO] WebApp:491 - Direct internet connection detected
2025-07-10 03:20:52,904 [INFO] WebApp:900 - Register for checkConnection events
2025-07-10 03:20:52,904 [INFO] WebApp:463 - Apply proxy settings
2025-07-10 03:20:52,904 [INFO] WebApp:548 - Chromium requires no authentication
2025-07-10 03:20:52,905 [INFO] WebApp:491 - Direct internet connection detected
2025-07-10 03:20:53,006 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENVWB1.1.4.0
2025-07-10 03:20:53,013 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-ASTRA1.2.0.2
2025-07-10 03:20:53,024 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SMBUS.2.1.0
2025-07-10 03:20:53,027 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ST60.1.0.0
2025-07-10 03:20:53,047 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F7.1.1.0
2025-07-10 03:20:53,064 [WARN] PackLoader:240 - Cannot read IP mode file for Infineon.AIROC-Wi-Fi-Bluetooth-STM32.1.7.0
2025-07-10 03:20:53,085 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7.3.4.0
2025-07-10 03:20:53,092 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DISPLAY.3.0.0
2025-07-10 03:20:53,102 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC10.1.0.0
2025-07-10 03:20:53,110 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLEMGR.4.0.0
2025-07-10 03:20:53,115 [WARN] PackLoader:240 - Cannot read IP mode file for emotas.I-CUBE-CANOPEN.1.3.0
2025-07-10 03:20:53,119 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : Cortex-A Device cause : null
2025-07-10 03:20:53,130 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-10 03:20:53,130 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-10 03:20:53,130 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : Cortex-A Device cause : null
2025-07-10 03:20:53,133 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-SMARTAG2.1.2.0
2025-07-10 03:20:53,136 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-FLIGHT1.5.1.0
2025-07-10 03:20:53,143 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-10 03:20:53,144 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-10 03:20:53,145 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-10 03:20:53,146 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-10 03:20:53,146 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 1 : Invalid condition id : UX_CORESTACK_Condition cause : null
2025-07-10 03:20:53,148 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WL.2.0.0
2025-07-10 03:20:53,151 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENV1.5.0.0
2025-07-10 03:20:53,157 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE2.3.3.0
2025-07-10 03:20:53,161 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC9.1.0.0
2025-07-10 03:20:53,165 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSL.5.8.0
2025-07-10 03:20:53,168 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-BLE1.7.1.0
2025-07-10 03:20:53,171 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfMQTT.1.19.2
2025-07-10 03:20:53,174 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AI.10.1.0
2025-07-10 03:20:53,181 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G0.1.1.0
2025-07-10 03:20:53,187 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SAFEA1.1.2.2
2025-07-10 03:20:53,191 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC4.3.0.0
2025-07-10 03:20:53,202 [WARN] PackLoader:240 - Cannot read IP mode file for EmbeddedOffice.I-CUBE-FS-RTOS.1.0.1
2025-07-10 03:20:53,207 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-WB05N.2.0.0
2025-07-10 03:20:53,211 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfTPM.3.8.0
2025-07-10 03:20:53,215 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TCPP.4.2.0
2025-07-10 03:20:53,220 [WARN] PackLoader:240 - Cannot read IP mode file for RealThread.X-CUBE-RT-Thread_Nano.4.1.1
2025-07-10 03:20:53,224 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-SIGFOX1.3.2.0
2025-07-10 03:20:53,227 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-EEPRMA1.5.1.0
2025-07-10 03:20:53,242 [WARN] PackLoader:240 - Cannot read IP mode file for ITTIA_DB.I-CUBE-ITTIADB.8.9.0
2025-07-10 03:20:53,247 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ST67W61.1.0.0
2025-07-10 03:20:53,260 [INFO] WebApp:225 - Starting web application
2025-07-10 03:20:53,261 [INFO] WebApp:593 - Web application path used C:\ST\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\db\plugins\mcufinder\reactClient1\index.html
2025-07-10 03:20:53,270 [WARN] PackLoader:240 - Cannot read IP mode file for SEGGER.I-CUBE-embOS.1.3.1
2025-07-10 03:20:53,292 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0
2025-07-10 03:20:53,313 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-MEMS1.11.3.0
2025-07-10 03:20:53,377 [INFO] UserAuth:487 - Internet connection configuration mode: 1
2025-07-10 03:20:53,381 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-PM33A1.1.0.0
2025-07-10 03:20:53,388 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F4.1.1.0
2025-07-10 03:20:53,392 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ISPU.2.1.0
2025-07-10 03:20:53,399 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L5.2.0.0
2025-07-10 03:20:53,403 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC6.3.1.0
2025-07-10 03:20:53,407 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-FREERTOS.1.3.1
2025-07-10 03:20:53,409 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-STAIOTCFT.1.0.0
2025-07-10 03:20:53,412 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-DPower.1.3.0
2025-07-10 03:20:53,420 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-10 03:20:53,421 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-L4.2.0.0
2025-07-10 03:20:53,421 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-10 03:20:53,421 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-10 03:20:53,421 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : LAN8742 Phy interface Condition cause : null
2025-07-10 03:20:53,425 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SFXS2LP1.4.0.0
2025-07-10 03:20:53,434 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7.3.3.0
2025-07-10 03:20:53,441 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-10 03:20:53,441 [WARN] ConditionMgr:438 - getConditionDescription Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-10 03:20:53,442 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-WB.2.0.0
2025-07-10 03:20:53,442 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-10 03:20:53,442 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-10 03:20:53,442 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-10 03:20:53,443 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-10 03:20:53,443 [WARN] ConditionMgr:1044 - genDependencies : Invalid condition id : UX DEVICE CLASS RTOS Condition cause : null
2025-07-10 03:20:53,447 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-GNSS1.7.0.1
2025-07-10 03:20:53,452 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOUCHGFX.4.25.0
2025-07-10 03:20:53,455 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-STBOX1.2.0.0
2025-07-10 03:20:53,461 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SUBG2.5.0.0
2025-07-10 03:20:53,473 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-H7RS.1.1.0
2025-07-10 03:20:53,477 [WARN] PackLoader:240 - Cannot read IP mode file for Cesanta.I-CUBE-Mongoose.7.13.0
2025-07-10 03:20:53,486 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-G4.2.0.0
2025-07-10 03:20:53,490 [WARN] PackLoader:240 - Cannot read IP mode file for wolfSSL.I-CUBE-wolfSSH.1.4.20
2025-07-10 03:20:53,499 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-NFC7.2.0.0
2025-07-10 03:20:53,505 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-IPS.3.1.0
2025-07-10 03:20:53,510 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ALS.1.0.2
2025-07-10 03:20:53,514 [WARN] PackLoader:240 - Cannot read IP mode file for portGmbH.I-Cube-SoM-uGOAL.1.1.0
2025-07-10 03:20:53,519 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-TOF1.3.4.3
2025-07-10 03:20:53,523 [INFO] ThirdParty:978 - Integrity check success = true
2025-07-10 03:20:53,523 [INFO] IntegrityCheckThread:100 - exiting critical section [integrity check]
2025-07-10 03:20:53,523 [INFO] IntegrityCheckThread:103 - End integrity checks thread
2025-07-10 03:20:53,523 [INFO] ThirdParty:873 - entering critical section [close project]
2025-07-10 03:20:53,524 [INFO] ThirdParty:883 - exiting critical section [close project]
2025-07-10 03:20:53,527 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-07-10 03:20:53,528 [INFO] UtilMem:75 - Begin LoadConfig() Used Memory: 253126224 Bytes (489684992)
2025-07-10 03:20:53,529 [INFO] MicroXplorer:468 - Change Database Path : 
2025-07-10 03:20:53,529 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-07-10 03:20:53,529 [INFO] OpenFileManager:355 - Change cursor
2025-07-10 03:20:53,552 [INFO] Mcu:2029 - Initializing MCU STM32F303C(B-C)Tx STM32F303CCTx STM32F303CCT6
