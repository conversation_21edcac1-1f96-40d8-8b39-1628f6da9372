# 语音采集输出系统使用说明

## 快速开始

### 1. 硬件连接
```
咪头 -> 放大器 -> PA0 (ADC1_IN1)
PA4 (DAC_OUT1) -> 功放 -> 扬声器
```

### 2. 编译和烧录
1. 在STM32CubeIDE中打开项目
2. 编译项目 (Ctrl+B)
3. 烧录到STM32F303CCT6 (Ctrl+F11)

### 3. 测试
- 对着咪头说话
- 应该能从扬声器听到放大的声音

## 参数调整

### 增益调节
在 `Core/Inc/audio_process.h` 中修改：
```c
#define AUDIO_GAIN_DEFAULT     2.0f    // 改为您需要的增益值
```

### 噪声门限
在 `Core/Inc/audio_process.h` 中修改：
```c
#define AUDIO_NOISE_GATE       50      // 改为您需要的门限值
```

## 常见问题

### Q: 没有声音输出
A: 检查以下项目：
1. 功放是否正常工作
2. PA4引脚连接是否正确
3. DAC配置是否正确

### Q: 声音太小
A: 增加增益值：
```c
#define AUDIO_GAIN_DEFAULT     3.0f    // 增加到3倍
```

### Q: 噪声太大
A: 增加噪声门限：
```c
#define AUDIO_NOISE_GATE       100     // 增加到100
```

### Q: 声音失真
A: 降低增益值：
```c
#define AUDIO_GAIN_DEFAULT     1.5f    // 降低到1.5倍
```

## 扩展功能

### 添加更多音频处理
在 `Core/Src/audio_process.c` 的 `AudioProcess_Process` 函数中添加：
```c
void AudioProcess_Process(uint16_t* input, uint16_t* output, uint16_t length)
{
    // 复制数据
    for(uint16_t i = 0; i < length; i++) {
        output[i] = input[i];
    }
    
    // 添加您的处理代码
    // 例如：均衡器、混响等
    
    // 应用噪声门限
    AudioProcess_ApplyNoiseGate(output, length, AUDIO_NOISE_GATE);
    
    // 应用增益
    AudioProcess_ApplyGain(output, length, AUDIO_GAIN_DEFAULT);
}
```

### 添加音量控制
可以通过修改增益来实现音量控制：
```c
// 在main.c中添加音量控制变量
float volume = 1.0f;  // 0.0 - 2.0

// 在DMA回调中使用
AudioProcess_ApplyGain(&dac_buffer[half_size], half_size, volume);
``` 