# STM32F303CCT6 语音采集输出系统

## 项目概述

这是一个基于STM32F303CCT6单片机的语音采集输出系统，实现了ADC采集语音信号并通过DAC输出到功放的完整功能。

## 硬件配置

- **MCU**: STM32F303CCT6
- **ADC**: PA0引脚 (ADC1_IN1) - 连接咪头放大器
- **DAC**: PA4引脚 (DAC_OUT1) - 连接功放
- **DMA**: 启用DMA模式，优先级为High，模式为Circular
- **定时器**: TIM6用于DAC触发

## 功能特性

### 1. 音频采集
- 12位ADC分辨率
- 连续转换模式
- DMA传输
- PA0引脚采集语音信号

### 2. 音频处理
- **增益调节**: 可调节音频增益
- **噪声门限**: 抑制背景噪声
- **实时处理**: 低延迟音频处理

### 3. 音频输出
- 12位DAC分辨率
- DMA传输
- PA4引脚输出到功放

## 文件结构

```
Core/
├── Inc/
│   ├── audio_process.h      # 音频处理头文件
│   ├── adc.h               # ADC配置头文件
│   ├── dac.h               # DAC配置头文件
│   └── ...
├── Src/
│   ├── audio_process.c      # 音频处理实现
│   ├── adc.c               # ADC配置实现
│   ├── dac.c               # DAC配置实现
│   ├── main.c              # 主程序
│   └── ...
```

## 音频处理算法

### 1. 增益调节
- 默认增益: 2.0倍
- 可调节范围: 0.1 - 10.0倍
- 防止音频过载

### 2. 噪声门限
- 默认门限: 50
- 低于门限的音频被静音
- 有效抑制背景噪声

### 3. 实时处理
- 低延迟处理
- DMA传输确保实时性
- 双缓冲机制

## 使用方法

### 1. 编译和烧录
1. 使用STM32CubeIDE打开项目
2. 编译项目
3. 烧录到STM32F303CCT6

### 2. 硬件连接
- 将咪头连接到PA0引脚（通过放大器）
- 将功放连接到PA4引脚
- 确保电源供应稳定

### 3. 参数调整
可以通过修改以下参数来调整音频处理效果：

```c
// 在audio_process.h中调整
#define AUDIO_GAIN_DEFAULT     2.0f    // 默认增益
#define AUDIO_NOISE_GATE       50      // 噪声门限
```

## 性能指标

- **ADC分辨率**: 12位
- **DAC分辨率**: 12位
- **延迟**: < 1ms
- **处理能力**: 实时处理
- **内存使用**: ~2KB RAM
- **CPU占用**: < 10%

## 故障排除

### 1. 没有音频输出
- 检查DAC连接
- 确认DMA配置正确
- 检查定时器配置

### 2. 音频失真
- 调整AGC参数
- 检查压缩器设置
- 确认ADC/DAC配置

### 3. 噪声过大
- 调整噪声门限
- 检查硬件连接
- 确认电源稳定性

## 扩展功能

### 1. 添加更多音频效果
- 均衡器
- 混响
- 回声消除

### 2. 增加通信功能
- UART通信
- USB音频
- 蓝牙音频

### 3. 添加存储功能
- SD卡录音
- Flash存储
- 音频回放

## 注意事项

1. 确保电源供应稳定，避免音频失真
2. 合理设置增益参数，避免过载
3. 定期检查硬件连接
4. 根据实际应用调整处理参数

## 许可证

本项目采用MIT许可证，详见LICENSE文件。 