21:10:56 **** Incremental Build of configuration Debug for project WDHW ****
make -j20 all 
arm-none-eabi-gcc "../Core/Src/main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F303xC -c -I../Core/Inc -I../Drivers/STM32F3xx_HAL_Driver/Inc/Legacy -I../Drivers/STM32F3xx_HAL_Driver/Inc -I../Drivers/CMSIS/Device/ST/STM32F3xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/main.d" -MT"Core/Src/main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/main.o"
../Core/Src/main.c: In function 'main':
../Core/Src/main.c:24:3: warning: implicit declaration of function 'SystemClock_Config'; did you mean 'SysTick_Config'? [-Wimplicit-function-declaration]
   24 |   SystemClock_Config();
      |   ^~~~~~~~~~~~~~~~~~
      |   SysTick_Config
../Core/Src/main.c: At top level:
../Core/Src/main.c:55:6: warning: conflicting types for 'SystemClock_Config'; have 'void(void)'
   55 | void SystemClock_Config(void)
      |      ^~~~~~~~~~~~~~~~~~
../Core/Src/main.c:24:3: note: previous implicit declaration of 'SystemClock_Config' with type 'void(void)'
   24 |   SystemClock_Config();
      |   ^~~~~~~~~~~~~~~~~~
arm-none-eabi-gcc -o "WDHW.elf" @"objects.list"   -mcpu=cortex-m4 -T"C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\STM32F303CCTX_FLASH.ld" --specs=nosys.specs -Wl,-Map="WDHW.map" -Wl,--gc-sections -static --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -Wl,--start-group -lc -lm -Wl,--end-group
Finished building target: WDHW.elf
 
arm-none-eabi-size  WDHW.elf 
arm-none-eabi-objdump -h -S WDHW.elf  > "WDHW.list"
arm-none-eabi-objcopy  -O binary WDHW.elf  "WDHW.bin"
   text	   data	    bss	    dec	    hex	filename
  15388	     12	   5844	  21244	   52fc	WDHW.elf
Finished building: default.size.stdout
 
Finished building: WDHW.bin
 
Finished building: WDHW.list
 
21:10:59 **** Incremental Build of configuration Debug for project WDHW ****
make -j20 all 
arm-none-eabi-size  WDHW.elf 
   text	   data	    bss	    dec	    hex	filename
  15388	     12	   5844	  21244	   52fc	WDHW.elf
Finished building: default.size.stdout
 
21:13:47 **** Incremental Build of configuration Debug for project WDHW ****
make -j20 all 
arm-none-eabi-gcc "../Core/Src/main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F303xC -c -I../Core/Inc -I../Drivers/STM32F3xx_HAL_Driver/Inc/Legacy -I../Drivers/STM32F3xx_HAL_Driver/Inc -I../Drivers/CMSIS/Device/ST/STM32F3xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/main.d" -MT"Core/Src/main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/main.o"
../Core/Src/main.c: In function 'main':
../Core/Src/main.c:24:3: warning: implicit declaration of function 'SystemClock_Config'; did you mean 'SysTick_Config'? [-Wimplicit-function-declaration]
   24 |   SystemClock_Config();
      |   ^~~~~~~~~~~~~~~~~~
      |   SysTick_Config
../Core/Src/main.c: At top level:
../Core/Src/main.c:55:6: warning: conflicting types for 'SystemClock_Config'; have 'void(void)'
   55 | void SystemClock_Config(void)
      |      ^~~~~~~~~~~~~~~~~~
../Core/Src/main.c:24:3: note: previous implicit declaration of 'SystemClock_Config' with type 'void(void)'
   24 |   SystemClock_Config();
      |   ^~~~~~~~~~~~~~~~~~
arm-none-eabi-gcc -o "WDHW.elf" @"objects.list"   -mcpu=cortex-m4 -T"C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\WDHW\STM32F303CCTX_FLASH.ld" --specs=nosys.specs -Wl,-Map="WDHW.map" -Wl,--gc-sections -static --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -Wl,--start-group -lc -lm -Wl,--end-group
Finished building target: WDHW.elf
 
arm-none-eabi-size  WDHW.elf 
arm-none-eabi-objdump -h -S WDHW.elf  > "WDHW.list"
arm-none-eabi-objcopy  -O binary WDHW.elf  "WDHW.bin"
   text	   data	    bss	    dec	    hex	filename
  15388	     12	   5844	  21244	   52fc	WDHW.elf
Finished building: default.size.stdout
 
Finished building: WDHW.bin
 
Finished building: WDHW.list
 
21:14:01 **** Incremental Build of configuration Debug for project WDHW ****
make -j20 all 
arm-none-eabi-size  WDHW.elf 
   text	   data	    bss	    dec	    hex	filename
  15388	     12	   5844	  21244	   52fc	WDHW.elf
Finished building: default.size.stdout
 
