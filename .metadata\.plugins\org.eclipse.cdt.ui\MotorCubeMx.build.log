16:25:40 **** Incremental Build of configuration Debug for project MotorCubeMx ****
make -j20 all 
arm-none-eabi-gcc "../User/MotorControl.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"User/MotorControl.d" -MT"User/MotorControl.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "User/MotorControl.o"
arm-none-eabi-gcc "../User/cmd.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"User/cmd.d" -MT"User/cmd.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "User/cmd.o"
arm-none-eabi-gcc "../User/mc_Encoder.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"User/mc_Encoder.d" -MT"User/mc_Encoder.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "User/mc_Encoder.o"
arm-none-eabi-gcc "../User/mc_Hall.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"User/mc_Hall.d" -MT"User/mc_Hall.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "User/mc_Hall.o"
arm-none-eabi-gcc "../User/mc_Lib.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"User/mc_Lib.d" -MT"User/mc_Lib.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "User/mc_Lib.o"
arm-none-eabi-gcc "../SI/G431CubeMX.si4project/Backup/MotorControl(4568).c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"SI/G431CubeMX.si4project/Backup/MotorControl(4568).d" -MT"SI/G431CubeMX.si4project/Backup/MotorControl(4568).o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "SI/G431CubeMX.si4project/Backup/MotorControl(4568).o"
arm-none-eabi-gcc "../SI/G431CubeMX.si4project/Backup/mc_Hall(1908).c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"SI/G431CubeMX.si4project/Backup/mc_Hall(1908).d" -MT"SI/G431CubeMX.si4project/Backup/mc_Hall(1908).o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "SI/G431CubeMX.si4project/Backup/mc_Hall(1908).o"
arm-none-eabi-gcc -mcpu=cortex-m4 -g3 -DDEBUG -c -x assembler-with-cpp -MMD -MP -MF"MDK-ARM/startup_stm32g431xx.d" -MT"MDK-ARM/startup_stm32g431xx.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "MDK-ARM/startup_stm32g431xx.o" "../MDK-ARM/startup_stm32g431xx.s"
arm-none-eabi-gcc "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.d" -MT"Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.o"
../User/MotorControl.c:2:10: fatal error: main.h: No such file or directory
    2 | #include "main.h"
      |          ^~~~~~~~
compilation terminated.
arm-none-eabi-gcc "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.d" -MT"Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.o"
arm-none-eabi-gcc "../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32G431xx -c -I../../Core/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc -I../../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy -I../../Drivers/CMSIS/Device/ST/STM32G4xx/Include -I../../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.d" -MT"Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.o"
../User/mc_Encoder.c:42:10: fatal error: main.h: No such file or directory
   42 | #include "main.h"
      |          ^~~~~~~~
compilation terminated.
make: *** [User/subdir.mk:31: User/MotorControl.o] Error 1
make: *** Waiting for unfinished jobs....
../User/mc_Hall.c:1:10: fatal error: main.h: No such file or directory
    1 | #include "main.h"
      |          ^~~~~~~~
compilation terminated.
make: *** [User/subdir.mk:31: User/mc_Encoder.o] Error 1
../User/mc_Lib.c:42:10: fatal error: main.h: No such file or directory
   42 | #include "main.h"
      |          ^~~~~~~~
compilation terminated.
../User/cmd.c:2:10: fatal error: main.h: No such file or directory
    2 | #include "main.h"
      |          ^~~~~~~~
compilation terminated.
make: *** [User/subdir.mk:31: User/mc_Hall.o] Error 1
../SI/G431CubeMX.si4project/Backup/MotorControl(4568).c:2:10: fatal error: main.h: No such file or directory
    2 | #include "main.h"
      |          ^~~~~~~~
compilation terminated.
make: *** [User/subdir.mk:31: User/cmd.o] Error 1
make: *** [User/subdir.mk:31: User/mc_Lib.o] Error 1
../SI/G431CubeMX.si4project/Backup/mc_Hall(1908).c:1:10: fatal error: main.h: No such file or directory
    1 | #include "main.h"
      |          ^~~~~~~~
compilation terminated.
../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.c:35:10: fatal error: stm32g4xx_hal.h: No such file or directory
   35 | #include "stm32g4xx_hal.h"
      |          ^~~~~~~~~~~~~~~~~
compilation terminated.
../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.c:297:10: fatal error: stm32g4xx_hal.h: No such file or directory
  297 | #include "stm32g4xx_hal.h"
      |          ^~~~~~~~~~~~~~~~~
compilation terminated.
make: *** [SI/G431CubeMX.si4project/Backup/subdir.mk:22: SI/G431CubeMX.si4project/Backup/MotorControl(4568).o] Error 1
../Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.c:33:10: fatal error: stm32g4xx_hal.h: No such file or directory
   33 | #include "stm32g4xx_hal.h"
      |          ^~~~~~~~~~~~~~~~~
compilation terminated.
make: *** [SI/G431CubeMX.si4project/Backup/subdir.mk:24: SI/G431CubeMX.si4project/Backup/mc_Hall(1908).o] Error 1
make: *** [Drivers/STM32G4xx_HAL_Driver/Src/subdir.mk:274: Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal.o] Error 1
make: *** [Drivers/STM32G4xx_HAL_Driver/Src/subdir.mk:274: Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc.o] Error 1
make: *** [Drivers/STM32G4xx_HAL_Driver/Src/subdir.mk:274: Drivers/STM32G4xx_HAL_Driver/Src/stm32g4xx_hal_adc_ex.o] Error 1
../MDK-ARM/startup_stm32g431xx.s: Assembler messages:
../MDK-ARM/startup_stm32g431xx.s:1: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:2: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:3: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:4: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:5: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:6: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:7: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:8: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:9: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:10: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:11: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:12: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:13: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:14: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:15: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:16: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:17: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:18: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:19: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:20: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:21: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:22: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:23: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:24: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:26: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:27: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:29: Error: bad instruction `amount of memory(in bytes)allocated for Stack'
../MDK-ARM/startup_stm32g431xx.s:30: Error: bad instruction `tailor this value to your application needs'
../MDK-ARM/startup_stm32g431xx.s:31: Error: junk at end of line, first unrecognized character is `<'
../MDK-ARM/startup_stm32g431xx.s:32: Error: junk at end of line, first unrecognized character is `<'
../MDK-ARM/startup_stm32g431xx.s:33: Error: junk at end of line, first unrecognized character is `<'
../MDK-ARM/startup_stm32g431xx.s:35: Error: bad instruction `stack_size EQU 0x400'
../MDK-ARM/startup_stm32g431xx.s:37: Error: bad instruction `area STACK,NOINIT,READWRITE,ALIGN=3'
../MDK-ARM/startup_stm32g431xx.s:38: Error: bad instruction `stack_mem SPACE Stack_Size'
../MDK-ARM/startup_stm32g431xx.s:39: Error: bad instruction `__initial_sp'
../MDK-ARM/startup_stm32g431xx.s:42: Error: junk at end of line, first unrecognized character is `<'
../MDK-ARM/startup_stm32g431xx.s:43: Error: junk at end of line, first unrecognized character is `<'
../MDK-ARM/startup_stm32g431xx.s:44: Error: junk at end of line, first unrecognized character is `<'
../MDK-ARM/startup_stm32g431xx.s:46: Error: bad instruction `heap_size EQU 0x200'
../MDK-ARM/startup_stm32g431xx.s:48: Error: bad instruction `area HEAP,NOINIT,READWRITE,ALIGN=3'
../MDK-ARM/startup_stm32g431xx.s:49: Error: bad instruction `__heap_base'
../MDK-ARM/startup_stm32g431xx.s:50: Error: bad instruction `heap_mem SPACE Heap_Size'
../MDK-ARM/startup_stm32g431xx.s:51: Error: bad instruction `__heap_limit'
../MDK-ARM/startup_stm32g431xx.s:53: Error: bad instruction `preserve8'
../MDK-ARM/startup_stm32g431xx.s:54: Error: bad instruction `thumb'
../MDK-ARM/startup_stm32g431xx.s:57: Error: bad instruction `vector Table Mapped to Address 0 at Reset'
../MDK-ARM/startup_stm32g431xx.s:58: Error: bad instruction `area RESET,DATA,READONLY'
../MDK-ARM/startup_stm32g431xx.s:59: Error: bad instruction `export __Vectors'
../MDK-ARM/startup_stm32g431xx.s:60: Error: bad instruction `export __Vectors_End'
../MDK-ARM/startup_stm32g431xx.s:61: Error: bad instruction `export __Vectors_Size'
../MDK-ARM/startup_stm32g431xx.s:63: Error: bad instruction `__vectors DCD __initial_sp'
../MDK-ARM/startup_stm32g431xx.s:63: Error: bad instruction `top of Stack'
../MDK-ARM/startup_stm32g431xx.s:64: Error: bad instruction `dcd Reset_Handler'
../MDK-ARM/startup_stm32g431xx.s:64: Error: bad instruction `reset Handler'
../MDK-ARM/startup_stm32g431xx.s:65: Error: bad instruction `dcd NMI_Handler'
../MDK-ARM/startup_stm32g431xx.s:65: Error: bad instruction `nmi Handler'
../MDK-ARM/startup_stm32g431xx.s:66: Error: bad instruction `dcd HardFault_Handler'
../MDK-ARM/startup_stm32g431xx.s:66: Error: bad instruction `hard Fault Handler'
../MDK-ARM/startup_stm32g431xx.s:67: Error: bad instruction `dcd MemManage_Handler'
../MDK-ARM/startup_stm32g431xx.s:67: Error: bad instruction `mpu Fault Handler'
../MDK-ARM/startup_stm32g431xx.s:68: Error: bad instruction `dcd BusFault_Handler'
../MDK-ARM/startup_stm32g431xx.s:68: Error: bad instruction `bus Fault Handler'
../MDK-ARM/startup_stm32g431xx.s:69: Error: bad instruction `dcd UsageFault_Handler'
../MDK-ARM/startup_stm32g431xx.s:69: Error: bad instruction `usage Fault Handler'
../MDK-ARM/startup_stm32g431xx.s:70: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:70: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:71: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:71: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:72: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:72: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:73: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:73: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:74: Error: bad instruction `dcd SVC_Handler'
../MDK-ARM/startup_stm32g431xx.s:74: Error: bad instruction `svcall Handler'
../MDK-ARM/startup_stm32g431xx.s:75: Error: bad instruction `dcd DebugMon_Handler'
../MDK-ARM/startup_stm32g431xx.s:75: Error: bad instruction `debug Monitor Handler'
../MDK-ARM/startup_stm32g431xx.s:76: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:76: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:77: Error: bad instruction `dcd PendSV_Handler'
../MDK-ARM/startup_stm32g431xx.s:77: Error: bad instruction `pendsv Handler'
../MDK-ARM/startup_stm32g431xx.s:78: Error: bad instruction `dcd SysTick_Handler'
../MDK-ARM/startup_stm32g431xx.s:78: Error: bad instruction `systick Handler'
../MDK-ARM/startup_stm32g431xx.s:80: Error: bad instruction `external Interrupts'
../MDK-ARM/startup_stm32g431xx.s:81: Error: bad instruction `dcd WWDG_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:81: Error: bad instruction `window WatchDog'
../MDK-ARM/startup_stm32g431xx.s:82: Error: bad instruction `dcd PVD_PVM_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:82: Error: bad instruction `pvd/PVM1/PVM2/PVM3/PVM4 through EXTI Line detection'
../MDK-ARM/startup_stm32g431xx.s:83: Error: bad instruction `dcd RTC_TAMP_LSECSS_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:83: Error: bad instruction `rtc, TAMP and RCC LSE_CSS through the EXTI line'
../MDK-ARM/startup_stm32g431xx.s:84: Error: bad instruction `dcd RTC_WKUP_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:84: Error: bad instruction `rtc Wakeup through the EXTI line'
../MDK-ARM/startup_stm32g431xx.s:85: Error: bad instruction `dcd FLASH_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:85: Error: bad instruction `flash'
../MDK-ARM/startup_stm32g431xx.s:86: Error: bad instruction `dcd RCC_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:86: Error: bad instruction `rcc'
../MDK-ARM/startup_stm32g431xx.s:87: Error: bad instruction `dcd EXTI0_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:87: Error: bad instruction `exti Line0'
../MDK-ARM/startup_stm32g431xx.s:88: Error: bad instruction `dcd EXTI1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:88: Error: bad instruction `exti Line1'
../MDK-ARM/startup_stm32g431xx.s:89: Error: bad instruction `dcd EXTI2_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:89: Error: bad instruction `exti Line2'
../MDK-ARM/startup_stm32g431xx.s:90: Error: bad instruction `dcd EXTI3_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:90: Error: bad instruction `exti Line3'
../MDK-ARM/startup_stm32g431xx.s:91: Error: bad instruction `dcd EXTI4_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:91: Error: bad instruction `exti Line4'
../MDK-ARM/startup_stm32g431xx.s:92: Error: bad instruction `dcd DMA1_Channel1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:92: Error: bad instruction `dma1 Channel 1'
../MDK-ARM/startup_stm32g431xx.s:93: Error: bad instruction `dcd DMA1_Channel2_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:93: Error: bad instruction `dma1 Channel 2'
../MDK-ARM/startup_stm32g431xx.s:94: Error: bad instruction `dcd DMA1_Channel3_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:94: Error: bad instruction `dma1 Channel 3'
../MDK-ARM/startup_stm32g431xx.s:95: Error: bad instruction `dcd DMA1_Channel4_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:95: Error: bad instruction `dma1 Channel 4'
../MDK-ARM/startup_stm32g431xx.s:96: Error: bad instruction `dcd DMA1_Channel5_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:96: Error: bad instruction `dma1 Channel 5'
../MDK-ARM/startup_stm32g431xx.s:97: Error: bad instruction `dcd DMA1_Channel6_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:97: Error: bad instruction `dma1 Channel 6'
../MDK-ARM/startup_stm32g431xx.s:98: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:98: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:99: Error: bad instruction `dcd ADC1_2_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:99: Error: bad instruction `adc1 and ADC2'
../MDK-ARM/startup_stm32g431xx.s:100: Error: bad instruction `dcd USB_HP_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:100: Error: bad instruction `usb Device High Priority'
../MDK-ARM/startup_stm32g431xx.s:101: Error: bad instruction `dcd USB_LP_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:101: Error: bad instruction `usb Device Low Priority'
../MDK-ARM/startup_stm32g431xx.s:102: Error: bad instruction `dcd FDCAN1_IT0_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:102: Error: bad instruction `fdcan1 interrupt line 0'
../MDK-ARM/startup_stm32g431xx.s:103: Error: bad instruction `dcd FDCAN1_IT1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:103: Error: bad instruction `fdcan1 interrupt line 1'
../MDK-ARM/startup_stm32g431xx.s:104: Error: bad instruction `dcd EXTI9_5_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:104: Error: bad instruction `external Line[9:5]s'
../MDK-ARM/startup_stm32g431xx.s:105: Error: bad instruction `dcd TIM1_BRK_TIM15_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:105: Error: bad instruction `tim1 Break,Transition error,Index error and TIM15'
../MDK-ARM/startup_stm32g431xx.s:106: Error: bad instruction `dcd TIM1_UP_TIM16_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:106: Error: bad instruction `tim1 Update and TIM16'
../MDK-ARM/startup_stm32g431xx.s:107: Error: bad instruction `dcd TIM1_TRG_COM_TIM17_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:107: Error: bad instruction `tim1 Trigger,Commutation,Direction change,Index and TIM17'
../MDK-ARM/startup_stm32g431xx.s:108: Error: bad instruction `dcd TIM1_CC_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:108: Error: bad instruction `tim1 Capture Compare'
../MDK-ARM/startup_stm32g431xx.s:109: Error: bad instruction `dcd TIM2_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:109: Error: bad instruction `tim2'
../MDK-ARM/startup_stm32g431xx.s:110: Error: bad instruction `dcd TIM3_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:110: Error: bad instruction `tim3'
../MDK-ARM/startup_stm32g431xx.s:111: Error: bad instruction `dcd TIM4_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:111: Error: bad instruction `tim4'
../MDK-ARM/startup_stm32g431xx.s:112: Error: bad instruction `dcd I2C1_EV_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:112: Error: bad instruction `i2c1 Event'
../MDK-ARM/startup_stm32g431xx.s:113: Error: bad instruction `dcd I2C1_ER_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:113: Error: bad instruction `i2c1 Error'
../MDK-ARM/startup_stm32g431xx.s:114: Error: bad instruction `dcd I2C2_EV_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:114: Error: bad instruction `i2c2 Event'
../MDK-ARM/startup_stm32g431xx.s:115: Error: bad instruction `dcd I2C2_ER_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:115: Error: bad instruction `i2c2 Error'
../MDK-ARM/startup_stm32g431xx.s:116: Error: bad instruction `dcd SPI1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:116: Error: bad instruction `spi1'
../MDK-ARM/startup_stm32g431xx.s:117: Error: bad instruction `dcd SPI2_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:117: Error: bad instruction `spi2'
../MDK-ARM/startup_stm32g431xx.s:118: Error: bad instruction `dcd USART1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:118: Error: bad instruction `usart1'
../MDK-ARM/startup_stm32g431xx.s:119: Error: bad instruction `dcd USART2_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:119: Error: bad instruction `usart2'
../MDK-ARM/startup_stm32g431xx.s:120: Error: bad instruction `dcd USART3_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:120: Error: bad instruction `usart3'
../MDK-ARM/startup_stm32g431xx.s:121: Error: bad instruction `dcd EXTI15_10_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:121: Error: bad instruction `external Line[15:10]'
../MDK-ARM/startup_stm32g431xx.s:122: Error: bad instruction `dcd RTC_Alarm_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:122: Error: bad instruction `rtc Alarm(A and B)through EXTI Line'
../MDK-ARM/startup_stm32g431xx.s:123: Error: bad instruction `dcd USBWakeUp_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:123: Error: bad instruction `usb Wakeup through EXTI line'
../MDK-ARM/startup_stm32g431xx.s:124: Error: bad instruction `dcd TIM8_BRK_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:124: Error: bad instruction `tim8 Break,Transition error and Index error Interrupt'
../MDK-ARM/startup_stm32g431xx.s:125: Error: bad instruction `dcd TIM8_UP_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:125: Error: bad instruction `tim8 Update Interrupt'
../MDK-ARM/startup_stm32g431xx.s:126: Error: bad instruction `dcd TIM8_TRG_COM_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:126: Error: bad instruction `tim8 Trigger,Commutation,Direction change and Index Interrupt'
../MDK-ARM/startup_stm32g431xx.s:127: Error: bad instruction `dcd TIM8_CC_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:127: Error: bad instruction `tim8 Capture Compare Interrupt'
../MDK-ARM/startup_stm32g431xx.s:128: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:128: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:129: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:129: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:130: Error: bad instruction `dcd LPTIM1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:130: Error: bad instruction `lp TIM1 interrupt'
../MDK-ARM/startup_stm32g431xx.s:131: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:131: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:132: Error: bad instruction `dcd SPI3_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:132: Error: bad instruction `spi3'
../MDK-ARM/startup_stm32g431xx.s:133: Error: bad instruction `dcd UART4_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:133: Error: bad instruction `uart4'
../MDK-ARM/startup_stm32g431xx.s:134: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:134: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:135: Error: bad instruction `dcd TIM6_DAC_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:135: Error: bad instruction `tim6 and DAC1&3 underrun errors'
../MDK-ARM/startup_stm32g431xx.s:136: Error: bad instruction `dcd TIM7_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:136: Error: bad instruction `tim7'
../MDK-ARM/startup_stm32g431xx.s:137: Error: bad instruction `dcd DMA2_Channel1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:137: Error: bad instruction `dma2 Channel 1'
../MDK-ARM/startup_stm32g431xx.s:138: Error: bad instruction `dcd DMA2_Channel2_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:138: Error: bad instruction `dma2 Channel 2'
../MDK-ARM/startup_stm32g431xx.s:139: Error: bad instruction `dcd DMA2_Channel3_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:139: Error: bad instruction `dma2 Channel 3'
../MDK-ARM/startup_stm32g431xx.s:140: Error: bad instruction `dcd DMA2_Channel4_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:140: Error: bad instruction `dma2 Channel 4'
../MDK-ARM/startup_stm32g431xx.s:141: Error: bad instruction `dcd DMA2_Channel5_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:141: Error: bad instruction `dma2 Channel 5'
../MDK-ARM/startup_stm32g431xx.s:142: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:142: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:143: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:143: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:144: Error: bad instruction `dcd UCPD1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:144: Error: bad instruction `ucpd1'
../MDK-ARM/startup_stm32g431xx.s:145: Error: bad instruction `dcd COMP1_2_3_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:145: Error: bad instruction `comp1, COMP2 and COMP3'
../MDK-ARM/startup_stm32g431xx.s:146: Error: bad instruction `dcd COMP4_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:146: Error: bad instruction `comp4'
../MDK-ARM/startup_stm32g431xx.s:147: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:147: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:148: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:148: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:149: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:149: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:150: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:150: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:151: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:151: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:152: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:152: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:153: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:153: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:154: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:154: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:155: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:155: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:156: Error: bad instruction `dcd CRS_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:156: Error: bad instruction `crs Interrupt'
../MDK-ARM/startup_stm32g431xx.s:157: Error: bad instruction `dcd SAI1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:157: Error: bad instruction `serial Audio Interface 1 global interrupt'
../MDK-ARM/startup_stm32g431xx.s:158: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:158: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:159: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:159: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:160: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:160: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:161: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:161: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:162: Error: bad instruction `dcd FPU_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:162: Error: bad instruction `fpu'
../MDK-ARM/startup_stm32g431xx.s:163: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:163: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:164: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:164: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:165: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:165: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:166: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:166: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:167: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:167: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:168: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:168: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:169: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:169: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:170: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:170: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:171: Error: bad instruction `dcd RNG_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:171: Error: bad instruction `rng global interrupt'
../MDK-ARM/startup_stm32g431xx.s:172: Error: bad instruction `dcd LPUART1_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:172: Error: bad instruction `lp UART 1 interrupt'
../MDK-ARM/startup_stm32g431xx.s:173: Error: bad instruction `dcd I2C3_EV_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:173: Error: bad instruction `i2c3 Event'
../MDK-ARM/startup_stm32g431xx.s:174: Error: bad instruction `dcd I2C3_ER_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:174: Error: bad instruction `i2c3 Error'
../MDK-ARM/startup_stm32g431xx.s:175: Error: bad instruction `dcd DMAMUX_OVR_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:175: Error: bad instruction `dmamux overrun global interrupt'
../MDK-ARM/startup_stm32g431xx.s:176: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:176: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:177: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:177: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:178: Error: bad instruction `dcd DMA2_Channel6_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:178: Error: bad instruction `dma2 Channel 6'
../MDK-ARM/startup_stm32g431xx.s:179: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:179: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:180: Error: bad instruction `dcd 0'
../MDK-ARM/startup_stm32g431xx.s:180: Error: bad instruction `reserved'
../MDK-ARM/startup_stm32g431xx.s:181: Error: bad instruction `dcd CORDIC_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:181: Error: bad instruction `cordic'
../MDK-ARM/startup_stm32g431xx.s:182: Error: bad instruction `dcd FMAC_IRQHandler'
../MDK-ARM/startup_stm32g431xx.s:182: Error: bad instruction `fmac'
../MDK-ARM/startup_stm32g431xx.s:184: Error: bad instruction `__vectors_end'
../MDK-ARM/startup_stm32g431xx.s:186: Error: bad instruction `__vectors_size EQU __Vectors_End-__Vectors'
../MDK-ARM/startup_stm32g431xx.s:188: Error: bad instruction `area |.text|,CODE,READONLY'
../MDK-ARM/startup_stm32g431xx.s:190: Error: bad instruction `reset handler'
../MDK-ARM/startup_stm32g431xx.s:191: Error: bad instruction `reset_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:192: Error: bad instruction `export Reset_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:193: Error: bad instruction `import SystemInit'
../MDK-ARM/startup_stm32g431xx.s:194: Error: bad instruction `import __main'
../MDK-ARM/startup_stm32g431xx.s:200: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:202: Error: bad instruction `dummy Exception Handlers(infinite loops which can be modified)'
../MDK-ARM/startup_stm32g431xx.s:204: Error: bad instruction `nmi_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:205: Error: bad instruction `export NMI_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:207: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:208: Error: bad instruction `hardfault_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:210: Error: bad instruction `export HardFault_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:212: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:213: Error: bad instruction `memmanage_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:215: Error: bad instruction `export MemManage_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:217: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:218: Error: bad instruction `busfault_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:220: Error: bad instruction `export BusFault_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:222: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:223: Error: bad instruction `usagefault_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:225: Error: bad instruction `export UsageFault_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:227: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:228: Error: bad instruction `svc_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:229: Error: bad instruction `export SVC_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:231: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:232: Error: bad instruction `debugmon_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:234: Error: bad instruction `export DebugMon_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:236: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:237: Error: bad instruction `pendsv_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:238: Error: bad instruction `export PendSV_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:240: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:241: Error: bad instruction `systick_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:242: Error: bad instruction `export SysTick_Handler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:244: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:246: Error: bad instruction `default_handler PROC'
../MDK-ARM/startup_stm32g431xx.s:248: Error: bad instruction `export WWDG_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:249: Error: bad instruction `export PVD_PVM_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:250: Error: bad instruction `export RTC_TAMP_LSECSS_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:251: Error: bad instruction `export RTC_WKUP_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:252: Error: bad instruction `export FLASH_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:253: Error: bad instruction `export RCC_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:254: Error: bad instruction `export EXTI0_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:255: Error: bad instruction `export EXTI1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:256: Error: bad instruction `export EXTI2_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:257: Error: bad instruction `export EXTI3_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:258: Error: bad instruction `export EXTI4_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:259: Error: bad instruction `export DMA1_Channel1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:260: Error: bad instruction `export DMA1_Channel2_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:261: Error: bad instruction `export DMA1_Channel3_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:262: Error: bad instruction `export DMA1_Channel4_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:263: Error: bad instruction `export DMA1_Channel5_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:264: Error: bad instruction `export DMA1_Channel6_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:265: Error: bad instruction `export ADC1_2_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:266: Error: bad instruction `export USB_HP_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:267: Error: bad instruction `export USB_LP_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:268: Error: bad instruction `export FDCAN1_IT0_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:269: Error: bad instruction `export FDCAN1_IT1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:270: Error: bad instruction `export EXTI9_5_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:271: Error: bad instruction `export TIM1_BRK_TIM15_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:272: Error: bad instruction `export TIM1_UP_TIM16_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:273: Error: bad instruction `export TIM1_TRG_COM_TIM17_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:274: Error: bad instruction `export TIM1_CC_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:275: Error: bad instruction `export TIM2_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:276: Error: bad instruction `export TIM3_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:277: Error: bad instruction `export TIM4_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:278: Error: bad instruction `export I2C1_EV_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:279: Error: bad instruction `export I2C1_ER_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:280: Error: bad instruction `export I2C2_EV_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:281: Error: bad instruction `export I2C2_ER_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:282: Error: bad instruction `export SPI1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:283: Error: bad instruction `export SPI2_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:284: Error: bad instruction `export USART1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:285: Error: bad instruction `export USART2_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:286: Error: bad instruction `export USART3_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:287: Error: bad instruction `export EXTI15_10_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:288: Error: bad instruction `export RTC_Alarm_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:289: Error: bad instruction `export USBWakeUp_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:290: Error: bad instruction `export TIM8_BRK_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:291: Error: bad instruction `export TIM8_UP_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:292: Error: bad instruction `export TIM8_TRG_COM_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:293: Error: bad instruction `export TIM8_CC_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:294: Error: bad instruction `export LPTIM1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:295: Error: bad instruction `export SPI3_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:296: Error: bad instruction `export UART4_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:297: Error: bad instruction `export TIM6_DAC_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:298: Error: bad instruction `export TIM7_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:299: Error: bad instruction `export DMA2_Channel1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:300: Error: bad instruction `export DMA2_Channel2_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:301: Error: bad instruction `export DMA2_Channel3_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:302: Error: bad instruction `export DMA2_Channel4_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:303: Error: bad instruction `export DMA2_Channel5_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:304: Error: bad instruction `export UCPD1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:305: Error: bad instruction `export COMP1_2_3_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:306: Error: bad instruction `export COMP4_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:307: Error: bad instruction `export CRS_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:308: Error: bad instruction `export SAI1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:309: Error: bad instruction `export FPU_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:310: Error: bad instruction `export RNG_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:311: Error: bad instruction `export LPUART1_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:312: Error: bad instruction `export I2C3_EV_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:313: Error: bad instruction `export I2C3_ER_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:314: Error: bad instruction `export DMAMUX_OVR_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:315: Error: bad instruction `export DMA2_Channel6_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:316: Error: bad instruction `export CORDIC_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:317: Error: bad instruction `export FMAC_IRQHandler [WEAK]'
../MDK-ARM/startup_stm32g431xx.s:319: Error: bad instruction `wwdg_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:320: Error: bad instruction `pvd_pvm_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:321: Error: bad instruction `rtc_tamp_lsecss_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:322: Error: bad instruction `rtc_wkup_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:323: Error: bad instruction `flash_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:324: Error: bad instruction `rcc_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:325: Error: bad instruction `exti0_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:326: Error: bad instruction `exti1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:327: Error: bad instruction `exti2_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:328: Error: bad instruction `exti3_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:329: Error: bad instruction `exti4_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:330: Error: bad instruction `dma1_channel1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:331: Error: bad instruction `dma1_channel2_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:332: Error: bad instruction `dma1_channel3_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:333: Error: bad instruction `dma1_channel4_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:334: Error: bad instruction `dma1_channel5_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:335: Error: bad instruction `dma1_channel6_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:336: Error: bad instruction `adc1_2_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:337: Error: bad instruction `usb_hp_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:338: Error: bad instruction `usb_lp_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:339: Error: bad instruction `fdcan1_it0_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:340: Error: bad instruction `fdcan1_it1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:341: Error: bad instruction `exti9_5_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:342: Error: bad instruction `tim1_brk_tim15_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:343: Error: bad instruction `tim1_up_tim16_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:344: Error: bad instruction `tim1_trg_com_tim17_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:345: Error: bad instruction `tim1_cc_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:346: Error: bad instruction `tim2_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:347: Error: bad instruction `tim3_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:348: Error: bad instruction `tim4_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:349: Error: bad instruction `i2c1_ev_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:350: Error: bad instruction `i2c1_er_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:351: Error: bad instruction `i2c2_ev_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:352: Error: bad instruction `i2c2_er_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:353: Error: bad instruction `spi1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:354: Error: bad instruction `spi2_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:355: Error: bad instruction `usart1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:356: Error: bad instruction `usart2_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:357: Error: bad instruction `usart3_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:358: Error: bad instruction `exti15_10_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:359: Error: bad instruction `rtc_alarm_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:360: Error: bad instruction `usbwakeup_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:361: Error: bad instruction `tim8_brk_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:362: Error: bad instruction `tim8_up_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:363: Error: bad instruction `tim8_trg_com_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:364: Error: bad instruction `tim8_cc_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:365: Error: bad instruction `lptim1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:366: Error: bad instruction `spi3_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:367: Error: bad instruction `uart4_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:368: Error: bad instruction `tim6_dac_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:369: Error: bad instruction `tim7_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:370: Error: bad instruction `dma2_channel1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:371: Error: bad instruction `dma2_channel2_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:372: Error: bad instruction `dma2_channel3_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:373: Error: bad instruction `dma2_channel4_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:374: Error: bad instruction `dma2_channel5_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:375: Error: bad instruction `ucpd1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:376: Error: bad instruction `comp1_2_3_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:377: Error: bad instruction `comp4_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:378: Error: bad instruction `crs_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:379: Error: bad instruction `sai1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:380: Error: bad instruction `fpu_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:381: Error: bad instruction `rng_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:382: Error: bad instruction `lpuart1_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:383: Error: bad instruction `i2c3_ev_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:384: Error: bad instruction `i2c3_er_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:385: Error: bad instruction `dmamux_ovr_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:386: Error: bad instruction `dma2_channel6_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:387: Error: bad instruction `cordic_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:388: Error: bad instruction `fmac_irqhandler'
../MDK-ARM/startup_stm32g431xx.s:392: Error: bad instruction `endp'
../MDK-ARM/startup_stm32g431xx.s:394: Error: bad instruction `align'
../MDK-ARM/startup_stm32g431xx.s:396: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:397: Error: bad instruction `user Stack and Heap initialization'
../MDK-ARM/startup_stm32g431xx.s:398: Error: junk at end of line, first unrecognized character is `*'
../MDK-ARM/startup_stm32g431xx.s:399: Error: bad instruction `__microlib'
../MDK-ARM/startup_stm32g431xx.s:401: Error: bad instruction `export __initial_sp'
../MDK-ARM/startup_stm32g431xx.s:402: Error: bad instruction `export __heap_base'
../MDK-ARM/startup_stm32g431xx.s:403: Error: bad instruction `export __heap_limit'
../MDK-ARM/startup_stm32g431xx.s:405: Error: bad instruction `else'
../MDK-ARM/startup_stm32g431xx.s:407: Error: bad instruction `import __use_two_region_memory'
../MDK-ARM/startup_stm32g431xx.s:408: Error: bad instruction `export __user_initial_stackheap'
../MDK-ARM/startup_stm32g431xx.s:410: Error: bad instruction `__user_initial_stackheap'
../MDK-ARM/startup_stm32g431xx.s:413: Error: syntax error -- `ldr R1,=(Stack_Mem+Stack_Size)'
../MDK-ARM/startup_stm32g431xx.s:414: Error: syntax error -- `ldr R2,=(Heap_Mem+Heap_Size)'
../MDK-ARM/startup_stm32g431xx.s:418: Error: bad instruction `align'
../MDK-ARM/startup_stm32g431xx.s:420: Error: bad instruction `endif'
../MDK-ARM/startup_stm32g431xx.s:422: Error: bad instruction `end'
make: *** [MDK-ARM/subdir.mk:19: MDK-ARM/startup_stm32g431xx.o] Error 1
"make -j20 all" terminated with exit code 2. Build might be incomplete.

16:25:41 Build Failed. 505 errors, 0 warnings. (took 1s.500ms)

