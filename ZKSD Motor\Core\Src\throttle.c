#include "throttle.h"
#include "main.h"
#include <stdio.h>

// 默认CubeMX生成的外部句柄
extern ADC_HandleTypeDef hadc1;
extern TIM_HandleTypeDef htim1;

#define ADC_RES         4095.0f
#define VREF            3.3f
#define THROTTLE_LOW    0.9f
#define THROTTLE_HIGH   4.3f

static volatile uint16_t adc_raw = 0;
float throttle_percent = 0.0f;
extern volatile uint16_t adc_dma_buf;
volatile uint16_t g_throttle_adc_value = 0;  // 全局ADC值，方便调试

// 设置三相互补PWM占空比
static void Throttle_SetPWM(float percent)
{
    uint32_t period = __HAL_TIM_GET_AUTORELOAD(&htim1);
    uint32_t ccr = (uint32_t)(period * percent / 100.0f);
    
    // 设置所有三个通道的占空比
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, ccr);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_2, ccr);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_3, ccr);
}

void Throttle_Init(void)
{
    // 停止ADC
    HAL_ADC_Stop(&hadc1);
    
    // 启动ADC DMA模式
    if (HAL_ADC_Start_DMA(&hadc1, (uint32_t*)&adc_dma_buf, 1) != HAL_OK) {
        // ADC启动失败
        while(1);
    }
    
    // 启动所有PWM通道
    if (HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1) != HAL_OK) while(1);
    if (HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_2) != HAL_OK) while(1);
    if (HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_3) != HAL_OK) while(1);
    
    // 启用主输出
    __HAL_TIM_MOE_ENABLE(&htim1);
    
    // 初始化时设置一个小的占空比
    Throttle_SetPWM(10.0f);
}

void Throttle_Loop(void)
{
    // 保存ADC值到全局变量
    g_throttle_adc_value = adc_dma_buf;
    
    // 计算油门百分比
    float percent = (adc_dma_buf / ADC_RES) * 100.0f;
    
    // 限制百分比范围
    if (percent > 100.0f) percent = 100.0f;
    if (percent < 0.0f) percent = 0.0f;
    
    // 更新全局变量
    throttle_percent = percent;
    
    // 设置PWM占空比
    Throttle_SetPWM(percent);
    
    // 调试：每1000次循环输出一次ADC值（可以通过调试器查看）
    static uint32_t debug_counter = 0;
    debug_counter++;
    if (debug_counter >= 1000) {
        debug_counter = 0;
        // 这里可以添加调试输出，比如通过串口
        // 或者设置一个断点来查看adc_dma_buf的值
    }
}

float Throttle_GetPercent(void)
{
    return throttle_percent;
}

void Throttle_SetThreshold(uint16_t low, uint16_t high)
{
    // This function is no longer used in the new implementation
}

void Throttle_SetDuty(float percent)
{
    Throttle_SetPWM(percent);
}

// 测试函数：设置固定的PWM占空比进行测试
void Throttle_TestPWM(void)
{
    static uint32_t test_counter = 0;
    static float test_duty = 0.0f;
    
    test_counter++;
    if (test_counter >= 1000) {  // 每10秒改变一次
        test_counter = 0;
        test_duty += 10.0f;
        if (test_duty > 100.0f) test_duty = 0.0f;
    }
    
    Throttle_SetPWM(test_duty);
}

// 简单测试函数：设置固定占空比
void Throttle_SetFixedDuty(float duty)
{
    Throttle_SetPWM(duty);
}

// 调试函数：检查ADC状态
uint16_t Throttle_GetADCValue(void)
{
    return g_throttle_adc_value;
} 