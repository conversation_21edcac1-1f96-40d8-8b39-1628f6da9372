<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_gG-Q4Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_gG-Q4Vz6EfCbCMAxoqNuvA" bindingContexts="_gHDKf1z6EfCbCMAxoqNuvA">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;WDHW.ioc&quot; tooltip=&quot;WDHW/WDHW.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/WDHW.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;WDHW/Core/Src/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_dac_ex.c&quot; tooltip=&quot;WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;system_stm32f3xx.c&quot; tooltip=&quot;WDHW/Core/Src/system_stm32f3xx.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Core/Src/system_stm32f3xx.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;audio_process.c&quot; tooltip=&quot;WDHW/Core/Src/audio_process.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Core/Src/audio_process.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;ZKSD Motor.ioc&quot; tooltip=&quot;ZKSD Motor/ZKSD Motor.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/ZKSD Motor.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;startup_stm32h745zitx.s&quot; tooltip=&quot;STMH_CM4/Application/Startup/startup_stm32h745zitx.s&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH_CM4/Application/Startup/startup_stm32h745zitx.s&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;startup_stm32h745zitx.s&quot; tooltip=&quot;STMH_CM7/Application/Startup/startup_stm32h745zitx.s&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH_CM7/Application/Startup/startup_stm32h745zitx.s&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;STMH_CM7/Application/User/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH_CM7/Application/User/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;system_stm32h7xx_dualcore_boot_cm4_cm7.c&quot; tooltip=&quot;STMH/Drivers/CMSIS/system_stm32h7xx_dualcore_boot_cm4_cm7.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH/Drivers/CMSIS/system_stm32h7xx_dualcore_boot_cm4_cm7.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;STMH.ioc&quot; tooltip=&quot;STMH/STMH.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH/STMH.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_dma.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dma.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dma.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm4.h&quot; tooltip=&quot;ZKSD Motor/Drivers/CMSIS/Include/core_cm4.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/CMSIS/Include/core_cm4.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_gpio.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_gpio.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_gpio.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_adc_ex.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc_ex.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc_ex.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_adc.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;MotorCubeMx/Core/Src/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/MotorCubeMx/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;MotorCubeMx.ioc&quot; tooltip=&quot;MotorCubeMx/MotorCubeMx.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/MotorCubeMx/MotorCubeMx.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;adc.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_it.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/stm32f3xx_it.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/stm32f3xx_it.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;cmsis_gcc.h&quot; tooltip=&quot;ZKSD Motor/Drivers/CMSIS/Include/cmsis_gcc.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/CMSIS/Include/cmsis_gcc.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_msp.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/stm32f3xx_hal_msp.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/stm32f3xx_hal_msp.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_cortex.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_cortex.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_cortex.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_rcc.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_rcc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_rcc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;startup_stm32f303cctx.s&quot; tooltip=&quot;ZKSD Motor/Core/Startup/startup_stm32f303cctx.s&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Startup/startup_stm32f303cctx.s&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_gG-Q4Vz6EfCbCMAxoqNuvA" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_gG-Q4lz6EfCbCMAxoqNuvA" x="445" y="-28" width="1050" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1751907777404"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_gG-Q4lz6EfCbCMAxoqNuvA" selectedElement="_gG-Q41z6EfCbCMAxoqNuvA" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_gG-Q41z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_gG-39lz6EfCbCMAxoqNuvA">
        <children xsi:type="advanced:Perspective" xmi:id="_gG-Q5Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_gG-Q5Vz6EfCbCMAxoqNuvA" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_gG-Q5Vz6EfCbCMAxoqNuvA" selectedElement="_gG-Q5lz6EfCbCMAxoqNuvA" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_gG-Q5lz6EfCbCMAxoqNuvA" elementId="topLeft" containerData="2500" selectedElement="_gG-Q51z6EfCbCMAxoqNuvA">
              <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q51z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_gHB7cVz6EfCbCMAxoqNuvA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q6Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_gHB7i1z6EfCbCMAxoqNuvA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q6Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_gHB7jFz6EfCbCMAxoqNuvA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_gG-Q6lz6EfCbCMAxoqNuvA" containerData="7500" selectedElement="_gG-Q61z6EfCbCMAxoqNuvA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_gG-Q61z6EfCbCMAxoqNuvA" containerData="7500" selectedElement="_gG-Q7Fz6EfCbCMAxoqNuvA" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q7Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_gHB7Z1z6EfCbCMAxoqNuvA"/>
                <children xsi:type="basic:PartStack" xmi:id="_gG-Q7Vz6EfCbCMAxoqNuvA" elementId="topRight" containerData="2500" selectedElement="_gG-Q7lz6EfCbCMAxoqNuvA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q7lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ContentOutline" ref="_gHB7slz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q71z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_gHB7yFz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q8Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_gHB7yVz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_gG-Q8Vz6EfCbCMAxoqNuvA" containerData="2500" selectedElement="_gG-Q8lz6EfCbCMAxoqNuvA" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_gG-Q8lz6EfCbCMAxoqNuvA" elementId="bottom" containerData="5000" selectedElement="_gG-38Fz6EfCbCMAxoqNuvA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q81z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProblemView" ref="_gHB7jVz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-Q9Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.TaskList" ref="_gHB7k1z6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-38Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView" ref="_gHB7mVz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-38Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.PropertySheet" ref="_gHB7olz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_gG-38lz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.viewMStack" containerData="5000" selectedElement="_gG-381z6EfCbCMAxoqNuvA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-381z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" ref="_gHB721z6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-39Fz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" ref="_gHCiWlz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-39Vz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" ref="_gHCiX1z6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_gG-39lz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.perspective" selectedElement="_gG-391z6EfCbCMAxoqNuvA" label="Device Configuration Tool" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_gG-391z6EfCbCMAxoqNuvA" selectedElement="_gG-3-lz6EfCbCMAxoqNuvA" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_gG-3-Fz6EfCbCMAxoqNuvA" elementId="left" containerData="1818" selectedElement="_gG-3-Vz6EfCbCMAxoqNuvA">
              <children xsi:type="advanced:Placeholder" xmi:id="_gG-3-Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_gHB7cVz6EfCbCMAxoqNuvA" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_gG-3-lz6EfCbCMAxoqNuvA" containerData="8182" selectedElement="_gG-3-1z6EfCbCMAxoqNuvA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_gG-3-1z6EfCbCMAxoqNuvA" containerData="5000" selectedElement="_gG-3_Fz6EfCbCMAxoqNuvA">
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-3_Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editorss" containerData="8733" ref="_gHB7Z1z6EfCbCMAxoqNuvA"/>
                <children xsi:type="basic:PartStack" xmi:id="_gG-3_Vz6EfCbCMAxoqNuvA" elementId="bottomRight" containerData="1267" selectedElement="_gG-3_1z6EfCbCMAxoqNuvA">
                  <tags>General</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-3_lz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.views.OutputsView" toBeRendered="false" ref="_gHCiZFz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Device Configuration Tool</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-3_1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView" ref="_gHB7mVz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_gG-4AFz6EfCbCMAxoqNuvA" elementId="topRight" toBeRendered="false" containerData="1123"/>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_gG-4AVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_gG-4Alz6EfCbCMAxoqNuvA" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.faultanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_gG-4Alz6EfCbCMAxoqNuvA" selectedElement="_gG-4CVz6EfCbCMAxoqNuvA" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_gG-4A1z6EfCbCMAxoqNuvA" containerData="2500" selectedElement="_gG-4BFz6EfCbCMAxoqNuvA" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_gG-4BFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_gG-4BVz6EfCbCMAxoqNuvA">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4BVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.DebugView" ref="_gHCiZVz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Blz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_gHB7cVz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_gG-4B1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4CFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_gHCifFz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_gG-4CVz6EfCbCMAxoqNuvA" containerData="7500" selectedElement="_gG-4GVz6EfCbCMAxoqNuvA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_gG-4Clz6EfCbCMAxoqNuvA" containerData="7500" selectedElement="_gG-4C1z6EfCbCMAxoqNuvA" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4C1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editorss" containerData="5310" ref="_gHB7Z1z6EfCbCMAxoqNuvA"/>
                <children xsi:type="basic:PartStack" xmi:id="_gG-4DFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="4690" selectedElement="_gG-4F1z6EfCbCMAxoqNuvA">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4DVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.VariableView" ref="_gHCibFz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Dlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.BreakpointView" ref="_gHCib1z6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4D1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ExpressionView" ref="_gHCiclz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4EFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_gHB7slz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4EVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_gHB7olz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Elz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_gHB7yFz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4E1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_gHCidlz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4FFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_gHCid1z6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4FVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" toBeRendered="false" ref="_gHCie1z6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Flz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.RegisterView" ref="_gHCiaFz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4F1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" ref="_gHCiglz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gG-4GFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.sfrview" ref="_gHCihVz6EfCbCMAxoqNuvA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_gG-4GVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="2500" selectedElement="_gG-4Glz6EfCbCMAxoqNuvA">
                <tags>Debug</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Glz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView" ref="_gHB7mVz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                  <tags>active</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4G1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_gHB7jFz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4HFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_gHCia1z6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4HVz6EfCbCMAxoqNuvA" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_gHCidVz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Hlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProblemView" ref="_gHB7jVz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4H1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_gHCieFz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4IFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_gHCifVz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4IVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_gHCigFz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Ilz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" toBeRendered="false" ref="_gHCigVz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gG-4I1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.MemoryView" ref="_gHCiiFz6EfCbCMAxoqNuvA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_gG-4JFz6EfCbCMAxoqNuvA" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_gG-4JVz6EfCbCMAxoqNuvA" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_gHB7Ylz6EfCbCMAxoqNuvA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_gG-4Jlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_gHB7Y1z6EfCbCMAxoqNuvA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_gG-4J1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_gHB7Zlz6EfCbCMAxoqNuvA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7Ylz6EfCbCMAxoqNuvA" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7Y1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Information Center" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;file:///C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/configuration/org.eclipse.osgi/73/0/.cp/welcome/index.html&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gHB7ZFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB7ZVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7Zlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_gHB7Z1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editorss" selectedElement="_gHB7aFz6EfCbCMAxoqNuvA">
      <children xsi:type="basic:PartStack" xmi:id="_gHB7aFz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_gHB7cFz6EfCbCMAxoqNuvA">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_gHB7aVz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;WDHW/Core/Src/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;455&quot; selectionTopPixel=&quot;200&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_gHB7bVz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="audio_process.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;audio_process.c&quot; partName=&quot;audio_process.c&quot; title=&quot;audio_process.c&quot; tooltip=&quot;WDHW/Core/Src/audio_process.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Core/Src/audio_process.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;775&quot; selectionTopPixel=&quot;280&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_gHB7blz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="system_stm32f3xx.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;system_stm32f3xx.c&quot; partName=&quot;system_stm32f3xx.c&quot; title=&quot;system_stm32f3xx.c&quot; tooltip=&quot;WDHW/Core/Src/system_stm32f3xx.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Core/Src/system_stm32f3xx.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;7099&quot; selectionTopPixel=&quot;3600&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_gHB7b1z6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="stm32f3xx_hal_dac_ex.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_dac_ex.c&quot; partName=&quot;stm32f3xx_hal_dac_ex.c&quot; title=&quot;stm32f3xx_hal_dac_ex.c&quot; tooltip=&quot;WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;5012&quot; selectionTopPixel=&quot;2240&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_gHB7cFz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="WDHW.ioc" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;WDHW.ioc&quot; partName=&quot;WDHW.ioc&quot; title=&quot;WDHW.ioc&quot; tooltip=&quot;Device Configuration Tool&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/WDHW.ioc&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>com.st.stm32cube.common.mx.startCubeMx</tags>
          <tags>active</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7cVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gHB7clz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB7hlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7i1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7jFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7jVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;350&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gHB7jlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB7kFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7k1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;350&quot; org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.priorityField=&quot;35&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gHB7lFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB7llz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7mVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gHB7mlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB7m1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7olz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gHB7o1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB7qlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7slz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gHB7s1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB7wVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7yFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB7yVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view horizontalPosition=&quot;0&quot; verticalPosition=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
      <menus xmi:id="_gHB7ylz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.views.MakeView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHB70lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.views.MakeView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHB721z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_gHCiUFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCiV1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiWlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_gHCiW1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCiXFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiX1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_gHCiYFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCiYVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiZFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.views.OutputsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
      <tags>View</tags>
      <tags>categoryTag:Device Configuration Tool</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiZVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCiZlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCiZ1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiaFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCiaVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCialz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCia1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCibFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCibVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCiblz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCib1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCicFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCicVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiclz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCic1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCidFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCidVz6EfCbCMAxoqNuvA" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCidlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCid1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCieFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCieVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCielz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCie1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCifFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCifVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCiflz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCif1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCigFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCigVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiglz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x73b0;&#x573a;&#x8868;&#x8fbe;&#x5f0f;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCig1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCihFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCihVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.sfrview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCihlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.sfrview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCih1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.sfrview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gHCiiFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gHCiiVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gHCiilz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_gHCii1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_gHCijFz6EfCbCMAxoqNuvA" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gHCijVz6EfCbCMAxoqNuvA" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCijlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_gHCillz6EfCbCMAxoqNuvA" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_gHFn5lz6EfCbCMAxoqNuvA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCinlz6EfCbCMAxoqNuvA" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gHCin1z6EfCbCMAxoqNuvA" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCioFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_gHCiolz6EfCbCMAxoqNuvA" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_gHFn1Fz6EfCbCMAxoqNuvA"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_gHCio1z6EfCbCMAxoqNuvA" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_gHGNJVz6EfCbCMAxoqNuvA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCiqFz6EfCbCMAxoqNuvA" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gHCiqVz6EfCbCMAxoqNuvA" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCizFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi0lz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi11z6EfCbCMAxoqNuvA" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi21z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi5Fz6EfCbCMAxoqNuvA" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gHCi5Vz6EfCbCMAxoqNuvA" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi5lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_gHCi7Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_gHFnilz6EfCbCMAxoqNuvA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi81z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.editor.CEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi9Fz6EfCbCMAxoqNuvA" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gHCi9Vz6EfCbCMAxoqNuvA" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi9lz6EfCbCMAxoqNuvA" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gHCi91z6EfCbCMAxoqNuvA" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gHCi-Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gHCi-1z6EfCbCMAxoqNuvA" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gHCi_1z6EfCbCMAxoqNuvA" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_gHCjAFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_gHCjAVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gHCjBlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gHCjB1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_gHCjC1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_gHCjDFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_gHCjDVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_gHCjDlz6EfCbCMAxoqNuvA" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_gHDKf1z6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHCjD1z6EfCbCMAxoqNuvA" keySequence="CTRL+1" command="_gHFng1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjEFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+L" command="_gHGM2lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjEVz6EfCbCMAxoqNuvA" keySequence="CTRL+V" command="_gHFm11z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjElz6EfCbCMAxoqNuvA" keySequence="CTRL+A" command="_gHGM91z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjE1z6EfCbCMAxoqNuvA" keySequence="CTRL+C" command="_gHFmrlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjFFz6EfCbCMAxoqNuvA" keySequence="CTRL+X" command="_gHFn3Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjFVz6EfCbCMAxoqNuvA" keySequence="CTRL+Y" command="_gHGNJVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjFlz6EfCbCMAxoqNuvA" keySequence="CTRL+Z" command="_gHFn1Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjF1z6EfCbCMAxoqNuvA" keySequence="ALT+PAGE_UP" command="_gHGNO1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjGFz6EfCbCMAxoqNuvA" keySequence="ALT+PAGE_DOWN" command="_gHFnQVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjGVz6EfCbCMAxoqNuvA" keySequence="SHIFT+INSERT" command="_gHFm11z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjGlz6EfCbCMAxoqNuvA" keySequence="ALT+F11" command="_gHFnF1z6EfCbCMAxoqNuvA">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_gHCjG1z6EfCbCMAxoqNuvA" keySequence="CTRL+F10" command="_gHFm-Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHCjHFz6EfCbCMAxoqNuvA" keySequence="CTRL+INSERT" command="_gHFmrlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJYFz6EfCbCMAxoqNuvA" keySequence="CTRL+PAGE_UP" command="_gHFoAVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJYVz6EfCbCMAxoqNuvA" keySequence="CTRL+PAGE_DOWN" command="_gHFni1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJYlz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+F3" command="_gHFn81z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJY1z6EfCbCMAxoqNuvA" keySequence="SHIFT+DEL" command="_gHFn3Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJZFz6EfCbCMAxoqNuvA" keySequence="ALT+/" command="_gHFnu1z6EfCbCMAxoqNuvA">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_gHDJZVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.textEditorScope" bindingContext="_gHDKhFz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDJZlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+CR" command="_gHFn8Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJZ1z6EfCbCMAxoqNuvA" keySequence="CTRL+BS" command="_gHFmpVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJaFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+Q" command="_gHFncFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJaVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+J" command="_gHFnX1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJalz6EfCbCMAxoqNuvA" keySequence="CTRL++" command="_gHFnFVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJa1z6EfCbCMAxoqNuvA" keySequence="CTRL+-" command="_gHGM3Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJbFz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+J" command="_gHFnfVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJbVz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+A" command="_gHFmzFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJblz6EfCbCMAxoqNuvA" keySequence="CTRL+J" command="_gHFm_1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJb1z6EfCbCMAxoqNuvA" keySequence="CTRL+L" command="_gHFnz1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJcFz6EfCbCMAxoqNuvA" keySequence="CTRL+D" command="_gHFnCVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJcVz6EfCbCMAxoqNuvA" keySequence="CTRL+=" command="_gHFnFVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJclz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+/" command="_gHGM6Fz6EfCbCMAxoqNuvA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_gHDJc1z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Y" command="_gHFmnVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJdFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+DEL" command="_gHFnwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJdVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+X" command="_gHFmuVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJdlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+Y" command="_gHGM3Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJd1z6EfCbCMAxoqNuvA" keySequence="CTRL+DEL" command="_gHFn0Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJeFz6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_UP" command="_gHGNN1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJeVz6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_DOWN" command="_gHFnTVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJelz6EfCbCMAxoqNuvA" keySequence="SHIFT+END" command="_gHGM41z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJe1z6EfCbCMAxoqNuvA" keySequence="SHIFT+HOME" command="_gHGMyFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJfFz6EfCbCMAxoqNuvA" keySequence="END" command="_gHGMulz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJfVz6EfCbCMAxoqNuvA" keySequence="INSERT" command="_gHFm8Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJflz6EfCbCMAxoqNuvA" keySequence="F2" command="_gHFnjVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJf1z6EfCbCMAxoqNuvA" keySequence="HOME" command="_gHGM5Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJgFz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+ARROW_UP" command="_gHGNElz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJgVz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+ARROW_DOWN" command="_gHGNBFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJglz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+INSERT" command="_gHFnRVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJg1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_gHGM5lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJhFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_gHFnUFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJhVz6EfCbCMAxoqNuvA" keySequence="CTRL+F10" command="_gHFn7Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJhlz6EfCbCMAxoqNuvA" keySequence="CTRL+END" command="_gHFnU1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJh1z6EfCbCMAxoqNuvA" keySequence="CTRL+ARROW_UP" command="_gHFnKVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJiFz6EfCbCMAxoqNuvA" keySequence="CTRL+ARROW_DOWN" command="_gHGNT1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJiVz6EfCbCMAxoqNuvA" keySequence="CTRL+ARROW_LEFT" command="_gHFmplz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJilz6EfCbCMAxoqNuvA" keySequence="CTRL+ARROW_RIGHT" command="_gHFnbVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJi1z6EfCbCMAxoqNuvA" keySequence="CTRL+HOME" command="_gHFm1lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJjFz6EfCbCMAxoqNuvA" keySequence="CTRL+NUMPAD_MULTIPLY" command="_gHFnb1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJjVz6EfCbCMAxoqNuvA" keySequence="CTRL+NUMPAD_ADD" command="_gHGM_1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJjlz6EfCbCMAxoqNuvA" keySequence="CTRL+NUMPAD_SUBTRACT" command="_gHFn71z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJj1z6EfCbCMAxoqNuvA" keySequence="CTRL+NUMPAD_DIVIDE" command="_gHFnLFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJkFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_gHFne1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJkVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_gHFm8lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJklz6EfCbCMAxoqNuvA" keySequence="SHIFT+CR" command="_gHGM5Fz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDJk1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_gHDKiFz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDJlFz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+SHIFT+C" command="_gHGNUVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJlVz6EfCbCMAxoqNuvA" keySequence="CTRL+TAB" command="_gHGNS1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJllz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+P" command="_gHGMvFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJl1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+T" command="_gHFnpFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJmFz6EfCbCMAxoqNuvA" keySequence="CTRL+7" command="_gHFm2lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJmVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+H" command="_gHFnAlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJmlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+N" command="_gHFnbFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJm1z6EfCbCMAxoqNuvA" keySequence="CTRL+/" command="_gHFm2lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJnFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+O" command="_gHFn7Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJnVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+A" command="_gHFn9Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJnlz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+S" command="_gHGNK1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJn1z6EfCbCMAxoqNuvA" keySequence="CTRL+#" command="_gHFnmVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJoFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+C" command="_gHFm2lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJoVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+F" command="_gHGNTFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJolz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+G" command="_gHFmoFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJo1z6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+H" command="_gHFnhlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJpFz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+I" command="_gHFm3lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJpVz6EfCbCMAxoqNuvA" keySequence="CTRL+T" command="_gHFnWFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJplz6EfCbCMAxoqNuvA" keySequence="CTRL+I" command="_gHFnXVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJp1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+/" command="_gHGM_lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJqFz6EfCbCMAxoqNuvA" keySequence="CTRL+O" command="_gHFmwVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJqVz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+R" command="_gHFnxVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJqlz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+S" command="_gHFm-lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJq1z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+T" command="_gHFnnFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJrFz6EfCbCMAxoqNuvA" keySequence="CTRL+G" command="_gHGNIFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJrVz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+L" command="_gHGNBlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJrlz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+M" command="_gHFmzVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJr1z6EfCbCMAxoqNuvA" keySequence="CTRL+=" command="_gHFnmVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJsFz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+O" command="_gHFn3lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJsVz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Z" command="_gHFn31z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJslz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+\" command="_gHFm21z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJs1z6EfCbCMAxoqNuvA" keySequence="F3" command="_gHGNVVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJtFz6EfCbCMAxoqNuvA" keySequence="F4" command="_gHGM-Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJtVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+ARROW_UP" command="_gHFnyVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJtlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_gHFnQ1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJt1z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+ARROW_UP" command="_gHGMy1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJuFz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_gHGNTVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJuVz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_gHFnvFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJulz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_gHGM0lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJu1z6EfCbCMAxoqNuvA" keySequence="ALT+C" command="_gHFm3Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJvFz6EfCbCMAxoqNuvA" keySequence="SHIFT+TAB" command="_gHFnBFz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDJvVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.contexts.window" bindingContext="_gHDKgFz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDJvlz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+SHIFT+L" command="_gHFm5Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJv1z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q O" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJwFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_gHDJwVz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+B" command="_gHFnOFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJwlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+R" command="_gHGNU1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJw1z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q Q" command="_gHFnMFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJxFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+S" command="_gHFnDlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJxVz6EfCbCMAxoqNuvA" keySequence="CTRL+3" command="_gHFnjFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJxlz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q S" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJx1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_gHDJyFz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q V" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJyVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_gHDJylz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+G" command="_gHFnIlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJy1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+W" command="_gHFn21z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJzFz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q H" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJzVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_gHDJzlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+K" command="_gHFnI1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJz1z6EfCbCMAxoqNuvA" keySequence="CTRL+," command="_gHFm3Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ0Fz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q L" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ0Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_gHDJ0lz6EfCbCMAxoqNuvA" keySequence="CTRL+." command="_gHGNFVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ01z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+B" command="_gHFnJFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ1Fz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+E" command="_gHFnP1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ1Vz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q X" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ1lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_gHDJ11z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q Y" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ2Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_gHDJ2Vz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q Z" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ2lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_gHDJ21z6EfCbCMAxoqNuvA" keySequence="CTRL+P" command="_gHFn5lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ3Fz6EfCbCMAxoqNuvA" keySequence="CTRL+Q" command="_gHFn-1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ3Vz6EfCbCMAxoqNuvA" keySequence="CTRL+S" command="_gHGM3lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ3lz6EfCbCMAxoqNuvA" keySequence="CTRL+W" command="_gHGNMFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ31z6EfCbCMAxoqNuvA" keySequence="CTRL+H" command="_gHFnuVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ4Fz6EfCbCMAxoqNuvA" keySequence="CTRL+K" command="_gHFnOlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ4Vz6EfCbCMAxoqNuvA" keySequence="CTRL+M" command="_gHFnslz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ4lz6EfCbCMAxoqNuvA" keySequence="CTRL+N" command="_gHGNLFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ41z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+?" command="_gHFnpVz6EfCbCMAxoqNuvA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_gHDJ5Fz6EfCbCMAxoqNuvA" keySequence="CTRL+B" command="_gHFm4Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ5Vz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q B" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ5lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_gHDJ51z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+Q C" command="_gHFnMFz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ6Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_gHDJ6Vz6EfCbCMAxoqNuvA" keySequence="CTRL+E" command="_gHFnzVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ6lz6EfCbCMAxoqNuvA" keySequence="CTRL+F" command="_gHFnFFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ61z6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+W" command="_gHGNEVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ7Fz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+N" command="_gHFn11z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ7Vz6EfCbCMAxoqNuvA" keySequence="CTRL+_" command="_gHFnqlz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ7lz6EfCbCMAxoqNuvA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_gHDJ71z6EfCbCMAxoqNuvA" keySequence="CTRL+{" command="_gHFnqlz6EfCbCMAxoqNuvA">
      <parameters xmi:id="_gHDJ8Fz6EfCbCMAxoqNuvA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_gHDJ8Vz6EfCbCMAxoqNuvA" keySequence="SHIFT+F9" command="_gHFn6Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ8lz6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_LEFT" command="_gHFm_Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ81z6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_RIGHT" command="_gHFn8lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ9Fz6EfCbCMAxoqNuvA" keySequence="SHIFT+F5" command="_gHGNDFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ9Vz6EfCbCMAxoqNuvA" keySequence="ALT+F7" command="_gHFm2Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ9lz6EfCbCMAxoqNuvA" keySequence="F9" command="_gHGMwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ91z6EfCbCMAxoqNuvA" keySequence="F11" command="_gHGM9Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ-Fz6EfCbCMAxoqNuvA" keySequence="F12" command="_gHFnvlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ-Vz6EfCbCMAxoqNuvA" keySequence="F2" command="_gHFm31z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ-lz6EfCbCMAxoqNuvA" keySequence="F5" command="_gHGMu1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ-1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+F7" command="_gHGM9Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ_Fz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+F8" command="_gHFnqFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ_Vz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+ARROW_LEFT" command="_gHFn-1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ_lz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+ARROW_RIGHT" command="_gHFnLlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDJ_1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+F4" command="_gHFn21z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKAFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+F6" command="_gHFnE1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKAVz6EfCbCMAxoqNuvA" keySequence="CTRL+F7" command="_gHFmr1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKAlz6EfCbCMAxoqNuvA" keySequence="CTRL+F8" command="_gHFnhVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKA1z6EfCbCMAxoqNuvA" keySequence="CTRL+F4" command="_gHGNMFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKBFz6EfCbCMAxoqNuvA" keySequence="CTRL+F6" command="_gHFnG1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKBVz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+F7" command="_gHFnV1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKBlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_gHFnDVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKB1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_gHFnrVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKCFz6EfCbCMAxoqNuvA" keySequence="DEL" command="_gHFnIFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKCVz6EfCbCMAxoqNuvA" keySequence="ALT+?" command="_gHFnpVz6EfCbCMAxoqNuvA">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_gHDKClz6EfCbCMAxoqNuvA" keySequence="ALT+-" command="_gHFmxFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKC1z6EfCbCMAxoqNuvA" keySequence="ALT+CR" command="_gHFnmlz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKDFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_gHDKhlz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKDVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+P" command="_gHGMz1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKDlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+G" command="_gHGMuVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKD1z6EfCbCMAxoqNuvA" keySequence="F3" command="_gHFn_Fz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKEFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_gHDKlFz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKEVz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+T" command="_gHFnpFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKElz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+H" command="_gHFnAlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKE1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+G" command="_gHFmoFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKFFz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+H" command="_gHFnhlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKFVz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+I" command="_gHFm3lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKFlz6EfCbCMAxoqNuvA" keySequence="ALT+SHIFT+R" command="_gHFnxVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKF1z6EfCbCMAxoqNuvA" keySequence="CTRL+G" command="_gHGNIFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKGFz6EfCbCMAxoqNuvA" keySequence="F3" command="_gHGNVVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKGVz6EfCbCMAxoqNuvA" keySequence="F4" command="_gHGM-Fz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKGlz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_gHDKglz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKG1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+V" command="_gHGMzlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKHFz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+C" command="_gHFnkVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKHVz6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_UP" command="_gHFmpFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKHlz6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_RIGHT" command="_gHGM8Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKH1z6EfCbCMAxoqNuvA" keySequence="SHIFT+INSERT" command="_gHGMzlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKIFz6EfCbCMAxoqNuvA" keySequence="CTRL+INSERT" command="_gHFnkVz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKIVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_gHDKh1z6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKIlz6EfCbCMAxoqNuvA" keySequence="CTRL+/" command="_gHFnNVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKI1z6EfCbCMAxoqNuvA" keySequence="F3" command="_gHFnB1z6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKJFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_gHDKjFz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKJVz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+M" command="_gHGM9lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKJlz6EfCbCMAxoqNuvA" keySequence="ALT+CTRL+N" command="_gHGNA1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKJ1z6EfCbCMAxoqNuvA" keySequence="CTRL+T" command="_gHFnlFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKKFz6EfCbCMAxoqNuvA" keySequence="CTRL+W" command="_gHFm91z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKKVz6EfCbCMAxoqNuvA" keySequence="CTRL+N" command="_gHFnGFz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKKlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.debugging" bindingContext="_gHDKjVz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKK1z6EfCbCMAxoqNuvA" keySequence="CTRL+R" command="_gHFmtVz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKLFz6EfCbCMAxoqNuvA" keySequence="F7" command="_gHGNHFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKLVz6EfCbCMAxoqNuvA" keySequence="F8" command="_gHFm7Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKLlz6EfCbCMAxoqNuvA" keySequence="F5" command="_gHFm7lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKL1z6EfCbCMAxoqNuvA" keySequence="F6" command="_gHGM7Fz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKMFz6EfCbCMAxoqNuvA" keySequence="CTRL+F2" command="_gHFnwVz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKMVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_gHDKjlz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKMlz6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+," command="_gHFn_Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKM1z6EfCbCMAxoqNuvA" keySequence="CTRL+SHIFT+." command="_gHFnr1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKNFz6EfCbCMAxoqNuvA" keySequence="CTRL+G" command="_gHFnsFz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKNVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_gHDKkVz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKNlz6EfCbCMAxoqNuvA" keySequence="CTRL+G" command="_gHGNKFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKN1z6EfCbCMAxoqNuvA" keySequence="HOME" command="_gHFnj1z6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKOFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.console" bindingContext="_gHDKiVz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKOVz6EfCbCMAxoqNuvA" keySequence="CTRL+Z" command="_gHGND1z6EfCbCMAxoqNuvA">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_gHDKOlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_gHDKklz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKO1z6EfCbCMAxoqNuvA" keySequence="SHIFT+F7" command="_gHFnBlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKPFz6EfCbCMAxoqNuvA" keySequence="SHIFT+F8" command="_gHFnp1z6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKPVz6EfCbCMAxoqNuvA" keySequence="SHIFT+F5" command="_gHGM7Vz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKPlz6EfCbCMAxoqNuvA" keySequence="SHIFT+F6" command="_gHFm4lz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKP1z6EfCbCMAxoqNuvA" keySequence="CTRL+F5" command="_gHGM6Vz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKQFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_gHDwcFz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKQVz6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_LEFT" command="_gHFnWlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKQlz6EfCbCMAxoqNuvA" keySequence="ALT+ARROW_RIGHT" command="_gHFndlz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKQ1z6EfCbCMAxoqNuvA" keySequence="F3" command="_gHGNVVz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKRFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_gHDKhVz6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKRVz6EfCbCMAxoqNuvA" keySequence="F3" command="_gHGNVVz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKRlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_gHDKi1z6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKR1z6EfCbCMAxoqNuvA" keySequence="CTRL+INSERT" command="_gHFnDFz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKSFz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_gHDKk1z6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKSVz6EfCbCMAxoqNuvA" keySequence="ALT+Y" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKSlz6EfCbCMAxoqNuvA" keySequence="ALT+A" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKS1z6EfCbCMAxoqNuvA" keySequence="ALT+B" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKTFz6EfCbCMAxoqNuvA" keySequence="ALT+C" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKTVz6EfCbCMAxoqNuvA" keySequence="ALT+D" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKTlz6EfCbCMAxoqNuvA" keySequence="ALT+E" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKT1z6EfCbCMAxoqNuvA" keySequence="ALT+F" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKUFz6EfCbCMAxoqNuvA" keySequence="ALT+G" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKUVz6EfCbCMAxoqNuvA" keySequence="ALT+P" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKUlz6EfCbCMAxoqNuvA" keySequence="ALT+R" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKU1z6EfCbCMAxoqNuvA" keySequence="ALT+S" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKVFz6EfCbCMAxoqNuvA" keySequence="ALT+T" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKVVz6EfCbCMAxoqNuvA" keySequence="ALT+V" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKVlz6EfCbCMAxoqNuvA" keySequence="ALT+W" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKV1z6EfCbCMAxoqNuvA" keySequence="ALT+H" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKWFz6EfCbCMAxoqNuvA" keySequence="ALT+L" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
    <bindings xmi:id="_gHDKWVz6EfCbCMAxoqNuvA" keySequence="ALT+N" command="_gHFmwFz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKWlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.context" bindingContext="_gHDwc1z6EfCbCMAxoqNuvA">
    <bindings xmi:id="_gHDKW1z6EfCbCMAxoqNuvA" keySequence="ALT+K" command="_gHFnSFz6EfCbCMAxoqNuvA"/>
  </bindingTables>
  <bindingTables xmi:id="_gHDKXFz6EfCbCMAxoqNuvA" bindingContext="_gHDwdFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKXVz6EfCbCMAxoqNuvA" bindingContext="_gHDwdVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKXlz6EfCbCMAxoqNuvA" bindingContext="_gHDwdlz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKX1z6EfCbCMAxoqNuvA" bindingContext="_gHDwd1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKYFz6EfCbCMAxoqNuvA" bindingContext="_gHDweFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKYVz6EfCbCMAxoqNuvA" bindingContext="_gHDweVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKYlz6EfCbCMAxoqNuvA" bindingContext="_gHDwelz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKY1z6EfCbCMAxoqNuvA" bindingContext="_gHDwe1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKZFz6EfCbCMAxoqNuvA" bindingContext="_gHDwfFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKZVz6EfCbCMAxoqNuvA" bindingContext="_gHDwfVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKZlz6EfCbCMAxoqNuvA" bindingContext="_gHDwflz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKZ1z6EfCbCMAxoqNuvA" bindingContext="_gHDwf1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKaFz6EfCbCMAxoqNuvA" bindingContext="_gHDwgFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKaVz6EfCbCMAxoqNuvA" bindingContext="_gHDwgVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKalz6EfCbCMAxoqNuvA" bindingContext="_gHDwglz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKa1z6EfCbCMAxoqNuvA" bindingContext="_gHDwg1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKbFz6EfCbCMAxoqNuvA" bindingContext="_gHDwhFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKbVz6EfCbCMAxoqNuvA" bindingContext="_gHDwhVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKblz6EfCbCMAxoqNuvA" bindingContext="_gHDwhlz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKb1z6EfCbCMAxoqNuvA" bindingContext="_gHDwh1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKcFz6EfCbCMAxoqNuvA" bindingContext="_gHDwiFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKcVz6EfCbCMAxoqNuvA" bindingContext="_gHDwiVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKclz6EfCbCMAxoqNuvA" bindingContext="_gHDwilz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKc1z6EfCbCMAxoqNuvA" bindingContext="_gHDwi1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKdFz6EfCbCMAxoqNuvA" bindingContext="_gHDwjFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKdVz6EfCbCMAxoqNuvA" bindingContext="_gHDwjVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKdlz6EfCbCMAxoqNuvA" bindingContext="_gHDwjlz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKd1z6EfCbCMAxoqNuvA" bindingContext="_gHDwj1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKeFz6EfCbCMAxoqNuvA" bindingContext="_gHDwkFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKeVz6EfCbCMAxoqNuvA" bindingContext="_gHDwkVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKelz6EfCbCMAxoqNuvA" bindingContext="_gHDwklz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKe1z6EfCbCMAxoqNuvA" bindingContext="_gHDwk1z6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKfFz6EfCbCMAxoqNuvA" bindingContext="_gHDwlFz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKfVz6EfCbCMAxoqNuvA" bindingContext="_gHDwlVz6EfCbCMAxoqNuvA"/>
  <bindingTables xmi:id="_gHDKflz6EfCbCMAxoqNuvA" bindingContext="_gHDwllz6EfCbCMAxoqNuvA"/>
  <rootContext xmi:id="_gHDKf1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_gHDKgFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_gHDKgVz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_gHDKglz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_gHDKg1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_gHDKhFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_gHDKhVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_gHDKhlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_gHDKh1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_gHDKiFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_gHDKiVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_gHDKilz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_gHDKi1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_gHDKjFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_gHDKjVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_gHDKjlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_gHDKj1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mpu.debug.ui.debugging" name="Debugging C/C++ on MPU - Cortex-M" description="Debugging C/C++ Programs on MPU - Cortex-M"/>
        <children xmi:id="_gHDKkFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.ui.debugging" name="Debugging C/C++ on MCU" description="Debugging C/C++ Programs on MCU"/>
        <children xmi:id="_gHDKkVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_gHDKklz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_gHDKk1z6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_gHDKlFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
    </children>
    <children xmi:id="_gHDKlVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_gHDwcFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_gHDwcVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_gHDwclz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_gHDwc1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.context" name="Device Configuration Tool Context" description="Device Configuration Tool  Context"/>
  <rootContext xmi:id="_gHDwdFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet" name="Auto::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet"/>
  <rootContext xmi:id="_gHDwdVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.informationcenter.actionSet3" name="Auto::com.st.stm32cube.ide.mcu.informationcenter.actionSet3"/>
  <rootContext xmi:id="_gHDwdlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_gHDwd1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_gHDweFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_gHDweVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_gHDwelz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_gHDwe1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_gHDwfFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_gHDwfVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_gHDwflz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_gHDwf1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_gHDwgFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_gHDwgVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_gHDwglz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_gHDwg1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_gHDwhFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_gHDwhVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_gHDwhlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_gHDwh1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_gHDwiFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_gHDwiVz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_gHDwilz6EfCbCMAxoqNuvA" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_gHDwi1z6EfCbCMAxoqNuvA" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_gHDwjFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_gHDwjVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_gHDwjlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_gHDwj1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_gHDwkFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_gHDwkVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_gHDwklz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_gHDwk1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_gHDwlFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_gHDwlVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_gHDwllz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.launch.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::com.st.stm32cube.ide.mcu.debug.launch.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_gHDwl1z6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwmFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.views.OutputsView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" category="Device Configuration Tool" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
    <tags>View</tags>
    <tags>categoryTag:Device Configuration Tool</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwmVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwmlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwm1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.logview" label="SWV Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.SWVLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwnFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.statisticalprofiling" label="SWV Statistical Profiling" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_statistical_profiling.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.statisticalprofiling.SWVStatisticalProfilingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwnVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.itmtrace" label="SWV ITM Data Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/console_view.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.itmtrace.SWVConsole"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwnlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.exceptionlogview" label="SWV Exception Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_Exception_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.SWVExceptionLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwn1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceview" label="SWV Data Trace" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/insp_sbook.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatrace.SWVDataTraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwoFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.SWVDatatraceTimeline" label="SWV Data Trace Timeline Graph" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/Datatrace_timeline.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceTimeline.SWVDatatraceTimeline"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwoVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.faultanalyzer.view" label="&#x6545;&#x969c;&#x5206;&#x6790;&#x5668;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.faultanalyzer/icons/clanbomber.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.faultanalyzer.FaultAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.faultanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwolz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.freertos.queues" label="FreeRTOS Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.queues.FORtosQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwo1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.freertos.tasklist" label="FreeRTOS Task List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.tasklist.FORtosTaskList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwpFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.freertos.semaphore" label="FreeRTOS Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.semaphores.FORtosSemaphores"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwpVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.freertos.timers" label="FreeRTOS Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.timers.FORtosTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwplz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" label="&#x73b0;&#x573a;&#x8868;&#x8fbe;&#x5f0f;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwp1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.sfrview" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwqFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwqVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.tcp.console.view" label="TCP Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.tcp.console/icons/console.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.tcp.console.ui.TCPConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.tcp.console"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwqlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.threads" label="ThreadX Thread List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.threadlist.ThreadXThreadList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwq1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.semaphores" label="ThreadX Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.semaphores.ThreadXSemaphoreList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwrFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.mutexes" label="ThreadX Mutexes" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.mutexes.ThreadXMutexList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwrVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.queues" label="ThreadX Message Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.queues.ThreadXMessageQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwrlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.eventflags" label="ThreadX Event Flags" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.eventflags.ThreadXEventFlags"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwr1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.timer" label="ThreadX Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.timers.ThreadXTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwsFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.blockpools" label="ThreadX Memory Block Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.blockpools.ThreadXMemoryBlockPools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwsVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.bytepools" label="ThreadX Memory Byte Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.bytepools.ThreadXMemoryBytePools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwslz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mpu.flashmemory.programmer.view" label="Flash Memories Programmer" iconURI="platform:/plugin/com.st.stm32cube.ide.mpu.flashmemory.programmer/icons/soc.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mpu.flashmemory.programmer.views.FlashMemoryProgrammerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mpu.flashmemory.programmer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDws1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwtFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwtVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwtlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwt1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwuFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwuVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwulz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwu1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwvFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwvVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwvlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwv1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwwFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwwVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwwlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDww1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwxFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwxVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwxlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwx1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwyFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwyVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwylz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwy1z6EfCbCMAxoqNuvA" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwzFz6EfCbCMAxoqNuvA" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwzVz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwzlz6EfCbCMAxoqNuvA" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_gHDwz1z6EfCbCMAxoqNuvA" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw0Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw0Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw0lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw01z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw1Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw1Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw1lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw11z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw2Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw2Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw2lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw21z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw3Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gHDw3Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_gHFmi1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_gHFmjFz6EfCbCMAxoqNuvA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_gHFmjVz6EfCbCMAxoqNuvA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_gHFmjlz6EfCbCMAxoqNuvA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_gHFmnFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmnVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmnlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmn1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmoFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmoVz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_gHGN1Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmolz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmo1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmpFz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_gHGN0lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmpVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmplz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmp1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_gHGN0Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmqFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmqVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_gHGNy1z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFmqlz6EfCbCMAxoqNuvA" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_gHFmq1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmrFz6EfCbCMAxoqNuvA" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_gHGN3Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFmrVz6EfCbCMAxoqNuvA" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_gHFmrlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmr1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmsFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_gHGNwFz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFmsVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_gHFmslz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFms1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmtFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmtVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmtlz6EfCbCMAxoqNuvA" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_gHGN2Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmt1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmuFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmuVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmulz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmu1z6EfCbCMAxoqNuvA" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmvFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmvVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmvlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmv1z6EfCbCMAxoqNuvA" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmwFz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_gHGN0lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmwVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmwlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_gHGN0Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmw1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmxFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmxVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmxlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmx1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_gHGN2lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmyFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_gHGN3Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmyVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmylz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmy1z6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmzFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmzVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmzlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFmz1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm0Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm0Vz6EfCbCMAxoqNuvA" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_gHGNx1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm0lz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mpu.linux.ide.command.setupopenstlinux" commandName="Setup OpenSTLinux" description="Setup OpenSTLinux" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm01z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm1Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm1Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm1lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm11z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm2Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm2Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm2lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm21z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm3Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm3Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm3lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm31z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm4Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm4Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm4lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_gHGN21z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm41z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm5Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_gHGNylz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm5Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm5lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_gHGNxFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm51z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.ide.command.generatecode" commandName="Generate Code" description="Generate Code (based on .ioc file content)" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm6Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm6Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm6lz6EfCbCMAxoqNuvA" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm61z6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_gHGN2Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm7Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_gHGN3lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm7Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm7lz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm71z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm8Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm8Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm8lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm81z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm9Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm9Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_gHGN3Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFm9lz6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_gHFm91z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm-Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm-Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm-lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm-1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm_Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm_Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm_lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFm_1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnAFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnAVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnAlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnA1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnBFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnBVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnBlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_gHGN21z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnB1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_gHGN01z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnCFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnCVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnClz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_gHGN2Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnC1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartConfigurationCommand" commandName="Restart Configuration Command" category="_gHGNzFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnDFz6EfCbCMAxoqNuvA" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_gHGNx1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnDVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnDlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnD1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.externaltools.test" commandName="Test ExternalTools" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnEFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnEVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnElz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnE1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnFFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnFVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnFlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnF1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnGFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnGVz6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnGlz6EfCbCMAxoqNuvA" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_gHGNvlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnG1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnHFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnHVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnHlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.common.ui.view_export" commandName="Export view data to file" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnH1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnIFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnIVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnIlz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_gHGN1Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnI1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnJFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnJVz6EfCbCMAxoqNuvA" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_gHGN1Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnJlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnJ1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_gHGN21z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnKFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_gHFnKVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnKlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnK1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnLFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnLVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnLlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnL1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnMFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_gHGNv1z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnMVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_gHFnMlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_gHFnM1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_gHFnNFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnNVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_gHGN01z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnNlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_gHGNxlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnN1z6EfCbCMAxoqNuvA" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_gHFnOFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnOVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnOlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnO1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnPFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnPVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_gHGNyFz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnPlz6EfCbCMAxoqNuvA" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_gHFnP1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnQFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnQVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnQlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnQ1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnRFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnRVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnRlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_gHGNzlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnR1z6EfCbCMAxoqNuvA" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_gHFnSFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.menu.generatecode" commandName="Generate Code" description="Generate Code" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnSVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnSlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnS1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnTFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnTVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnTlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.datarefresh" commandName="Data Refresh" description="Data Refresh" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnT1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnUFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnUVz6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnUlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnU1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnVFz6EfCbCMAxoqNuvA" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_gHGN1Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnVVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnVlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mpu.flashmemory.programmer.command" commandName="Flash Memory Programmer Command" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnV1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnWFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnWVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showInCommand" commandName="name" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnWlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnW1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnXFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnXVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnXlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnX1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnYFz6EfCbCMAxoqNuvA" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_gHGN3Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnYVz6EfCbCMAxoqNuvA" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_gHFnYlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnY1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.pintopincompatibility" commandName="Pin to pin compatibility" description="Pin to pin compatibility" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnZFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnZVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_gHGN0Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnZlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_gHGNxFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnZ1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_gHGNxVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnaFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnaVz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_gHGN1Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnalz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFna1z6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_gHGN1Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnbFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnbVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnblz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnb1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFncFz6EfCbCMAxoqNuvA" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFncVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnclz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showstate" commandName="name" category="_gHGN3Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnc1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_gHFndFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFndVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFndlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnd1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFneFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.docsandresources" commandName="Docs And Resources" description="Docs And Resources" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFneVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_gHGNzlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnelz6EfCbCMAxoqNuvA" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_gHFne1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnfFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnfVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnflz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnf1z6EfCbCMAxoqNuvA" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFngFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFngVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_gHGNxFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnglz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFng1z6EfCbCMAxoqNuvA" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnhFz6EfCbCMAxoqNuvA" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_gHGN1Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnhVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnhlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnh1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFniFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.generatereport" commandName="Generate Report" description="Generate Report" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFniVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnilz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFni1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnjFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnjVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnjlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnj1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnkFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnkVz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_gHGN0lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnklz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnk1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnlFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnlVz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnllz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.informationcenter.tutorialvideo" commandName="Tutorial Video" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnl1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_gHGN2Vz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnmFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_gHFnmVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnmlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnm1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnnFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnnVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_gHGNy1z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnnlz6EfCbCMAxoqNuvA" elementId="url" name="URL"/>
    <parameters xmi:id="_gHFnn1z6EfCbCMAxoqNuvA" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_gHFnoFz6EfCbCMAxoqNuvA" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_gHFnoVz6EfCbCMAxoqNuvA" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_gHFnolz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFno1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnpFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnpVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnplz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnp1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_gHGN21z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnqFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnqVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.toolbar.generatecode" commandName="Generate Code" description="Generate Code" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnqlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_gHGNy1z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFnq1z6EfCbCMAxoqNuvA" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_gHFnrFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnrVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnrlz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnr1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnsFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnsVz6EfCbCMAxoqNuvA" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnslz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFns1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFntFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFntVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.start_trace" commandName="Start Trace" description="Start Trace" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFntlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnt1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnuFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnuVz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_gHGN1Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnulz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.modifyIncludePathsBySelection" commandName="Add/remove include path..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnu1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnvFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnvVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnvlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnv1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnwFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnwVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnwlz6EfCbCMAxoqNuvA" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnw1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnxFz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnxVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnxlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_gHGN3lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnx1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnyFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnyVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnylz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_gHGN2Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFny1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_gHFnzFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_gHFnzVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnzlz6EfCbCMAxoqNuvA" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_gHGNx1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFnz1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn0Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn0Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn0lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn01z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn1Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn1Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn1lz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn11z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn2Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn2Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn2lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn21z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn3Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn3Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn3lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn31z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn4Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn4Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn4lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn41z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_gHGNyFz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFn5Fz6EfCbCMAxoqNuvA" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_gHFn5Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_gHGN2Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn5lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn51z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp" commandName="Convert Project to C or CPP" category="_gHGN3Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHFn6Fz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp.type" name="Convert Type (C/C++)"/>
  </commands>
  <commands xmi:id="_gHFn6Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn6lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn61z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn7Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn7Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn7lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn71z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn8Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn8Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn8lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn81z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn9Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn9Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn9lz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.ide.connectionToMyST" commandName="Connection to myST" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn91z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mpu.remote.serial.connectconsole" commandName="Connect Console Command" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn-Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_gHGN1Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn-Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn-lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn-1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn_Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn_Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn_lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFn_1z6EfCbCMAxoqNuvA" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_gHGN1Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFoAFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFoAVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHFoAlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_gHGN11z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGMsFz6EfCbCMAxoqNuvA" elementId="title" name="Title"/>
    <parameters xmi:id="_gHGMsVz6EfCbCMAxoqNuvA" elementId="message" name="Message"/>
    <parameters xmi:id="_gHGMslz6EfCbCMAxoqNuvA" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_gHGMs1z6EfCbCMAxoqNuvA" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_gHGMtFz6EfCbCMAxoqNuvA" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_gHGMtVz6EfCbCMAxoqNuvA" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_gHGMtlz6EfCbCMAxoqNuvA" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_gHGMt1z6EfCbCMAxoqNuvA" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_gHGMuFz6EfCbCMAxoqNuvA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_gHGMuVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMulz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMu1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMvFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMvVz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMvlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMv1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMwFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMwVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMwlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMw1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMxFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMxVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMxlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_gHGN3lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMx1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_gHGN0Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMyFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMyVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMylz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMy1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMzFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMzVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartCommand" commandName="Restart Command" category="_gHGNzFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMzlz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_gHGN0lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGMz1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM0Fz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.checkforupdates" commandName="Check For Embedded Software Packages Updates" description="Check For Embedded Software Packages Updates" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM0Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM0lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM01z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_gHGNxlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGM1Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_gHGM1Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM1lz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM11z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_gHGN3Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGM2Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_gHGM2Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_gHGM2lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM21z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM3Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM3Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM3lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM31z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_gHGN3lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM4Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_gHGNvlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM4Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_gHGN3Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM4lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM41z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM5Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM5Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM5lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM51z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM6Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM6Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM6lz6EfCbCMAxoqNuvA" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM61z6EfCbCMAxoqNuvA" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_gHGNvlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM7Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM7Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_gHGN21z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM7lz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM71z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.threadx.trx_to_file_command" commandName="Export trace" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM8Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM8Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_gHGN0lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM8lz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_gHGNyFz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGM81z6EfCbCMAxoqNuvA" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_gHGM9Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM9Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM9lz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM91z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM-Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM-Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM-lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_gHGNxFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM-1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM_Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM_Vz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.cmake.cmake_run_builder" commandName="cmake_run_builder" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM_lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGM_1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNAFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNAVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.openconfig" commandName="Config" description="Configure SWV" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNAlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_gHGN2lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNA1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNBFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNBVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_gHGNz1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNBlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNB1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNCFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNCVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNClz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNC1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNDFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNDVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNDlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGND1z6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNEFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_gHGN3lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNEVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNElz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNE1z6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.stlink.fwupgrade" commandName="ST-LINK &#x66f4;&#x65b0;" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNFFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNFVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNFlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_gHGNy1z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNF1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_gHGNGFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_gHGNzlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNGVz6EfCbCMAxoqNuvA" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_gHGNGlz6EfCbCMAxoqNuvA" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_gHGNG1z6EfCbCMAxoqNuvA" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_gHGNHFz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNHVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_gHGNy1z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNHlz6EfCbCMAxoqNuvA" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_gHGNH1z6EfCbCMAxoqNuvA" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_gHGNIFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNIVz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.cmake.commands.cmakeimport" commandName="cmakeimport" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNIlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_gHGNzlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNI1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_gHGN2lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNJFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNJVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNJlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.p2.list" commandName="P2 IU List" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNJ1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_gHGNzVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNKFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNKVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNKlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_gHGN0Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNK1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNLFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_gHGNyFz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNLVz6EfCbCMAxoqNuvA" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_gHGNLlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNL1z6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNMFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_gHGNyFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNMVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNMlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNM1z6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNNFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNNVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNNlz6EfCbCMAxoqNuvA" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNN1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNOFz6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_gHGN3Fz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNOVz6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_gHGNOlz6EfCbCMAxoqNuvA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_gHGNO1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_gHGNxlz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNPFz6EfCbCMAxoqNuvA" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_gHGNzlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNPVz6EfCbCMAxoqNuvA" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_gHGNPlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_gHGNxlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNP1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_gHGNQFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_gHGN11z6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNQVz6EfCbCMAxoqNuvA" elementId="title" name="Title"/>
    <parameters xmi:id="_gHGNQlz6EfCbCMAxoqNuvA" elementId="message" name="Message"/>
    <parameters xmi:id="_gHGNQ1z6EfCbCMAxoqNuvA" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_gHGNRFz6EfCbCMAxoqNuvA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_gHGNRVz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNRlz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNR1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNSFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_gHGN3Vz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNSVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNSlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNS1z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNTFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNTVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_gHGNwFz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNTlz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_gHGN1lz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNT1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_gHGNw1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNUFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_gHGNy1z6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNUVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNUlz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.common.mx.manageembeddedsoftwarepackages" commandName="Manage Embedded Software Packages" description="Manage Embedded Software Packages" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNU1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_gHGNxlz6EfCbCMAxoqNuvA">
    <parameters xmi:id="_gHGNVFz6EfCbCMAxoqNuvA" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_gHGNVVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_gHGNwVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNVlz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_gHGNyVz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNV1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet/com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.action.TerminateAndRelaunch" commandName="&#x7ec8;&#x6b62;&#x5e76;&#x91cd;&#x65b0;&#x542f;&#x52a8;" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNWFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.informationcenter.actionSet3/com.st.stm32cube.ide.mcu.informationcenter.action1" commandName="Information Center" description="Information Center" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNWVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNWlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNW1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNXFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNXVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNXlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNX1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNYFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNYVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNYlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNY1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNZFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNZVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNZlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNZ1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNaFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNaVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNalz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNa1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNbFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNbVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNblz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNb1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNcFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNcVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNclz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNc1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNdFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNdVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNdlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNd1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNeFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNeVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNelz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNe1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNfFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNfVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNflz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNf1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNgFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNgVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNglz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNg1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNhFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNhVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNhlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNh1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNiFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNiVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNilz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNi1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNjFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNjVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNjlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNj1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNkFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNkVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNklz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNk1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNlFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNlVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNllz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNl1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNmFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNmVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNmlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNm1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNnFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNnVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNnlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNn1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNoFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNoVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNolz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNo1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNpFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNpVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNplz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNp1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNqFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNqVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNqlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNq1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNrFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNrVz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNrlz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNr1z6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <commands xmi:id="_gHGNsFz6EfCbCMAxoqNuvA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_gHGN3Fz6EfCbCMAxoqNuvA"/>
  <addons xmi:id="_gHGNsVz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_gHGNslz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_gHGNs1z6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_gHGNtFz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_gHGNtVz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_gHGNtlz6EfCbCMAxoqNuvA" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_gHGNt1z6EfCbCMAxoqNuvA" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_gHGNuFz6EfCbCMAxoqNuvA" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_gHGNuVz6EfCbCMAxoqNuvA" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_gHGNulz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_gHGNu1z6EfCbCMAxoqNuvA" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_gHGNvFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_gHGNvVz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_gHGNvlz6EfCbCMAxoqNuvA" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_gHGNv1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_gHGNwFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_gHGNwVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_gHGNwlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_gHGNw1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_gHGNxFz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_gHGNxVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_gHGNxlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_gHGNx1z6EfCbCMAxoqNuvA" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_gHGNyFz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_gHGNyVz6EfCbCMAxoqNuvA" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_gHGNylz6EfCbCMAxoqNuvA" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_gHGNy1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_gHGNzFz6EfCbCMAxoqNuvA" elementId="com.st.stm32cube.ide.mcu.debug.launch.restartCategory" name="Restart Category"/>
  <categories xmi:id="_gHGNzVz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_gHGNzlz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_gHGNz1z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_gHGN0Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_gHGN0Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_gHGN0lz6EfCbCMAxoqNuvA" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_gHGN01z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_gHGN1Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_gHGN1Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_gHGN1lz6EfCbCMAxoqNuvA" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_gHGN11z6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_gHGN2Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_gHGN2Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_gHGN2lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_gHGN21z6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_gHGN3Fz6EfCbCMAxoqNuvA" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_gHGN3Vz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_gHGN3lz6EfCbCMAxoqNuvA" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
