<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_Ob7HMFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_Ob7uQFtUEfC4Pa9wYyRkCw" bindingContexts="_Ob7uSVtUEfC4Pa9wYyRkCw">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;WDHW/Core/Src/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_dac_ex.c&quot; tooltip=&quot;WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;system_stm32f3xx.c&quot; tooltip=&quot;WDHW/Core/Src/system_stm32f3xx.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Core/Src/system_stm32f3xx.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;audio_process.c&quot; tooltip=&quot;WDHW/Core/Src/audio_process.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/Core/Src/audio_process.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;WDHW.ioc&quot; tooltip=&quot;WDHW/WDHW.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/WDHW/WDHW.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;ZKSD Motor.ioc&quot; tooltip=&quot;ZKSD Motor/ZKSD Motor.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/ZKSD Motor.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;startup_stm32h745zitx.s&quot; tooltip=&quot;STMH_CM4/Application/Startup/startup_stm32h745zitx.s&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH_CM4/Application/Startup/startup_stm32h745zitx.s&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;startup_stm32h745zitx.s&quot; tooltip=&quot;STMH_CM7/Application/Startup/startup_stm32h745zitx.s&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH_CM7/Application/Startup/startup_stm32h745zitx.s&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;STMH_CM7/Application/User/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH_CM7/Application/User/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;system_stm32h7xx_dualcore_boot_cm4_cm7.c&quot; tooltip=&quot;STMH/Drivers/CMSIS/system_stm32h7xx_dualcore_boot_cm4_cm7.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH/Drivers/CMSIS/system_stm32h7xx_dualcore_boot_cm4_cm7.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;STMH.ioc&quot; tooltip=&quot;STMH/STMH.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/STMH/STMH.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_dma.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dma.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dma.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;core_cm4.h&quot; tooltip=&quot;ZKSD Motor/Drivers/CMSIS/Include/core_cm4.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/CMSIS/Include/core_cm4.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_gpio.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_gpio.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_gpio.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_adc_ex.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc_ex.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc_ex.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_adc.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;MotorCubeMx/Core/Src/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/MotorCubeMx/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.st.stm32cube.common.mx.startCubeMx&quot; name=&quot;MotorCubeMx.ioc&quot; tooltip=&quot;MotorCubeMx/MotorCubeMx.ioc&quot;>&#xD;&#xA;&lt;persistable path=&quot;/MotorCubeMx/MotorCubeMx.ioc&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;adc.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/adc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/adc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_it.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/stm32f3xx_it.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/stm32f3xx_it.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;cmsis_gcc.h&quot; tooltip=&quot;ZKSD Motor/Drivers/CMSIS/Include/cmsis_gcc.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/CMSIS/Include/cmsis_gcc.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_msp.c&quot; tooltip=&quot;ZKSD Motor/Core/Src/stm32f3xx_hal_msp.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Src/stm32f3xx_hal_msp.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_cortex.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_cortex.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_cortex.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_rcc.c&quot; tooltip=&quot;ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_rcc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_rcc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.asm.AsmEditor&quot; name=&quot;startup_stm32f303cctx.s&quot; tooltip=&quot;ZKSD Motor/Core/Startup/startup_stm32f303cctx.s&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZKSD Motor/Core/Startup/startup_stm32f303cctx.s&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_Ob7uQFtUEfC4Pa9wYyRkCw" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_OnSpsltUEfC4Pa9wYyRkCw" x="445" y="-28" width="1050" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1751907777404"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_OnSpsltUEfC4Pa9wYyRkCw" selectedElement="_OnSps1tUEfC4Pa9wYyRkCw" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_OnSps1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_Op-xUFtUEfC4Pa9wYyRkCw">
        <children xsi:type="advanced:Perspective" xmi:id="_Op-xUFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_Op-xUVtUEfC4Pa9wYyRkCw" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_Op-xUVtUEfC4Pa9wYyRkCw" selectedElement="_Op-xVltUEfC4Pa9wYyRkCw" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_Op-xUltUEfC4Pa9wYyRkCw" elementId="topLeft" containerData="2500" selectedElement="_Op-xU1tUEfC4Pa9wYyRkCw">
              <children xsi:type="advanced:Placeholder" xmi:id="_Op-xU1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_Op540FtUEfC4Pa9wYyRkCw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Op-xVFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_Op6f4FtUEfC4Pa9wYyRkCw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_Op-xVVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_Op6f4VtUEfC4Pa9wYyRkCw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_Op-xVltUEfC4Pa9wYyRkCw" containerData="7500" selectedElement="_Op-xV1tUEfC4Pa9wYyRkCw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_Op-xV1tUEfC4Pa9wYyRkCw" containerData="7500" selectedElement="_Op-xWFtUEfC4Pa9wYyRkCw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_Op-xWFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_Op21gFtUEfC4Pa9wYyRkCw"/>
                <children xsi:type="basic:PartStack" xmi:id="_Op-xWVtUEfC4Pa9wYyRkCw" elementId="topRight" containerData="2500" selectedElement="_Op-xWltUEfC4Pa9wYyRkCw">
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xWltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ContentOutline" ref="_Op7G81tUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xW1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_Op88IFtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xXFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_Op-KQFtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_Op-xXVtUEfC4Pa9wYyRkCw" containerData="2500" selectedElement="_Op-xXltUEfC4Pa9wYyRkCw" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_Op-xXltUEfC4Pa9wYyRkCw" elementId="bottom" containerData="5000" selectedElement="_Op-xYVtUEfC4Pa9wYyRkCw">
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xX1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProblemView" ref="_Op6f4ltUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xYFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.TaskList" ref="_Op7G8FtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xYVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" ref="_Op7G8VtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xYltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.PropertySheet" ref="_Op7G8ltUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_Op-xY1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.viewMStack" containerData="5000" selectedElement="_Op-xZFtUEfC4Pa9wYyRkCw">
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xZFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" ref="_Op-KQVtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xZVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" ref="_Op-KQltUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_Op-xZltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" ref="_Op-KQ1tUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_WaXkcFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.perspective" selectedElement="_WaXkcVtUEfC4Pa9wYyRkCw" label="Device Configuration Tool" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_WaXkcVtUEfC4Pa9wYyRkCw" selectedElement="_WaXkdFtUEfC4Pa9wYyRkCw" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_WaXkcltUEfC4Pa9wYyRkCw" elementId="left" containerData="1818" selectedElement="_WaXkc1tUEfC4Pa9wYyRkCw">
              <children xsi:type="advanced:Placeholder" xmi:id="_WaXkc1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_Op540FtUEfC4Pa9wYyRkCw" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_WaXkdFtUEfC4Pa9wYyRkCw" containerData="8182" selectedElement="_4JOmkFtmEfC4Pa9wYyRkCw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_4JOmkFtmEfC4Pa9wYyRkCw" containerData="5000" selectedElement="_WaXkdVtUEfC4Pa9wYyRkCw">
                <children xsi:type="advanced:Placeholder" xmi:id="_WaXkdVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editorss" containerData="8733" ref="_Op21gFtUEfC4Pa9wYyRkCw"/>
                <children xsi:type="basic:PartStack" xmi:id="_WaXkeFtUEfC4Pa9wYyRkCw" elementId="bottomRight" containerData="1267" selectedElement="_1RDygFtZEfC4Pa9wYyRkCw">
                  <tags>General</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_WaXkeVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.views.OutputsView" toBeRendered="false" ref="_WaW9YFtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Device Configuration Tool</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_1RDygFtZEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" ref="_Op7G8VtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_WaXkd1tUEfC4Pa9wYyRkCw" elementId="topRight" toBeRendered="false" containerData="1123"/>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_JLe1YVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_JLe1YlthEfC4Pa9wYyRkCw" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.texteditor.TemplatesView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.debuggerConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.debugsources.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.faultanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <tags>persp.editorOnboardingImageUri:platform:/plugin/org.eclipse.debug.ui/icons/full/onboarding_debug_persp.png</tags>
          <tags>persp.editorOnboardingText:Go hunt your bugs here.</tags>
          <tags>persp.editorOnboardingCommand:Find Actions$$$Ctrl+3</tags>
          <tags>persp.editorOnboardingCommand:Step Into$$$F5</tags>
          <tags>persp.editorOnboardingCommand:Step Over$$$F6</tags>
          <tags>persp.editorOnboardingCommand:Step Return$$$F7</tags>
          <tags>persp.editorOnboardingCommand:Resume$$$F8</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_JLe1YlthEfC4Pa9wYyRkCw" selectedElement="_JLe1aVthEfC4Pa9wYyRkCw" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_JLe1Y1thEfC4Pa9wYyRkCw" containerData="2500" selectedElement="_JLe1ZFthEfC4Pa9wYyRkCw" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_JLe1ZFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" containerData="5000" selectedElement="_JLe1ZVthEfC4Pa9wYyRkCw">
                <tags>org.eclipse.e4.primaryNavigationStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1ZVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.DebugView" ref="_JLYHsFthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1ZlthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_Op540FtUEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_JLe1Z1thEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1aFthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_JLdnQlthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_JLe1aVthEfC4Pa9wYyRkCw" containerData="7500" selectedElement="_JLe1eVthEfC4Pa9wYyRkCw">
              <children xsi:type="basic:PartSashContainer" xmi:id="_JLe1althEfC4Pa9wYyRkCw" containerData="7500" selectedElement="_JLe1a1thEfC4Pa9wYyRkCw" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1a1thEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editorss" containerData="5310" ref="_Op21gFtUEfC4Pa9wYyRkCw"/>
                <children xsi:type="basic:PartStack" xmi:id="_JLe1bFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="4690" selectedElement="_JLe1d1thEfC4Pa9wYyRkCw">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <tags>noFocus</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1bVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.VariableView" ref="_JLYuwlthEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1blthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.BreakpointView" ref="_JLYuw1thEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1b1thEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ExpressionView" ref="_JLYuxFthEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1cFthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_Op7G81tUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1cVthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_Op7G8ltUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1clthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_Op88IFtUEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1c1thEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_JLdAMFthEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1dFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_JLdAMVthEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1dVthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" toBeRendered="false" ref="_JLdnQVthEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1dlthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.RegisterView" ref="_JLYuwFthEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1d1thEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" ref="_JLeOU1thEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JLe1eFthEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.sfrview" ref="_JLe1YFthEfC4Pa9wYyRkCw" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Debug</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_JLe1eVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" containerData="2500" selectedElement="_JLe1elthEfC4Pa9wYyRkCw">
                <tags>Debug</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1elthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" ref="_Op7G8VtUEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                  <tags>active</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1e1thEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_Op6f4VtUEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1fFthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_JLYuwVthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1fVthEfC4Pa9wYyRkCw" elementId="org.eclipse.pde.runtime.LogView" toBeRendered="false" ref="_JLZV0FthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1flthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProblemView" ref="_Op6f4ltUEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1f1thEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_JLdnQFthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1gFthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" ref="_JLeOUFthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1gVthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" toBeRendered="false" ref="_JLeOUVthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JLe1glthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" toBeRendered="false" ref="_JLeOUlthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_JTaCUVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.MemoryView" ref="_JTaCUFthEfC4Pa9wYyRkCw" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_OnSptFtUEfC4Pa9wYyRkCw" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_OnSptVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_OnSCoFtUEfC4Pa9wYyRkCw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_OnSptltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_OnSpsFtUEfC4Pa9wYyRkCw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_OnSpt1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_OnSpsVtUEfC4Pa9wYyRkCw" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_OnSCoFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_OnSpsFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Information Center" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;file:///C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/configuration/org.eclipse.osgi/73/0/.cp/welcome/index.html&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Oy8gEFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Oy8gEVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_OnSpsVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_Op21gFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editorss" selectedElement="_Op21gVtUEfC4Pa9wYyRkCw">
      <children xsi:type="basic:PartStack" xmi:id="_Op21gVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_PGu-QFySEfC2q9u24cEywg">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <children xsi:type="basic:Part" xmi:id="_PGu-QFySEfC2q9u24cEywg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;WDHW/Core/Src/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Core/Src/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;455&quot; selectionTopPixel=&quot;200&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>active</tags>
          <tags>activeOnClose</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_kB1PcFyjEfC2q9u24cEywg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="audio_process.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;audio_process.c&quot; partName=&quot;audio_process.c&quot; title=&quot;audio_process.c&quot; tooltip=&quot;WDHW/Core/Src/audio_process.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Core/Src/audio_process.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;775&quot; selectionTopPixel=&quot;280&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_h9HJMFykEfC2q9u24cEywg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="system_stm32f3xx.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;system_stm32f3xx.c&quot; partName=&quot;system_stm32f3xx.c&quot; title=&quot;system_stm32f3xx.c&quot; tooltip=&quot;WDHW/Core/Src/system_stm32f3xx.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Core/Src/system_stm32f3xx.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;7099&quot; selectionTopPixel=&quot;3600&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_T-UEUFyqEfC2q9u24cEywg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="stm32f3xx_hal_dac_ex.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stm32f3xx_hal_dac_ex.c&quot; partName=&quot;stm32f3xx_hal_dac_ex.c&quot; title=&quot;stm32f3xx_hal_dac_ex.c&quot; tooltip=&quot;WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/WDHW/Drivers/STM32F3xx_HAL_Driver/Src/stm32f3xx_hal_dac_ex.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;5012&quot; selectionTopPixel=&quot;2240&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op540FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Or-8cFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Or-8cVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op6f4FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op6f4VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op6f4ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;350&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_OulkgFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_OulkgVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op7G8FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;350&quot; org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.priorityField=&quot;35&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_O0fYMFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_O0fYMVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op7G8VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_O00IUFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_O00IUVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op7G8ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_O1EnAFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_O1EnAVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op7G81tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_OuHDYFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_OuHDYVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op88IFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op-KQFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view horizontalPosition=&quot;0&quot; verticalPosition=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
      <menus xmi:id="_O1alQFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.views.MakeView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_O1alQVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.views.MakeView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op-KQVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_OvHI8FtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_OvHI8VtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op-KQltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_O10N4FtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_O10N4VtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Op-KQ1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_O2JlEFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_O2JlEVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_WaW9YFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.views.OutputsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
      <tags>View</tags>
      <tags>categoryTag:Device Configuration Tool</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLYHsFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_JMKK0FthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JMKK0VthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLYuwFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_JUDikFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JUDikVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLYuwVthEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLYuwlthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_JNV2kFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JNV2kVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLYuw1thEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_JS0McFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JS0McVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLYuxFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_JSSA8FthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JSSoAFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLZV0FthEfC4Pa9wYyRkCw" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLdAMFthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLdAMVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLdnQFthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_N-xlQFthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.executablesView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_N-xlQVthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.executablesView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLdnQVthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLdnQlthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLeOUFthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_JUau8FthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JUau8VthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLeOUVthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLeOUlthEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLeOU1thEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="&#x73b0;&#x573a;&#x8868;&#x8fbe;&#x5f0f;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_KGV7IFthEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_KGV7IVthEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JLe1YFthEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.sfrview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_N_U-4FthEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.sfrview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_N_U-4VthEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.sfrview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JTaCUFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_JTeTwFthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JTeTwVthEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_Ob7uQVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_OoSvQFtUEfC4Pa9wYyRkCw" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_OoSvQVtUEfC4Pa9wYyRkCw" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoTWUFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_uF-XQFyKEfC2q9u24cEywg" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_OcLmSFtUEfC4Pa9wYyRkCw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoTWUVtUEfC4Pa9wYyRkCw" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_OoTWUltUEfC4Pa9wYyRkCw" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoTWU1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_uF--UFyKEfC2q9u24cEywg" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" command="_OcLmNltUEfC4Pa9wYyRkCw"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_uF--UVyKEfC2q9u24cEywg" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_OcMNSltUEfC4Pa9wYyRkCw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoTWVFtUEfC4Pa9wYyRkCw" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_OoTWVVtUEfC4Pa9wYyRkCw" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Oqg80FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_Oq5XUFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OqtKEFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OqZBAFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoTWVltUEfC4Pa9wYyRkCw" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_OoTWV1tUEfC4Pa9wYyRkCw" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoTWWFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_uF_lYFyKEfC2q9u24cEywg" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_OcLl7FtUEfC4Pa9wYyRkCw"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoT9YFtUEfC4Pa9wYyRkCw" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_OoT9YVtUEfC4Pa9wYyRkCw" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoT9YltUEfC4Pa9wYyRkCw" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_OoT9Y1tUEfC4Pa9wYyRkCw" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_OoT9ZFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Oo6aUFtUEfC4Pa9wYyRkCw" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Oo7BYFtUEfC4Pa9wYyRkCw" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_Ob7uQltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_Ob7uQ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Ob7uRFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Ob7uRVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_Ob7uRltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_OzujMFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_Ob7uR1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_Ob7uSFtUEfC4Pa9wYyRkCw" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_Ob7uSVtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXMG1tUEfC4Pa9wYyRkCw" keySequence="CTRL+1" command="_OcLl5VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzJFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+L" command="_OcMM_1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaM1tUEfC4Pa9wYyRkCw" keySequence="CTRL+V" command="_OcKX-FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaRFtUEfC4Pa9wYyRkCw" keySequence="CTRL+A" command="_OcMNHFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaS1tUEfC4Pa9wYyRkCw" keySequence="CTRL+C" command="_OcKXz1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBQ1tUEfC4Pa9wYyRkCw" keySequence="CTRL+X" command="_OcLmPltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBRFtUEfC4Pa9wYyRkCw" keySequence="CTRL+Y" command="_OcMNSltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBR1tUEfC4Pa9wYyRkCw" keySequence="CTRL+Z" command="_OcLmNltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBXltUEfC4Pa9wYyRkCw" keySequence="ALT+PAGE_UP" command="_OcMNYFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBX1tUEfC4Pa9wYyRkCw" keySequence="ALT+PAGE_DOWN" command="_OcK-81tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBYltUEfC4Pa9wYyRkCw" keySequence="SHIFT+INSERT" command="_OcKX-FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBZ1tUEfC4Pa9wYyRkCw" keySequence="ALT+F11" command="_OcKYOFtUEfC4Pa9wYyRkCw">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_OcZoZVtUEfC4Pa9wYyRkCw" keySequence="CTRL+F10" command="_OcKYGltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoaltUEfC4Pa9wYyRkCw" keySequence="CTRL+INSERT" command="_OcKXz1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZodltUEfC4Pa9wYyRkCw" keySequence="CTRL+PAGE_UP" command="_OcLmY1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZod1tUEfC4Pa9wYyRkCw" keySequence="CTRL+PAGE_DOWN" command="_OcLl7VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoeVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+F3" command="_OcLmVVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZofltUEfC4Pa9wYyRkCw" keySequence="SHIFT+DEL" command="_OcLmPltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPaFtUEfC4Pa9wYyRkCw" keySequence="ALT+/" command="_OcLmHVtUEfC4Pa9wYyRkCw">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_OcV98FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.textEditorScope" bindingContext="_OcOCIltUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcWlAFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+CR" command="_OcLmU1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMEVtUEfC4Pa9wYyRkCw" keySequence="CTRL+BS" command="_OcKXxltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMGltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+Q" command="_OcK_IltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzIFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+J" command="_OcK_EVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzIltUEfC4Pa9wYyRkCw" keySequence="CTRL++" command="_OcKYNltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzJ1tUEfC4Pa9wYyRkCw" keySequence="CTRL+-" command="_OcMNAltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzQVtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+J" command="_OcK_L1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzSVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+A" command="_OcKX7VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaOFtUEfC4Pa9wYyRkCw" keySequence="CTRL+J" command="_OcKYIFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaPFtUEfC4Pa9wYyRkCw" keySequence="CTRL+L" command="_OcLmMVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaT1tUEfC4Pa9wYyRkCw" keySequence="CTRL+D" command="_OcKYKltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaW1tUEfC4Pa9wYyRkCw" keySequence="CTRL+=" command="_OcKYNltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBQFtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+/" command="_OcMNDVtUEfC4Pa9wYyRkCw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_OcZBQVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Y" command="_OcJwsVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBSltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+DEL" command="_OcLmIltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBS1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+X" command="_OcKX2ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBTFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+Y" command="_OcMNAVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBUFtUEfC4Pa9wYyRkCw" keySequence="CTRL+DEL" command="_OcLmM1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBVVtUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_UP" command="_OcMNXFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBV1tUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_DOWN" command="_OcK-_1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBYFtUEfC4Pa9wYyRkCw" keySequence="SHIFT+END" command="_OcMNCFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBaFtUEfC4Pa9wYyRkCw" keySequence="SHIFT+HOME" command="_OcLmfVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBbltUEfC4Pa9wYyRkCw" keySequence="END" command="_OcLmb1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBb1tUEfC4Pa9wYyRkCw" keySequence="INSERT" command="_OcKYEltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBcVtUEfC4Pa9wYyRkCw" keySequence="F2" command="_OcLl71tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoUltUEfC4Pa9wYyRkCw" keySequence="HOME" command="_OcMNCltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoVVtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+ARROW_UP" command="_OcMNN1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoVltUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+ARROW_DOWN" command="_OcMNKVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoWVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+INSERT" command="_OcK-91tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoXltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_OcMNC1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoX1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_OcK_AltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoZltUEfC4Pa9wYyRkCw" keySequence="CTRL+F10" command="_OcLmT1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoaFtUEfC4Pa9wYyRkCw" keySequence="CTRL+END" command="_OcK_BVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZocltUEfC4Pa9wYyRkCw" keySequence="CTRL+ARROW_UP" command="_OcK-21tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoc1tUEfC4Pa9wYyRkCw" keySequence="CTRL+ARROW_DOWN" command="_OcMNdFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZodFtUEfC4Pa9wYyRkCw" keySequence="CTRL+ARROW_LEFT" command="_OcKXx1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZodVtUEfC4Pa9wYyRkCw" keySequence="CTRL+ARROW_RIGHT" command="_OcK_H1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoeFtUEfC4Pa9wYyRkCw" keySequence="CTRL+HOME" command="_OcKX91tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoeltUEfC4Pa9wYyRkCw" keySequence="CTRL+NUMPAD_MULTIPLY" command="_OcK_IVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoe1tUEfC4Pa9wYyRkCw" keySequence="CTRL+NUMPAD_ADD" command="_OcMNJFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZofFtUEfC4Pa9wYyRkCw" keySequence="CTRL+NUMPAD_SUBTRACT" command="_OcLmUVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZofVtUEfC4Pa9wYyRkCw" keySequence="CTRL+NUMPAD_DIVIDE" command="_OcK-3ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZogltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_OcK_LVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZohFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_OcKYE1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPaltUEfC4Pa9wYyRkCw" keySequence="SHIFT+CR" command="_OcMNCVtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcWlAVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_OcOCNFtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXMEFtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+SHIFT+C" command="_OcMNdltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMEltUEfC4Pa9wYyRkCw" keySequence="CTRL+TAB" command="_OcMNcFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMFVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+P" command="_OcLmcVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMIVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+T" command="_OcLmBltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMKFtUEfC4Pa9wYyRkCw" keySequence="CTRL+7" command="_OcKX-1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMLVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+H" command="_OcKYI1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzKFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+N" command="_OcK_HltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzLFtUEfC4Pa9wYyRkCw" keySequence="CTRL+/" command="_OcKX-1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzLVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+O" command="_OcLmTltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzLltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+A" command="_OcLmVltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzMFtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+S" command="_OcMNUFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzMVtUEfC4Pa9wYyRkCw" keySequence="CTRL+#" command="_OcLl-1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzMltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+C" command="_OcKX-1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzNVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+F" command="_OcMNcVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzNltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+G" command="_OcKXwVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzOVtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+H" command="_OcLl6FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzO1tUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+I" command="_OcKX_1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaMVtUEfC4Pa9wYyRkCw" keySequence="CTRL+T" command="_OcK_CltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaN1tUEfC4Pa9wYyRkCw" keySequence="CTRL+I" command="_OcK_D1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaQVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+/" command="_OcMNI1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaQltUEfC4Pa9wYyRkCw" keySequence="CTRL+O" command="_OcKX4ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaRVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+R" command="_OcLmJ1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaSFtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+S" command="_OcKYG1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaTFtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+T" command="_OcLl_ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaVFtUEfC4Pa9wYyRkCw" keySequence="CTRL+G" command="_OcMNRVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaWFtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+L" command="_OcMNK1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaWVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+M" command="_OcKX7ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaWltUEfC4Pa9wYyRkCw" keySequence="CTRL+=" command="_OcLl-1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaXVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+O" command="_OcLmQFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBQltUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Z" command="_OcLmQVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBT1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+\" command="_OcKX_FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBc1tUEfC4Pa9wYyRkCw" keySequence="F3" command="_OcMNeltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBeVtUEfC4Pa9wYyRkCw" keySequence="F4" command="_OcMNHVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoXFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+ARROW_UP" command="_OcLmK1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoXVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_OcK-9VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoYVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+ARROW_UP" command="_OcMM8FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoY1tUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+ARROW_DOWN" command="_OcMNcltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoZFtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+ARROW_LEFT" command="_OcLmHltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoZ1tUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_OcMM91tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoiFtUEfC4Pa9wYyRkCw" keySequence="ALT+C" command="_OcKX_VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPaVtUEfC4Pa9wYyRkCw" keySequence="SHIFT+TAB" command="_OcKYJVtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcXME1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.contexts.window" bindingContext="_Ob7uSltUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXMFFtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+SHIFT+L" command="_OcKYBVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMGFtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q O" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXMGVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_OcXMHFtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+B" command="_OcK-6ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMHVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+R" command="_OcMNeFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMHltUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q Q" command="_OcK-4ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMH1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+S" command="_OcKYL1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMIFtUEfC4Pa9wYyRkCw" keySequence="CTRL+3" command="_OcLl7ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMJFtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q S" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXMJVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_OcXMKVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q V" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXMKltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_OcXMK1tUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+G" command="_OcK-1FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMLFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+W" command="_OcLmPVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXML1tUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q H" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXMMFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_OcXzIVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+K" command="_OcK-1VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzI1tUEfC4Pa9wYyRkCw" keySequence="CTRL+," command="_OcKX_ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzJVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q L" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXzJltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_OcXzKVtUEfC4Pa9wYyRkCw" keySequence="CTRL+." command="_OcMNOltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzL1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+B" command="_OcK-1ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzNFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+E" command="_OcK-8VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzPVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q X" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXzPltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_OcXzP1tUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q Y" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXzQFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_OcXzQltUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q Z" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcXzQ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_OcXzR1tUEfC4Pa9wYyRkCw" keySequence="CTRL+P" command="_OcLmSFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzSFtUEfC4Pa9wYyRkCw" keySequence="CTRL+Q" command="_OcLmXVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaMFtUEfC4Pa9wYyRkCw" keySequence="CTRL+S" command="_OcMNA1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaNVtUEfC4Pa9wYyRkCw" keySequence="CTRL+W" command="_OcMNVVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaNltUEfC4Pa9wYyRkCw" keySequence="CTRL+H" command="_OcLmG1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaOVtUEfC4Pa9wYyRkCw" keySequence="CTRL+K" command="_OcK-7FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaPVtUEfC4Pa9wYyRkCw" keySequence="CTRL+M" command="_OcLmFFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaQFtUEfC4Pa9wYyRkCw" keySequence="CTRL+N" command="_OcMNUVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaQ1tUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+?" command="_OcLmB1tUEfC4Pa9wYyRkCw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_OcYaR1tUEfC4Pa9wYyRkCw" keySequence="CTRL+B" command="_OcKYAltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaSVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q B" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcYaSltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_OcYaTVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+Q C" command="_OcK-4ltUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcYaTltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_OcYaUFtUEfC4Pa9wYyRkCw" keySequence="CTRL+E" command="_OcLmL1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaUVtUEfC4Pa9wYyRkCw" keySequence="CTRL+F" command="_OcKYNVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaV1tUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+W" command="_OcMNNltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaXFtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+N" command="_OcLmOVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBSFtUEfC4Pa9wYyRkCw" keySequence="CTRL+_" command="_OcLmDFtUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcZBSVtUEfC4Pa9wYyRkCw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_OcZBTVtUEfC4Pa9wYyRkCw" keySequence="CTRL+{" command="_OcLmDFtUEfC4Pa9wYyRkCw">
      <parameters xmi:id="_OcZBTltUEfC4Pa9wYyRkCw" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_OcZBVltUEfC4Pa9wYyRkCw" keySequence="SHIFT+F9" command="_OcLmS1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBWltUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_LEFT" command="_OcKYHltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBXVtUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_RIGHT" command="_OcLmVFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBZFtUEfC4Pa9wYyRkCw" keySequence="SHIFT+F5" command="_OcMNMVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBZltUEfC4Pa9wYyRkCw" keySequence="ALT+F7" command="_OcKX-VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBa1tUEfC4Pa9wYyRkCw" keySequence="F9" command="_OcLmdVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBbFtUEfC4Pa9wYyRkCw" keySequence="F11" command="_OcMNGVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBbVtUEfC4Pa9wYyRkCw" keySequence="F12" command="_OcLmIFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBcFtUEfC4Pa9wYyRkCw" keySequence="F2" command="_OcKYAFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBfFtUEfC4Pa9wYyRkCw" keySequence="F5" command="_OcLmcFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoU1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+F7" command="_OcMNGltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoVFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+F8" command="_OcLmCltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoV1tUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+ARROW_LEFT" command="_OcLmXVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoWFtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+ARROW_RIGHT" command="_OcK-4FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoWltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+F4" command="_OcLmPVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoW1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+F6" command="_OcKYNFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoYFtUEfC4Pa9wYyRkCw" keySequence="CTRL+F7" command="_OcKX0FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoYltUEfC4Pa9wYyRkCw" keySequence="CTRL+F8" command="_OcLl51tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZobltUEfC4Pa9wYyRkCw" keySequence="CTRL+F4" command="_OcMNVVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZocFtUEfC4Pa9wYyRkCw" keySequence="CTRL+F6" command="_OcKYPFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZocVtUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+F7" command="_OcK_CVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZogVtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_OcKYLltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZog1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_OcLmD1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZohVtUEfC4Pa9wYyRkCw" keySequence="DEL" command="_OcK-0ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZojltUEfC4Pa9wYyRkCw" keySequence="ALT+?" command="_OcLmB1tUEfC4Pa9wYyRkCw">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_OcaPZ1tUEfC4Pa9wYyRkCw" keySequence="ALT+-" command="_OcKX5VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPa1tUEfC4Pa9wYyRkCw" keySequence="ALT+CR" command="_OcLl_FtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcXMFltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_OcOCK1tUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXMF1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+P" command="_OcMM9FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzOFtUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+G" command="_OcLmbltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBeFtUEfC4Pa9wYyRkCw" keySequence="F3" command="_OcLmXltUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcXMIltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_OcOCNVtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXMI1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+T" command="_OcLmBltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXMLltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+H" command="_OcKYI1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzN1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+G" command="_OcKXwVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzOltUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+H" command="_OcLl6FtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzPFtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+I" command="_OcKX_1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaRltUEfC4Pa9wYyRkCw" keySequence="ALT+SHIFT+R" command="_OcLmJ1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaVVtUEfC4Pa9wYyRkCw" keySequence="CTRL+G" command="_OcMNRVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBdltUEfC4Pa9wYyRkCw" keySequence="F3" command="_OcMNeltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBeltUEfC4Pa9wYyRkCw" keySequence="F4" command="_OcMNHVtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcXMJltUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_OcOCIFtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXMJ1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+V" command="_OcMM81tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzM1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+C" command="_OcLl81tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBVFtUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_UP" command="_OcKXxVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBXFtUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_RIGHT" command="_OcMNFltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBYVtUEfC4Pa9wYyRkCw" keySequence="SHIFT+INSERT" command="_OcMM81tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoaVtUEfC4Pa9wYyRkCw" keySequence="CTRL+INSERT" command="_OcLl81tUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcXzKltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_OcOCM1tUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXzK1tUEfC4Pa9wYyRkCw" keySequence="CTRL+/" command="_OcK-51tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBcltUEfC4Pa9wYyRkCw" keySequence="F3" command="_OcKYKFtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcXzRFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_OcOCKltUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXzRVtUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+M" command="_OcMNG1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcXzRltUEfC4Pa9wYyRkCw" keySequence="ALT+CTRL+N" command="_OcMNKFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaMltUEfC4Pa9wYyRkCw" keySequence="CTRL+T" command="_OcLl9ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaNFtUEfC4Pa9wYyRkCw" keySequence="CTRL+W" command="_OcKYGFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaP1tUEfC4Pa9wYyRkCw" keySequence="CTRL+N" command="_OcKYOVtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcXzSltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.debugging" bindingContext="_OcOCLFtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcXzS1tUEfC4Pa9wYyRkCw" keySequence="CTRL+R" command="_OcKX1ltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBaVtUEfC4Pa9wYyRkCw" keySequence="F7" command="_OcMNQVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBaltUEfC4Pa9wYyRkCw" keySequence="F8" command="_OcKYDltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBe1tUEfC4Pa9wYyRkCw" keySequence="F5" command="_OcKYD1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoUFtUEfC4Pa9wYyRkCw" keySequence="F6" command="_OcMNEVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZobVtUEfC4Pa9wYyRkCw" keySequence="CTRL+F2" command="_OcLmI1tUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcYaOltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_OcOCLVtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcYaO1tUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+," command="_OcLmX1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaPltUEfC4Pa9wYyRkCw" keySequence="CTRL+SHIFT+." command="_OcLmEVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcYaVltUEfC4Pa9wYyRkCw" keySequence="CTRL+G" command="_OcLmEltUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcYaUltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_OcOCMFtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcYaU1tUEfC4Pa9wYyRkCw" keySequence="CTRL+G" command="_OcMNTVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoUVtUEfC4Pa9wYyRkCw" keySequence="HOME" command="_OcLl8VtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcZBRVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.console" bindingContext="_OcOCJFtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcZBRltUEfC4Pa9wYyRkCw" keySequence="CTRL+Z" command="_OcMNNFtUEfC4Pa9wYyRkCw">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_OcZBUVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_OcOCNltUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcZBUltUEfC4Pa9wYyRkCw" keySequence="SHIFT+F7" command="_OcKYJ1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBU1tUEfC4Pa9wYyRkCw" keySequence="SHIFT+F8" command="_OcLmCVtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBY1tUEfC4Pa9wYyRkCw" keySequence="SHIFT+F5" command="_OcMNEltUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBZVtUEfC4Pa9wYyRkCw" keySequence="SHIFT+F6" command="_OcKYA1tUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZob1tUEfC4Pa9wYyRkCw" keySequence="CTRL+F5" command="_OcMNDltUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcZBWFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_OcOCJ1tUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcZBWVtUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_LEFT" command="_OcK_DFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBW1tUEfC4Pa9wYyRkCw" keySequence="ALT+ARROW_RIGHT" command="_OcK_KFtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZBd1tUEfC4Pa9wYyRkCw" keySequence="F3" command="_OcMNeltUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcZBdFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_OcOCKVtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcZBdVtUEfC4Pa9wYyRkCw" keySequence="F3" command="_OcMNeltUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcZoa1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_OcOCKFtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcZobFtUEfC4Pa9wYyRkCw" keySequence="CTRL+INSERT" command="_OcKYLVtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcZof1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_OcOCMltUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcZogFtUEfC4Pa9wYyRkCw" keySequence="ALT+Y" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZohltUEfC4Pa9wYyRkCw" keySequence="ALT+A" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoh1tUEfC4Pa9wYyRkCw" keySequence="ALT+B" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoiVtUEfC4Pa9wYyRkCw" keySequence="ALT+C" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoiltUEfC4Pa9wYyRkCw" keySequence="ALT+D" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoi1tUEfC4Pa9wYyRkCw" keySequence="ALT+E" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZojFtUEfC4Pa9wYyRkCw" keySequence="ALT+F" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZojVtUEfC4Pa9wYyRkCw" keySequence="ALT+G" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZoj1tUEfC4Pa9wYyRkCw" keySequence="ALT+P" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZokFtUEfC4Pa9wYyRkCw" keySequence="ALT+R" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZokVtUEfC4Pa9wYyRkCw" keySequence="ALT+S" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcZokltUEfC4Pa9wYyRkCw" keySequence="ALT+T" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPYFtUEfC4Pa9wYyRkCw" keySequence="ALT+V" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPYVtUEfC4Pa9wYyRkCw" keySequence="ALT+W" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPYltUEfC4Pa9wYyRkCw" keySequence="ALT+H" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPZVtUEfC4Pa9wYyRkCw" keySequence="ALT+L" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
    <bindings xmi:id="_OcaPZltUEfC4Pa9wYyRkCw" keySequence="ALT+N" command="_OcKX4VtUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_OcaPY1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.context" bindingContext="_OcOCMVtUEfC4Pa9wYyRkCw">
    <bindings xmi:id="_OcaPZFtUEfC4Pa9wYyRkCw" keySequence="ALT+K" command="_OcK--ltUEfC4Pa9wYyRkCw"/>
  </bindingTables>
  <bindingTables xmi:id="_Op4DoVtUEfC4Pa9wYyRkCw" bindingContext="_Op4DoFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4Do1tUEfC4Pa9wYyRkCw" bindingContext="_Op4DoltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4DpVtUEfC4Pa9wYyRkCw" bindingContext="_Op4DpFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4Dp1tUEfC4Pa9wYyRkCw" bindingContext="_Op4DpltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4DqVtUEfC4Pa9wYyRkCw" bindingContext="_Op4DqFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4Dq1tUEfC4Pa9wYyRkCw" bindingContext="_Op4DqltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4DrVtUEfC4Pa9wYyRkCw" bindingContext="_Op4DrFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4Dr1tUEfC4Pa9wYyRkCw" bindingContext="_Op4DrltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4DsVtUEfC4Pa9wYyRkCw" bindingContext="_Op4DsFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qsFtUEfC4Pa9wYyRkCw" bindingContext="_Op4DsltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qsltUEfC4Pa9wYyRkCw" bindingContext="_Op4qsVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qtFtUEfC4Pa9wYyRkCw" bindingContext="_Op4qs1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qtltUEfC4Pa9wYyRkCw" bindingContext="_Op4qtVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4quFtUEfC4Pa9wYyRkCw" bindingContext="_Op4qt1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qultUEfC4Pa9wYyRkCw" bindingContext="_Op4quVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qvFtUEfC4Pa9wYyRkCw" bindingContext="_Op4qu1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qvltUEfC4Pa9wYyRkCw" bindingContext="_Op4qvVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qwFtUEfC4Pa9wYyRkCw" bindingContext="_Op4qv1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qwltUEfC4Pa9wYyRkCw" bindingContext="_Op4qwVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qxFtUEfC4Pa9wYyRkCw" bindingContext="_Op4qw1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qxltUEfC4Pa9wYyRkCw" bindingContext="_Op4qxVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qyFtUEfC4Pa9wYyRkCw" bindingContext="_Op4qx1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qyltUEfC4Pa9wYyRkCw" bindingContext="_Op4qyVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qzFtUEfC4Pa9wYyRkCw" bindingContext="_Op4qy1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4qzltUEfC4Pa9wYyRkCw" bindingContext="_Op4qzVtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op4q0FtUEfC4Pa9wYyRkCw" bindingContext="_Op4qz1tUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5RwVtUEfC4Pa9wYyRkCw" bindingContext="_Op5RwFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5Rw1tUEfC4Pa9wYyRkCw" bindingContext="_Op5RwltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5RxVtUEfC4Pa9wYyRkCw" bindingContext="_Op5RxFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5Rx1tUEfC4Pa9wYyRkCw" bindingContext="_Op5RxltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5RyVtUEfC4Pa9wYyRkCw" bindingContext="_Op5RyFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5Ry1tUEfC4Pa9wYyRkCw" bindingContext="_Op5RyltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5RzVtUEfC4Pa9wYyRkCw" bindingContext="_Op5RzFtUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_Op5Rz1tUEfC4Pa9wYyRkCw" bindingContext="_Op5RzltUEfC4Pa9wYyRkCw"/>
  <bindingTables xmi:id="_JOQckVthEfC4Pa9wYyRkCw" bindingContext="_JOQckFthEfC4Pa9wYyRkCw"/>
  <rootContext xmi:id="_Ob7uSVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_Ob7uSltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_Ob7uS1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_OcOCIFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_OcOCIVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_OcOCIltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_OcOCKVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_OcOCK1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_OcOCM1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_OcOCNFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_OcOCJFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_OcOCJVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_OcOCKFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_OcOCKltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_OcOCLFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_OcOCLVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_OcOCLltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mpu.debug.ui.debugging" name="Debugging C/C++ on MPU - Cortex-M" description="Debugging C/C++ Programs on MPU - Cortex-M"/>
        <children xmi:id="_OcOCL1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.ui.debugging" name="Debugging C/C++ on MCU" description="Debugging C/C++ Programs on MCU"/>
        <children xmi:id="_OcOCMFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_OcOCNltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_OcOCMltUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_OcOCNVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
    </children>
    <children xmi:id="_Ob7uTFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_OcOCJ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_OcOCI1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_OcOCJltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_OcOCMVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.context" name="Device Configuration Tool Context" description="Device Configuration Tool  Context"/>
  <rootContext xmi:id="_Op4DoFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet" name="Auto::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet"/>
  <rootContext xmi:id="_Op4DoltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.informationcenter.actionSet3" name="Auto::com.st.stm32cube.ide.mcu.informationcenter.actionSet3"/>
  <rootContext xmi:id="_Op4DpFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_Op4DpltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_Op4DqFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_Op4DqltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_Op4DrFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_Op4DrltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_Op4DsFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_Op4DsltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_Op4qsVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_Op4qs1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_Op4qtVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_Op4qt1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_Op4quVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_Op4qu1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_Op4qvVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_Op4qv1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_Op4qwVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_Op4qw1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_Op4qxVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_Op4qx1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_Op4qyVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_Op4qy1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_Op4qzVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_Op4qz1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_Op5RwFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_Op5RwltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_Op5RxFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_Op5RxltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_Op5RyFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_Op5RyltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_Op5RzFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_Op5RzltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_JOQckFthEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.launch.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::com.st.stm32cube.ide.mcu.debug.launch.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_OdkF8FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_OnDZIFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.views.OutputsView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" category="Device Configuration Tool" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
    <tags>View</tags>
    <tags>categoryTag:Device Configuration Tool</tags>
  </descriptors>
  <descriptors xmi:id="_OnEAMFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnEAMVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnEnQFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.logview" label="SWV Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.SWVLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_OnEnQVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.statisticalprofiling" label="SWV Statistical Profiling" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_statistical_profiling.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.statisticalprofiling.SWVStatisticalProfilingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_OnEnQltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.itmtrace" label="SWV ITM Data Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/console_view.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.itmtrace.SWVConsole"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_OnEnQ1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.exceptionlogview" label="SWV Exception Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_Exception_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.SWVExceptionLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_OnEnRFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceview" label="SWV Data Trace" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/insp_sbook.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatrace.SWVDataTraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1YFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.SWVDatatraceTimeline" label="SWV Data Trace Timeline Graph" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/Datatrace_timeline.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceTimeline.SWVDatatraceTimeline"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1YVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.faultanalyzer.view" label="&#x6545;&#x969c;&#x5206;&#x6790;&#x5668;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.faultanalyzer/icons/clanbomber.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.faultanalyzer.FaultAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.faultanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1YltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.freertos.queues" label="FreeRTOS Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.queues.FORtosQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1Y1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.freertos.tasklist" label="FreeRTOS Task List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.tasklist.FORtosTaskList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1ZFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.freertos.semaphore" label="FreeRTOS Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.semaphores.FORtosSemaphores"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1ZVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.freertos.timers" label="FreeRTOS Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.timers.FORtosTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1ZltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" label="&#x73b0;&#x573a;&#x8868;&#x8fbe;&#x5f0f;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnF1Z1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.sfrview" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnGccFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnGccVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.tcp.console.view" label="TCP Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.tcp.console/icons/console.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.tcp.console.ui.TCPConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.tcp.console"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_OnHDgFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.threads" label="ThreadX Thread List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.threadlist.ThreadXThreadList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHDgVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.semaphores" label="ThreadX Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.semaphores.ThreadXSemaphoreList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHDgltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.mutexes" label="ThreadX Mutexes" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.mutexes.ThreadXMutexList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHDg1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.queues" label="ThreadX Message Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.queues.ThreadXMessageQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHDhFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.eventflags" label="ThreadX Event Flags" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.eventflags.ThreadXEventFlags"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHqkFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.timer" label="ThreadX Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.timers.ThreadXTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHqkVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.blockpools" label="ThreadX Memory Block Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.blockpools.ThreadXMemoryBlockPools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHqkltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.bytepools" label="ThreadX Memory Byte Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.bytepools.ThreadXMemoryBytePools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_OnHqk1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mpu.flashmemory.programmer.view" label="Flash Memories Programmer" iconURI="platform:/plugin/com.st.stm32cube.ide.mpu.flashmemory.programmer/icons/soc.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mpu.flashmemory.programmer.views.FlashMemoryProgrammerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mpu.flashmemory.programmer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnHqlFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnHqlVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnIRoFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnIRoVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnIRoltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnIRo1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnIRpFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnIRpVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnI4sFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnI4sVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_OnJfwFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnJfwVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnKG0FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnKG0VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnKG0ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_OnKG01tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnKt4FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnKt4VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnKt4ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnLU8FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnLU8VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnLU8ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnLU81tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnLU9FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_OnLU9VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_OnL8AFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_OnL8AVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnMjEFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_OnMjEVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_OnMjEltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnMjE1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnNKIFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_OnNKIVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnNxMFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnOYQFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnOYQVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnOYQltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnOYQ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnO_UFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnO_UVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnO_UltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnPmYFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_OnPmYVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_OcJwsFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcJwsVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcJwsltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXwFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXwVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXwltUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_OcJJtltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXw1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXxFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXxVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_OcJJtFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXxltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXx1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXyFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_OcJJs1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXyVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXyltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_OcJJrVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcKXy1tUEfC4Pa9wYyRkCw" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_OcKXzFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKXzVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_OcJJvltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcKXzltUEfC4Pa9wYyRkCw" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_OcKXz1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX0FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX0VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_OcJJoltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcKX0ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_OcKX01tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX1FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX1VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX1ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX11tUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_OcJJultUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX2FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX2VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX2ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX21tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX3FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX3VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX3ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX31tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX4FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX4VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_OcJJtFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX4ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX41tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_OcJJs1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX5FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX5VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX5ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX51tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX6FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_OcJJvFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX6VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_OcJJv1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX6ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX61tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX7FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX7VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX7ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX71tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX8FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX8VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX8ltUEfC4Pa9wYyRkCw" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_OcJJqVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX81tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mpu.linux.ide.command.setupopenstlinux" commandName="Setup OpenSTLinux" description="Setup OpenSTLinux" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX9FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX9VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX9ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX91tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX-FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX-VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX-ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX-1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX_FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX_VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX_ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKX_1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYAFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYAVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYAltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYA1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_OcJJvVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYBFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYBVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_OcJJrFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYBltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYB1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_OcJJpltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYCFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.ide.command.generatecode" commandName="Generate Code" description="Generate Code (based on .ioc file content)" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYCVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYCltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYC1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYDFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_OcJJu1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYDVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_OcJJwFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYDltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYD1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYEFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYEVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYEltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYE1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYFFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYFVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYFltUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_OcJJvltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcKYF1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_OcKYGFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYGVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYGltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYG1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYHFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYHVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYHltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYH1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYIFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYIVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYIltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYI1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYJFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYJVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYJltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYJ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_OcJJvVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYKFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_OcJJtVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYKVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYKltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYK1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_OcJJultUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYLFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartConfigurationCommand" commandName="Restart Configuration Command" category="_OcJJrltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYLVtUEfC4Pa9wYyRkCw" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_OcJJqVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYLltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYL1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYMFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.externaltools.test" commandName="Test ExternalTools" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYMVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYMltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYM1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYNFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYNVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYNltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYN1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYOFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYOVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYOltUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYO1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_OcJJoFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYPFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYPVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcKYPltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-0FtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.common.ui.view_export" commandName="Export view data to file" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-0VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-0ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-01tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-1FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_OcJJtltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-1VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-1ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-11tUEfC4Pa9wYyRkCw" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_OcJJt1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-2FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-2VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_OcJJvVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK-2ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_OcK-21tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-3FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-3VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-3ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-31tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-4FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-4VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-4ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_OcJJoVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK-41tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_OcK-5FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_OcK-5VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_OcK-5ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-51tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_OcJJtVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-6FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_OcJJqFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK-6VtUEfC4Pa9wYyRkCw" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_OcK-6ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-61tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-7FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-7VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-7ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-71tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_OcJJqltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK-8FtUEfC4Pa9wYyRkCw" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_OcK-8VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-8ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-81tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-9FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-9VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-9ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-91tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK--FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_OcJJsFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK--VtUEfC4Pa9wYyRkCw" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_OcK--ltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.menu.generatecode" commandName="Generate Code" description="Generate Code" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK--1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-_FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-_VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-_ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK-_1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_AFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.datarefresh" commandName="Data Refresh" description="Data Refresh" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_AVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_AltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_A1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_BFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_BVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_BltUEfC4Pa9wYyRkCw" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_OcJJt1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_B1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_CFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mpu.flashmemory.programmer.command" commandName="Flash Memory Programmer Command" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_CVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_CltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_C1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showInCommand" commandName="name" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_DFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_DVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_DltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_D1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_EFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_EVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_EltUEfC4Pa9wYyRkCw" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_OcJJvltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK_E1tUEfC4Pa9wYyRkCw" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_OcK_FFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_FVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.pintopincompatibility" commandName="Pin to pin compatibility" description="Pin to pin compatibility" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_FltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_F1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_OcJJs1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_GFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_OcJJpltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_GVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_OcJJp1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_GltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_G1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_OcJJtltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_HFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_HVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_OcJJtltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_HltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_H1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_IFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_IVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_IltUEfC4Pa9wYyRkCw" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_I1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_JFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showstate" commandName="name" category="_OcJJvltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK_JVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_OcK_JltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_J1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_KFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_KVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_KltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.docsandresources" commandName="Docs And Resources" description="Docs And Resources" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_K1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_OcJJsFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcK_LFtUEfC4Pa9wYyRkCw" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_OcK_LVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_LltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcK_L1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl4FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl4VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl4ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl41tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_OcJJpltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl5FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl5VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl5ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_OcJJt1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl51tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl6FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl6VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl6ltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.generatereport" commandName="Generate Report" description="Generate Report" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl61tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl7FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl7VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl7ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl71tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl8FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl8VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl8ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl81tUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_OcJJtFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl9FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl9VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl9ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl91tUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl-FtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.informationcenter.tutorialvideo" commandName="Tutorial Video" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl-VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_OcJJu1tUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcLl-ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_OcLl-1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl_FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl_VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl_ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLl_1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_OcJJrVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcLmAFtUEfC4Pa9wYyRkCw" elementId="url" name="URL"/>
    <parameters xmi:id="_OcLmAVtUEfC4Pa9wYyRkCw" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_OcLmAltUEfC4Pa9wYyRkCw" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_OcLmA1tUEfC4Pa9wYyRkCw" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_OcLmBFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmBVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmBltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmB1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmCFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmCVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_OcJJvVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmCltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmC1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.toolbar.generatecode" commandName="Generate Code" description="Generate Code" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmDFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_OcJJrVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcLmDVtUEfC4Pa9wYyRkCw" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_OcLmDltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmD1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmEFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmEVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmEltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmE1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmFFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmFVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmFltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmF1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.start_trace" commandName="Start Trace" description="Start Trace" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmGFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmGVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmGltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmG1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_OcJJtltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmHFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.modifyIncludePathsBySelection" commandName="Add/remove include path..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmHVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmHltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmH1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmIFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmIVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmIltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmI1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmJFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmJVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmJltUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmJ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmKFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_OcJJwFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmKVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmKltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmK1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmLFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_OcJJultUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcLmLVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_OcLmLltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_OcLmL1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmMFtUEfC4Pa9wYyRkCw" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_OcJJqVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmMVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmMltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmM1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmNFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmNVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmNltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmN1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmOFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmOVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmOltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmO1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmPFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmPVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmPltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmP1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmQFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmQVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmQltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmQ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmRFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmRVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_OcJJqltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcLmRltUEfC4Pa9wYyRkCw" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_OcLmR1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_OcJJu1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmSFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmSVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp" commandName="Convert Project to C or CPP" category="_OcJJvltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcLmSltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp.type" name="Convert Type (C/C++)"/>
  </commands>
  <commands xmi:id="_OcLmS1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmTFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmTVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmTltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmT1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmUFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmUVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmUltUEfC4Pa9wYyRkCw" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmU1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmVFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmVVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmVltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmV1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmWFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.ide.connectionToMyST" commandName="Connection to myST" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmWVtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mpu.remote.serial.connectconsole" commandName="Connect Console Command" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmWltUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_OcJJtltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmW1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmXFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmXVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmXltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmX1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmYFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmYVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_OcJJt1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmYltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmY1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmZFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_OcJJuVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcLmZVtUEfC4Pa9wYyRkCw" elementId="title" name="Title"/>
    <parameters xmi:id="_OcLmZltUEfC4Pa9wYyRkCw" elementId="message" name="Message"/>
    <parameters xmi:id="_OcLmZ1tUEfC4Pa9wYyRkCw" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_OcLmaFtUEfC4Pa9wYyRkCw" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_OcLmaVtUEfC4Pa9wYyRkCw" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_OcLmaltUEfC4Pa9wYyRkCw" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_OcLma1tUEfC4Pa9wYyRkCw" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_OcLmbFtUEfC4Pa9wYyRkCw" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_OcLmbVtUEfC4Pa9wYyRkCw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_OcLmbltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmb1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmcFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmcVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmcltUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmc1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmdFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmdVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmdltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmd1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmeFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmeVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmeltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLme1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_OcJJwFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmfFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_OcJJs1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmfVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmfltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcLmf1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM8FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM8VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM8ltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartCommand" commandName="Restart Command" category="_OcJJrltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM81tUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_OcJJtFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM9FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM9VtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.checkforupdates" commandName="Check For Embedded Software Packages Updates" description="Check For Embedded Software Packages Updates" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM9ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM91tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM-FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_OcJJqFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMM-VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_OcMM-ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM-1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMM_FtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_OcJJvltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMM_VtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_OcMM_ltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_OcMM_1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNAFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNAVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNAltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNA1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNBFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_OcJJwFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNBVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_OcJJoFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNBltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_OcJJv1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNB1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNCFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNCVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNCltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNC1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNDFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNDVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNDltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMND1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNEFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_OcJJoFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNEVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNEltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_OcJJvVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNE1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNFFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.threadx.trx_to_file_command" commandName="Export trace" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNFVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNFltUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_OcJJtFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNF1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_OcJJqltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNGFtUEfC4Pa9wYyRkCw" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_OcMNGVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNGltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNG1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNHFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNHVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNHltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNH1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_OcJJpltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNIFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNIVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNIltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.cmake.cmake_run_builder" commandName="cmake_run_builder" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNI1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNJFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNJVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNJltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.openconfig" commandName="Config" description="Configure SWV" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNJ1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_OcJJvFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNKFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNKVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNKltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_OcJJsVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNK1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNLFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNLVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNLltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNL1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNMFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNMVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNMltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNM1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNNFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNNVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_OcJJwFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNNltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNN1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNOFtUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.stlink.fwupgrade" commandName="ST-LINK &#x66f4;&#x65b0;" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNOVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNOltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNO1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_OcJJrVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNPFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_OcMNPVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_OcJJsFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNPltUEfC4Pa9wYyRkCw" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_OcMNP1tUEfC4Pa9wYyRkCw" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_OcMNQFtUEfC4Pa9wYyRkCw" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_OcMNQVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNQltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_OcJJrVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNQ1tUEfC4Pa9wYyRkCw" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_OcMNRFtUEfC4Pa9wYyRkCw" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_OcMNRVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNRltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.cmake.commands.cmakeimport" commandName="cmakeimport" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNR1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_OcJJsFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNSFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_OcJJvFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNSVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNSltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNS1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.p2.list" commandName="P2 IU List" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNTFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_OcJJr1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNTVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNTltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNT1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_OcJJsltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNUFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNUVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_OcJJqltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNUltUEfC4Pa9wYyRkCw" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_OcMNU1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNVFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNVVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_OcJJqltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNVltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNV1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNWFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNWVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNWltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNW1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNXFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNXVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_OcJJvltUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNXltUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_OcMNX1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_OcMNYFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_OcJJqFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNYVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_OcJJsFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNYltUEfC4Pa9wYyRkCw" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_OcMNY1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_OcJJqFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNZFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_OcMNZVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_OcJJuVtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNZltUEfC4Pa9wYyRkCw" elementId="title" name="Title"/>
    <parameters xmi:id="_OcMNZ1tUEfC4Pa9wYyRkCw" elementId="message" name="Message"/>
    <parameters xmi:id="_OcMNaFtUEfC4Pa9wYyRkCw" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_OcMNaVtUEfC4Pa9wYyRkCw" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_OcMNaltUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNa1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNbFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNbVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_OcJJv1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNbltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNb1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNcFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNcVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNcltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_OcJJoltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNc1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_OcJJuFtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNdFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_OcJJpVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNdVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_OcJJrVtUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNdltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNd1tUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.common.mx.manageembeddedsoftwarepackages" commandName="Manage Embedded Software Packages" description="Manage Embedded Software Packages" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNeFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_OcJJqFtUEfC4Pa9wYyRkCw">
    <parameters xmi:id="_OcMNeVtUEfC4Pa9wYyRkCw" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_OcMNeltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_OcJJo1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OcMNe1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_OcJJq1tUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omsz0FtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet/com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.action.TerminateAndRelaunch" commandName="&#x7ec8;&#x6b62;&#x5e76;&#x91cd;&#x65b0;&#x542f;&#x52a8;" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omta4FtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.informationcenter.actionSet3/com.st.stm32cube.ide.mcu.informationcenter.action1" commandName="Information Center" description="Information Center" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmuB8FtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmuB8VtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmuB8ltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmuB81tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmupAFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmupAVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmupAltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQEFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQEVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQEltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQE1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQFFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQFVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQFltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQF1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQGFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQGVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQGltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmvQG1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3IFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3IVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3IltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3I1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3JFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3JVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3JltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3J1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3KFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3KVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omv3KltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmweMFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmweMVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmweMltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmweM1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmweNFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmweNVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFQFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFQVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFQltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFQ1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFRFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFRVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFRltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFR1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFSFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFSVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmxFSltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTYFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTYVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTYltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTY1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTZFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTZVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTZltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTZ1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTaFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTaVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTaltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTa1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTbFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTbVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTbltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTb1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTcFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTcVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTcltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTc1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTdFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTdVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTdltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTd1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTeFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_OmyTeVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6cFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6cVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6cltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6c1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6dFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6dVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6dltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6d1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6eFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6eVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6eltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6e1tUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6fFtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6fVtUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <commands xmi:id="_Omy6fltUEfC4Pa9wYyRkCw" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_OcJJvltUEfC4Pa9wYyRkCw"/>
  <addons xmi:id="_Ob7uTVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_Ob7uTltUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_Ob7uT1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_Ob7uUFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_Ob7uUVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_Ob7uUltUEfC4Pa9wYyRkCw" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_Ob7uU1tUEfC4Pa9wYyRkCw" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_Ob7uVFtUEfC4Pa9wYyRkCw" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_Ob7uVVtUEfC4Pa9wYyRkCw" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_Ob7uVltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_OcAmwFtUEfC4Pa9wYyRkCw" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_t4Co8VyKEfC2q9u24cEywg" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_OcJJoFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_OcJJoVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_OcJJoltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_OcJJo1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_OcJJpFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_OcJJpVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_OcJJpltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_OcJJp1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_OcJJqFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_OcJJqVtUEfC4Pa9wYyRkCw" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_OcJJqltUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_OcJJq1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_OcJJrFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_OcJJrVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_OcJJrltUEfC4Pa9wYyRkCw" elementId="com.st.stm32cube.ide.mcu.debug.launch.restartCategory" name="Restart Category"/>
  <categories xmi:id="_OcJJr1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_OcJJsFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_OcJJsVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_OcJJsltUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_OcJJs1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_OcJJtFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_OcJJtVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_OcJJtltUEfC4Pa9wYyRkCw" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_OcJJt1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_OcJJuFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_OcJJuVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_OcJJultUEfC4Pa9wYyRkCw" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_OcJJu1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_OcJJvFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_OcJJvVtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_OcJJvltUEfC4Pa9wYyRkCw" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_OcJJv1tUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_OcJJwFtUEfC4Pa9wYyRkCw" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
