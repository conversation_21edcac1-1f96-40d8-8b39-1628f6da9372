<?xml version="1.0" encoding="UTF-8" standalone="no"?><project><configuration id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.debug.652942507" name="Debug"><extension point="org.eclipse.cdt.core.LanguageSettingsProvider"><provider id="com.st.stm32cube.ide.mcu.toolchain.armnone.setup.CrossBuiltinSpecsDetector"><language id="org.eclipse.cdt.core.gcc"><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/newlib-nano"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include-fixed"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_EPSILON__" value="0x1P-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MAX__" value="0X7FFFFFFFP-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MIN__" value="(-0X1P15K-0X1P15K)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__APCS_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARMEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_32BIT_STATE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_7EM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_EXT_IDIV__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_ISA_THUMB" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_PROFILE" value="77"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ASM_SYNTAX_UNIFIED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_EABI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_CLZ" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_COPROC" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_DSP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_FMA" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_IDIV" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_LDREX" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_QBIT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SAT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SIMD32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_UNALIGNED" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FP" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_PCS_VFP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_MINIMAL_ENUM" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_WCHAR_T" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQUIRE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQ_REL" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_CONSUME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_CONSUME" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELAXED" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELEASE" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_SEQ_CST" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_BIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DENORM_MIN__" value="((double)4.9406564584124654e-324L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_EPSILON__" value="((double)2.2204460492503131e-16L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_10_EXP__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX__" value="((double)1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN__" value="((double)2.2250738585072014e-308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_NORM_MAX__" value="((double)1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEC_EVAL_METHOD__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ELF__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FINITE_MATH_ONLY__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DENORM_MIN__" value="4.9406564584124654e-324F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_EPSILON__" value="2.2204460492503131e-16F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN__" value="2.2250738585072014e-308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_NORM_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DENORM_MIN__" value="1.4012984643248171e-45F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_EPSILON__" value="1.1920928955078125e-7F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN__" value="1.1754943508222875e-38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_NORM_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DENORM_MIN__" value="4.9406564584124654e-324F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_EPSILON__" value="2.2204460492503131e-16F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN__" value="2.2250738585072014e-308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_NORM_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DENORM_MIN__" value="1.4012984643248171e-45F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EPSILON__" value="1.1920928955078125e-7F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD_TS_18661_3__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_EXP__" value="4.9406564584124654e-324L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN__" value="1.1754943508222875e-38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_NORM_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_RADIX__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_EPSILON__" value="0x1P-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MAX__" value="0X7FFFP-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MIN__" value="(-0.5R-0.5R)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ASM_FLAG_OUTPUTS__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_CONSTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_DESTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_EXECUTION_CHARSET_NAME" value="&quot;UTF-8&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_MINOR__" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_PATCHLEVEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_STDC_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_WIDE_EXECUTION_CHARSET_NAME" value="&quot;UTF-32LE&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_ABI_VERSION" value="1018"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_TYPEINFO_EQUALITY_INLINE" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HAVE_SPECULATION_SAFE_VALUE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_C(c)" value="c ## L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_TYPE__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_EPSILON__" value="0x1P-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MIN__" value="(-0X1P31LK-0X1P31LK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_EPSILON__" value="2.2204460492503131e-16L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN__" value="2.2250738585072014e-308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_NORM_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_EPSILON__" value="0x1P-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MAX__" value="0X7FFFFFFFP-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MIN__" value="(-0.5LR-0.5LR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_EPSILON__" value="0x1P-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MIN__" value="(-0X1P31LLK-0X1P31LLK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_EPSILON__" value="0x1P-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MAX__" value="0X7FFFFFFFFFFFFFFFP-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MIN__" value="(-0.5LLR-0.5LLR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_WIDTH__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__NO_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__REGISTER_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_EPSILON__" value="0x1P-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MAX__" value="0X7FFFP-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MIN__" value="(-0X1P7HK-0X1P7HK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_EPSILON__" value="0x1P-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MAX__" value="0X7FP-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MIN__" value="(-0.5HR-0.5HR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_FLOAT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_INT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_POINTER__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SHORT__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WCHAR_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WINT_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_HOSTED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_16__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_VERSION__" value="201112L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMBEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMB_INTERWORK__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_FBIT__" value="127"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_EPSILON__" value="0x1P-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MAX__" value="0XFFFFFFFFP-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MIN__" value="0.0UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_EPSILON__" value="0x1P-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MAX__" value="0XFFFFP-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MIN__" value="0.0UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_C(c)" value="c ## UL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_EPSILON__" value="0x1P-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MIN__" value="0.0ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_EPSILON__" value="0x1P-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MAX__" value="0XFFFFFFFFP-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MIN__" value="0.0ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_EPSILON__" value="0x1P-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MIN__" value="0.0ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_EPSILON__" value="0x1P-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MAX__" value="0XFFFFFFFFFFFFFFFFP-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MIN__" value="0.0ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_EPSILON__" value="0x1P-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MAX__" value="0XFFFFP-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MIN__" value="0.0UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USER_LABEL_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USES_INITFINI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_EPSILON__" value="0x1P-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_FBIT__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MAX__" value="0XFFP-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MIN__" value="0.0UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_FBIT__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VERSION__" value="&quot;13.3.1 20240614&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VFP_FP__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__arm__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb2__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb__" value="1"><flag value="BUILTIN|READONLY"/></entry></language><language id="org.eclipse.cdt.core.g++"><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/newlib-nano"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/c++/13.3.1"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7e-m+fp/hard"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/c++/13.3.1/backward"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include-fixed"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_EPSILON__" value="0x1P-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MAX__" value="0X7FFFFFFFP-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MIN__" value="(-0X1P15K-0X1P15K)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__APCS_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARMEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_32BIT_STATE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_7EM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_EXT_IDIV__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_ISA_THUMB" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_PROFILE" value="77"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ASM_SYNTAX_UNIFIED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_EABI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_CLZ" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_COPROC" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_DSP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_FMA" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_IDIV" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_LDREX" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_QBIT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SAT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SIMD32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_UNALIGNED" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FP" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_PCS_VFP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_MINIMAL_ENUM" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_WCHAR_T" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQUIRE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQ_REL" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_CONSUME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELAXED" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELEASE" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_SEQ_CST" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BYTE_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_BIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DENORM_MIN__" value="double(4.9406564584124654e-324L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_EPSILON__" value="double(2.2204460492503131e-16L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX__" value="double(1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN__" value="double(2.2250738585072014e-308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_NORM_MAX__" value="double(1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEC_EVAL_METHOD__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEPRECATED" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ELF__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__EXCEPTIONS" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FINITE_MATH_ONLY__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DENORM_MIN__" value="4.9406564584124654e-324F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_EPSILON__" value="2.2204460492503131e-16F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN__" value="2.2250738585072014e-308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_NORM_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DENORM_MIN__" value="1.4012984643248171e-45F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_EPSILON__" value="1.1920928955078125e-7F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN__" value="1.1754943508222875e-38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_NORM_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DENORM_MIN__" value="4.9406564584124654e-324F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_EPSILON__" value="2.2204460492503131e-16F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN__" value="2.2250738585072014e-308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_NORM_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DENORM_MIN__" value="1.4012984643248171e-45F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EPSILON__" value="1.1920928955078125e-7F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD_TS_18661_3__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN__" value="1.1754943508222875e-38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_NORM_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_RADIX__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_EPSILON__" value="0x1P-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MAX__" value="0X7FFFP-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MIN__" value="(-0.5R-0.5R)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ASM_FLAG_OUTPUTS__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_CONSTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_DESTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_EXECUTION_CHARSET_NAME" value="&quot;UTF-8&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_MINOR__" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_PATCHLEVEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_STDC_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_WIDE_EXECUTION_CHARSET_NAME" value="&quot;UTF-32LE&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUG__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_ABI_VERSION" value="1018"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_EXPERIMENTAL_CXX0X__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_RTTI" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_TYPEINFO_EQUALITY_INLINE" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_WEAK__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HAVE_SPECULATION_SAFE_VALUE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_C(c)" value="c ## L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_EPSILON__" value="0x1P-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MIN__" value="(-0X1P31LK-0X1P31LK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DENORM_MIN__" value="4.9406564584124654e-324L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_EPSILON__" value="2.2204460492503131e-16L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN__" value="2.2250738585072014e-308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_NORM_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_EPSILON__" value="0x1P-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MAX__" value="0X7FFFFFFFP-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MIN__" value="(-0.5LR-0.5LR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_EPSILON__" value="0x1P-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MIN__" value="(-0X1P31LLK-0X1P31LLK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_EPSILON__" value="0x1P-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MAX__" value="0X7FFFFFFFFFFFFFFFP-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MIN__" value="(-0.5LLR-0.5LLR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__NO_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__REGISTER_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_EPSILON__" value="0x1P-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MAX__" value="0X7FFFP-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MIN__" value="(-0X1P7HK-0X1P7HK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_EPSILON__" value="0x1P-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MAX__" value="0X7FP-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MIN__" value="(-0.5HR-0.5HR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_FLOAT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_INT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_POINTER__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SHORT__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SIZE_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WCHAR_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WINT_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_HOSTED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_16__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMBEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMB_INTERWORK__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_FBIT__" value="127"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_EPSILON__" value="0x1P-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MAX__" value="0XFFFFFFFFP-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MIN__" value="0.0UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_EPSILON__" value="0x1P-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MAX__" value="0XFFFFP-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MIN__" value="0.0UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_C(c)" value="c ## UL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_EPSILON__" value="0x1P-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MIN__" value="0.0ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_EPSILON__" value="0x1P-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MAX__" value="0XFFFFFFFFP-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MIN__" value="0.0ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_EPSILON__" value="0x1P-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MIN__" value="0.0ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_EPSILON__" value="0x1P-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MAX__" value="0XFFFFFFFFFFFFFFFFP-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MIN__" value="0.0ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_EPSILON__" value="0x1P-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MAX__" value="0XFFFFP-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MIN__" value="0.0UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USER_LABEL_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USES_INITFINI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_EPSILON__" value="0x1P-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MAX__" value="0XFFP-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MIN__" value="0.0UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_FBIT__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VERSION__" value="&quot;13.3.1 20240614&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VFP_FP__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__arm__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cplusplus" value="201402L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_aggregate_nsdmi" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_alias_templates" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_attributes" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_binary_literals" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_constexpr" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_decltype" value="200707L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_decltype_auto" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_delegating_constructors" value="200604L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_digit_separators" value="201309L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_exceptions" value="199711L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_generic_lambdas" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_hex_float" value="201603L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_inheriting_constructors" value="201511L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_init_captures" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_initializer_lists" value="200806L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_lambdas" value="200907L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_nsdmi" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_range_based_for" value="200907L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_raw_strings" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_ref_qualifiers" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_return_type_deduction" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rtti" value="199711L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_runtime_arrays" value="198712L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rvalue_reference" value="200610L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rvalue_references" value="200610L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_sized_deallocation" value="201309L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_static_assert" value="200410L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_threadsafe_static_init" value="200806L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_unicode_characters" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_unicode_literals" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_user_defined_literals" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_variable_templates" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_variadic_templates" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb2__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb__" value="1"><flag value="BUILTIN|READONLY"/></entry></language></provider></extension></configuration><configuration id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.**********" name="Release"><extension point="org.eclipse.cdt.core.LanguageSettingsProvider"><provider id="com.st.stm32cube.ide.mcu.toolchain.armnone.setup.CrossBuiltinSpecsDetector"><language id="org.eclipse.cdt.core.gcc"><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/newlib-nano"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include-fixed"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_EPSILON__" value="0x1P-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MAX__" value="0X7FFFFFFFP-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MIN__" value="(-0X1P15K-0X1P15K)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__APCS_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARMEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_32BIT_STATE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_7EM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_EXT_IDIV__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_ISA_THUMB" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_PROFILE" value="77"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ASM_SYNTAX_UNIFIED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_EABI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_CLZ" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_COPROC" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_DSP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_FMA" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_IDIV" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_LDREX" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_QBIT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SAT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SIMD32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_UNALIGNED" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FP" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_PCS_VFP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_MINIMAL_ENUM" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_WCHAR_T" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQUIRE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQ_REL" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_CONSUME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELAXED" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELEASE" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_SEQ_CST" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BYTE_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_BIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DENORM_MIN__" value="((double)4.9406564584124654e-324L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_EPSILON__" value="((double)2.2204460492503131e-16L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX__" value="((double)1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN__" value="((double)2.2250738585072014e-308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_NORM_MAX__" value="((double)1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEC_EVAL_METHOD__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ELF__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FINITE_MATH_ONLY__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DENORM_MIN__" value="4.9406564584124654e-324F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_EPSILON__" value="2.2204460492503131e-16F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN__" value="2.2250738585072014e-308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_NORM_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DENORM_MIN__" value="1.4012984643248171e-45F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_EPSILON__" value="1.1920928955078125e-7F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN__" value="1.1754943508222875e-38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_NORM_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DENORM_MIN__" value="4.9406564584124654e-324F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_EPSILON__" value="2.2204460492503131e-16F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN__" value="2.2250738585072014e-308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_NORM_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DENORM_MIN__" value="1.4012984643248171e-45F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EPSILON__" value="1.1920928955078125e-7F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD_TS_18661_3__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN__" value="1.1754943508222875e-38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_NORM_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_RADIX__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_EPSILON__" value="0x1P-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MAX__" value="0X7FFFP-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MIN__" value="(-0.5R-0.5R)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ASM_FLAG_OUTPUTS__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_CONSTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_DESTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_EXECUTION_CHARSET_NAME" value="&quot;UTF-8&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_MINOR__" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_PATCHLEVEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_STDC_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_WIDE_EXECUTION_CHARSET_NAME" value="&quot;UTF-32LE&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_ABI_VERSION" value="1018"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_TYPEINFO_EQUALITY_INLINE" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HAVE_SPECULATION_SAFE_VALUE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_C(c)" value="c ## L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_EPSILON__" value="0x1P-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MIN__" value="(-0X1P31LK-0X1P31LK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DENORM_MIN__" value="4.9406564584124654e-324L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_EPSILON__" value="2.2204460492503131e-16L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN__" value="2.2250738585072014e-308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_NORM_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_EPSILON__" value="0x1P-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MAX__" value="0X7FFFFFFFP-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MIN__" value="(-0.5LR-0.5LR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_EPSILON__" value="0x1P-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MIN__" value="(-0X1P31LLK-0X1P31LLK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_EPSILON__" value="0x1P-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MAX__" value="0X7FFFFFFFFFFFFFFFP-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MIN__" value="(-0.5LLR-0.5LLR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__NO_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__REGISTER_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_EPSILON__" value="0x1P-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MAX__" value="0X7FFFP-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MIN__" value="(-0X1P7HK-0X1P7HK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_EPSILON__" value="0x1P-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MAX__" value="0X7FP-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MIN__" value="(-0.5HR-0.5HR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_FLOAT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_INT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_POINTER__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SHORT__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SIZE_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WCHAR_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WINT_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_HOSTED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_16__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_VERSION__" value="201112L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMBEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMB_INTERWORK__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_FBIT__" value="127"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_EPSILON__" value="0x1P-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MAX__" value="0XFFFFFFFFP-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MIN__" value="0.0UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_EPSILON__" value="0x1P-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MAX__" value="0XFFFFP-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MIN__" value="0.0UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_C(c)" value="c ## UL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_EPSILON__" value="0x1P-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MIN__" value="0.0ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_EPSILON__" value="0x1P-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MAX__" value="0XFFFFFFFFP-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MIN__" value="0.0ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_EPSILON__" value="0x1P-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MIN__" value="0.0ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_EPSILON__" value="0x1P-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MAX__" value="0XFFFFFFFFFFFFFFFFP-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MIN__" value="0.0ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_EPSILON__" value="0x1P-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MAX__" value="0XFFFFP-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MIN__" value="0.0UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USER_LABEL_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USES_INITFINI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_EPSILON__" value="0x1P-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MAX__" value="0XFFP-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MIN__" value="0.0UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_FBIT__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VERSION__" value="&quot;13.3.1 20240614&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VFP_FP__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__arm__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb2__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb__" value="1"><flag value="BUILTIN|READONLY"/></entry></language><language id="org.eclipse.cdt.core.g++"><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/newlib-nano"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/c++/13.3.1"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7e-m+fp/hard"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include/c++/13.3.1/backward"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/lib/gcc/arm-none-eabi/13.3.1/include-fixed"><flag value="BUILTIN|READONLY"/></entry><entry kind="includePath" name="C:/ST/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/arm-none-eabi/include"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_EPSILON__" value="0x1P-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MAX__" value="0X7FFFFFFFP-15K"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ACCUM_MIN__" value="(-0X1P15K-0X1P15K)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__APCS_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARMEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_32BIT_STATE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_7EM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_EXT_IDIV__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_ISA_THUMB" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ARCH_PROFILE" value="77"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_ASM_SYNTAX_UNIFIED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_EABI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_CLZ" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_COPROC" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_DSP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_FMA" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_IDIV" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_LDREX" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_QBIT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SAT" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_SIMD32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FEATURE_UNALIGNED" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_FP" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_PCS_VFP" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_MINIMAL_ENUM" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ARM_SIZEOF_WCHAR_T" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQUIRE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_ACQ_REL" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_CONSUME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELAXED" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_RELEASE" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ATOMIC_SEQ_CST" value="5"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BIGGEST_ALIGNMENT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__BYTE_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_BIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__CHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DENORM_MIN__" value="double(4.9406564584124654e-324L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_EPSILON__" value="double(2.2204460492503131e-16L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MAX__" value="double(1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_MIN__" value="double(2.2250738585072014e-308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DBL_NORM_MAX__" value="double(1.7976931348623157e+308L)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEC_EVAL_METHOD__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DEPRECATED" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__DQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ELF__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__EXCEPTIONS" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FINITE_MATH_ONLY__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLOAT_WORD_ORDER__" value="__ORDER_LITTLE_ENDIAN__"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DENORM_MIN__" value="4.9406564584124654e-324F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_EPSILON__" value="2.2204460492503131e-16F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_MIN__" value="2.2250738585072014e-308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32X_NORM_MAX__" value="1.7976931348623157e+308F32x"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DENORM_MIN__" value="1.4012984643248171e-45F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_EPSILON__" value="1.1920928955078125e-7F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_MIN__" value="1.1754943508222875e-38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT32_NORM_MAX__" value="3.4028234663852886e+38F32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DENORM_MIN__" value="4.9406564584124654e-324F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_EPSILON__" value="2.2204460492503131e-16F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_MIN__" value="2.2250738585072014e-308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT64_NORM_MAX__" value="1.7976931348623157e+308F64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DECIMAL_DIG__" value="9"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DENORM_MIN__" value="1.4012984643248171e-45F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_DIG__" value="6"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EPSILON__" value="1.1920928955078125e-7F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD_TS_18661_3__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_EVAL_METHOD__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MANT_DIG__" value="24"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_10_EXP__" value="38"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX_EXP__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_10_EXP__" value="(-37)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN_EXP__" value="(-125)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_MIN__" value="1.1754943508222875e-38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_NORM_MAX__" value="3.4028234663852886e+38F"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FLT_RADIX__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FP_FAST_FMAF32" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_EPSILON__" value="0x1P-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MAX__" value="0X7FFFP-15R"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__FRACT_MIN__" value="(-0.5R-0.5R)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ASM_FLAG_OUTPUTS__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_BOOL_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR16_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR32_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_CHAR_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_INT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LLONG_LOCK_FREE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_LONG_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_POINTER_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_SHORT_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_TEST_AND_SET_TRUEVAL" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_ATOMIC_WCHAR_T_LOCK_FREE" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_CONSTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_DESTRUCTIVE_SIZE" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GCC_IEC_559_COMPLEX" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_EXECUTION_CHARSET_NAME" value="&quot;UTF-8&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_MINOR__" value="3"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_PATCHLEVEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_STDC_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC_WIDE_EXECUTION_CHARSET_NAME" value="&quot;UTF-32LE&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUC__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GNUG__" value="13"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_ABI_VERSION" value="1018"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_EXPERIMENTAL_CXX0X__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_RTTI" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_TYPEINFO_EQUALITY_INLINE" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__GXX_WEAK__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HAVE_SPECULATION_SAFE_VALUE" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__HQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_C(c)" value="c ## L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_C(c)" value="c ## LL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTMAX_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INTPTR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST16_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_FAST8_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_TYPE__" value="short int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST16_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_TYPE__" value="long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST32_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_TYPE__" value="long long int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST64_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_TYPE__" value="signed char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_LEAST8_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__INT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_EPSILON__" value="0x1P-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LACCUM_MIN__" value="(-0X1P31LK-0X1P31LK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DECIMAL_DIG__" value="17"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DENORM_MIN__" value="4.9406564584124654e-324L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_DIG__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_EPSILON__" value="2.2204460492503131e-16L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_DENORM__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_INFINITY__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_HAS_QUIET_NAN__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_IS_IEC_60559__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MANT_DIG__" value="53"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_10_EXP__" value="308"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX_EXP__" value="1024"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_10_EXP__" value="(-307)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN_EXP__" value="(-1021)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_MIN__" value="2.2250738585072014e-308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LDBL_NORM_MAX__" value="1.7976931348623157e+308L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_EPSILON__" value="0x1P-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MAX__" value="0X7FFFFFFFP-31LR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LFRACT_MIN__" value="(-0.5LR-0.5LR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_EPSILON__" value="0x1P-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MAX__" value="0X7FFFFFFFFFFFFFFFP-31LLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLACCUM_MIN__" value="(-0X1P31LLK-0X1P31LLK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_EPSILON__" value="0x1P-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MAX__" value="0X7FFFFFFFFFFFFFFFP-63LLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LLFRACT_MIN__" value="(-0.5LLR-0.5LLR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_MAX__" value="0x7fffffffffffffffLL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_LONG_WIDTH__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_MAX__" value="0x7fffffffL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__LONG_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__NO_INLINE__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_BIG_ENDIAN__" value="4321"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_LITTLE_ENDIAN__" value="1234"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ORDER_PDP_ENDIAN__" value="3412"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PRAGMA_REDEFINE_EXTNAME" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__PTRDIFF_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__QQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__REGISTER_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_EPSILON__" value="0x1P-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MAX__" value="0X7FFFP-7HK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SACCUM_MIN__" value="(-0X1P7HK-0X1P7HK)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_FBIT__" value="15"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_MAX__" value="0x7f"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SCHAR_WIDTH__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_EPSILON__" value="0x1P-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_FBIT__" value="7"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MAX__" value="0X7FP-7HR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SFRACT_MIN__" value="(-0.5HR-0.5HR)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_MAX__" value="0x7fff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SHRT_WIDTH__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MAX__" value="0x7fffffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_MIN__" value="(-__SIG_ATOMIC_MAX__ - 1)"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_TYPE__" value="int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIG_ATOMIC_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_FLOAT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_INT__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_DOUBLE__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG_LONG__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_LONG__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_POINTER__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_PTRDIFF_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SHORT__" value="2"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_SIZE_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WCHAR_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZEOF_WINT_T__" value="4"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SIZE_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_FBIT__" value="31"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__SQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_HOSTED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_16__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC_UTF_32__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__STDC__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_FBIT__" value="63"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMBEL__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__THUMB_INTERWORK__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_FBIT__" value="127"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__TQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_EPSILON__" value="0x1P-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MAX__" value="0XFFFFFFFFP-16UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UACCUM_MIN__" value="0.0UK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDA_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UDQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_EPSILON__" value="0x1P-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MAX__" value="0XFFFFP-16UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UFRACT_MIN__" value="0.0UR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHA_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UHQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_C(c)" value="c ## UL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_C(c)" value="c"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_C(c)" value="c ## ULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTMAX_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINTPTR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST16_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST32_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_FAST8_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_MAX__" value="0xffff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST16_TYPE__" value="short unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_MAX__" value="0xffffffffUL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST32_TYPE__" value="long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_MAX__" value="0xffffffffffffffffULL"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST64_TYPE__" value="long long unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_MAX__" value="0xff"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UINT_LEAST8_TYPE__" value="unsigned char"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_EPSILON__" value="0x1P-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULACCUM_MIN__" value="0.0ULK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_EPSILON__" value="0x1P-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MAX__" value="0XFFFFFFFFP-32ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULFRACT_MIN__" value="0.0ULR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_EPSILON__" value="0x1P-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_IBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MAX__" value="0XFFFFFFFFFFFFFFFFP-32ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLACCUM_MIN__" value="0.0ULLK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_EPSILON__" value="0x1P-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MAX__" value="0XFFFFFFFFFFFFFFFFP-64ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__ULLFRACT_MIN__" value="0.0ULLR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UQQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_EPSILON__" value="0x1P-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_IBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MAX__" value="0XFFFFP-8UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USACCUM_MIN__" value="0.0UHK"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_FBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USA_IBIT__" value="16"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USER_LABEL_PREFIX__" value=""><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USES_INITFINI__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_EPSILON__" value="0x1P-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_FBIT__" value="8"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MAX__" value="0XFFP-8UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USFRACT_MIN__" value="0.0UHR"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_FBIT__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__USQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_FBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTA_IBIT__" value="64"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_FBIT__" value="128"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__UTQ_IBIT__" value="0"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VERSION__" value="&quot;13.3.1 20240614&quot;"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__VFP_FP__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_UNSIGNED__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WCHAR_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MAX__" value="0xffffffffU"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_MIN__" value="0U"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_TYPE__" value="unsigned int"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__WINT_WIDTH__" value="32"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__arm__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cplusplus" value="201402L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_aggregate_nsdmi" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_alias_templates" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_attributes" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_binary_literals" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_constexpr" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_decltype" value="200707L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_decltype_auto" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_delegating_constructors" value="200604L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_digit_separators" value="201309L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_exceptions" value="199711L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_generic_lambdas" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_hex_float" value="201603L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_inheriting_constructors" value="201511L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_init_captures" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_initializer_lists" value="200806L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_lambdas" value="200907L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_nsdmi" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_range_based_for" value="200907L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_raw_strings" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_ref_qualifiers" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_return_type_deduction" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rtti" value="199711L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_runtime_arrays" value="198712L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rvalue_reference" value="200610L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_rvalue_references" value="200610L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_sized_deallocation" value="201309L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_static_assert" value="200410L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_threadsafe_static_init" value="200806L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_unicode_characters" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_unicode_literals" value="200710L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_user_defined_literals" value="200809L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_variable_templates" value="201304L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__cpp_variadic_templates" value="200704L"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb2__" value="1"><flag value="BUILTIN|READONLY"/></entry><entry kind="macro" name="__thumb__" value="1"><flag value="BUILTIN|READONLY"/></entry></language></provider></extension></configuration></project>