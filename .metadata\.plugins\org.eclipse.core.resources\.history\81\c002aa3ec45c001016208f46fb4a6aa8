#include "main.h"
#include "adc.h"
#include "dac.h"
#include "dma.h"
#include "gpio.h"
#include "audio_process.h"

#define AUDIO_BUFFER_SIZE 1024
uint16_t adc_buffer[AUDIO_BUFFER_SIZE];
uint16_t dac_buffer[AUDIO_BUFFER_SIZE];

volatile uint16_t ADC_LATEST = 0; // 实时保存最新ADC采集值
volatile uint16_t DAC_LATEST = 0; // 实时保存最新DAC输出值

// 添加LED引脚定义用于调试
#define LED_PIN GPIO_PIN_13
#define LED_PORT GPIOC

#define AUDIO_GAIN 3.0f  // 增加增益让声音更清晰

int main(void)
{
  HAL_Init();
  SystemClock_Config();
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_ADC1_Init();
  MX_DAC_Init();
  
  /* 初始化音频处理器 */
  AudioProcess_Init();

  /* 配置LED用于调试 */
  GPIO_InitTypeDef GPIO_InitStruct = {0};
  __HAL_RCC_GPIOC_CLK_ENABLE();
  GPIO_InitStruct.Pin = LED_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(LED_PORT, &GPIO_InitStruct);

  /* 启动DAC并设置初始值 */
  HAL_DAC_Start(&hdac, DAC_CHANNEL_1);
  HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, 2048);
  
  /* 启动ADC的DMA模式 */
  HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_buffer, AUDIO_BUFFER_SIZE);

  while (1)
  {
    /* 主循环无需处理，全部由DMA和回调完成 */
  }
}

void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_ADC12;
  PeriphClkInit.Adc12ClockSelection = RCC_ADC12PLLCLK_DIV1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* ADC DMA回调函数 */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc) {
    uint16_t half_size = AUDIO_BUFFER_SIZE / 2;
    static int32_t prev = 0;
    for(uint16_t i = 0; i < half_size; i++) {
        // 将ADC值转换为以1.65V为中心的交流信号
        int32_t sample = (int32_t)adc_buffer[i + half_size] - 2048; // 去直流，得到交流分量
        
        // 轻微滤波（递归平均）
        sample = (sample + prev) / 2;
        prev = sample;
        
        // 增益处理
        sample = sample * AUDIO_GAIN;
        
        // 将交流信号重新映射到DAC的1.65V中心点
        sample += 2048; // 加回1.65V偏置
        
        // 限幅保护
        if(sample < 0) sample = 0;
        if(sample > 4095) sample = 4095;
        
        dac_buffer[i + half_size] = (uint16_t)sample;
        HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_buffer[i + half_size]);
    }
    ADC_LATEST = adc_buffer[half_size];
    DAC_LATEST = dac_buffer[half_size];
}

void HAL_ADC_ConvHalfCpltCallback(ADC_HandleTypeDef* hadc) {
    uint16_t half_size = AUDIO_BUFFER_SIZE / 2;
    static int32_t prev = 0;
    for(uint16_t i = 0; i < half_size; i++) {
        // 将ADC值转换为以1.65V为中心的交流信号
        int32_t sample = (int32_t)adc_buffer[i] - 2048; // 去直流，得到交流分量
        
        // 轻微滤波（递归平均）
        sample = (sample + prev) / 2;
        prev = sample;
        
        // 增益处理
        sample = sample * AUDIO_GAIN;
        
        // 将交流信号重新映射到DAC的1.65V中心点
        sample += 2048; // 加回1.65V偏置
        
        // 限幅保护
        if(sample < 0) sample = 0;
        if(sample > 4095) sample = 4095;
        
        dac_buffer[i] = (uint16_t)sample;
        HAL_DAC_SetValue(&hdac, DAC_CHANNEL_1, DAC_ALIGN_12B_R, dac_buffer[i]);
    }
    ADC_LATEST = adc_buffer[0];
    DAC_LATEST = dac_buffer[0];
}

void Error_Handler(void)
{
  __disable_irq();
  while (1)
  {
  }
}    
