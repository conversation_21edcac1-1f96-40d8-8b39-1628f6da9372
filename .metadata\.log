!SESSION 2025-07-08 01:02:48.577 -----------------------------------------------
eclipse.buildId=Version 1.19.0
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-07-08 01:02:57.418
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-07-08 01:02:57.419
!MESSAGE Log4j2 initialized with config file C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\.metadata\.log4j2.xml

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:02:58.256
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:02:58.256
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:02:58.256
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:02:58.257
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-07-08 01:02:59.756
!MESSAGE Started RMI Server, listening on port 41337

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.432
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.432
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.432
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.433
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.449
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.449
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.449
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.450
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.450
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.450
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.450
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.450
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.450
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.451
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.452
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.452
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.452
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.452
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.452
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.452
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.452
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.453
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.453
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.453
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.454
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.502
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.502
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.502
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.502
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.503
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.503
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.503
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.503
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.503
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.503
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.504
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.504
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.504
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.504
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.505
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.505
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.505
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.505
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.505
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.505
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property http.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-07-08 01:03:02.506
!MESSAGE System property https.proxyPort is set to 8889 but should not be set.

!ENTRY org.eclipse.cdt.core 1 0 2025-07-08 01:04:38.034
!MESSAGE Indexed 'ZKSD Motor' (19 sources, 82 headers) in 1.62 sec: 4,895 declarations; 26,004 references; 0 unresolved inclusions; 1 syntax errors; 2 unresolved names (0.0065%)

!ENTRY com.st.stm32cube.ide.mcu.debug.launch 2 0 2025-07-08 01:24:03.913
!MESSAGE Won't launch debug session since we failed to locate the binary.

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 01:27:00.696
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Caused by: java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 01:27:00.697
!MESSAGE Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 0
java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 01:27:09.006
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Caused by: java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 01:27:09.006
!MESSAGE Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 0
java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY com.st.stm32cube.ide.mcu.debug.launch 2 0 2025-07-08 01:27:16.885
!MESSAGE Could not verify ST device! Please verify that the latest version of the GDB-server is used for the connection.

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 01:27:26.073
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Caused by: java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 01:27:26.073
!MESSAGE Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 0
java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 104 2025-07-08 01:44:00.728
!MESSAGE Program file does not exist
!STACK 0
java.io.FileNotFoundException: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\ZKSD Motor\Debug\ZKSD Motor.elf not found
	at org.eclipse.cdt.dsf.gdb.launching.LaunchUtils.verifyProgramPath(LaunchUtils.java:130)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.checkBinaryDetails(GdbLaunchDelegate.java:330)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:152)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 104 2025-07-08 01:44:00.729
!MESSAGE C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\ZKSD Motor\Debug\ZKSD Motor.elf not found
!STACK 0
java.io.FileNotFoundException: C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\ZKSD Motor\Debug\ZKSD Motor.elf not found
	at org.eclipse.cdt.dsf.gdb.launching.LaunchUtils.verifyProgramPath(LaunchUtils.java:130)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.checkBinaryDetails(GdbLaunchDelegate.java:330)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:152)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 02:06:33.478
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 02:06:33.478
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 02:06:33.478
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 02:06:44.334
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 02:06:44.334
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 02:06:44.334
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 02:15:28.485
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 02:15:28.485
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 02:15:28.485
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY com.st.stm32cube.ide.mcu.toolchain 4 4 2025-07-08 02:23:30.917
!MESSAGE ST-LINK GDB server failed to start (exit code = 11)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 02:23:30.938
!MESSAGE Error in final launch sequence:

Failed to start GDB server
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to start GDB server
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Caused by: org.eclipse.core.runtime.CoreException: ST-Link设备进行初始化时出错。
原因：（11) 未知原因。请检查连接到目标机的电源和电缆。
	at com.st.stm32cube.ide.mcu.debug.stlink.StLinkDebugHardware.verifyServer(StLinkDebugHardware.java:1934)
	at com.st.stm32cube.ide.mcu.debug.launch.export.HardwareDebugUtil.startServer(HardwareDebugUtil.java:199)
	at com.st.stm32cube.ide.mcu.debug.launch.export.HardwareDebugUtil.startServer(HardwareDebugUtil.java:93)
	at com.st.stm32cube.ide.mcu.debug.launch.LaunchSequenceUtil.stepStartGDBServer(LaunchSequenceUtil.java:206)
	at com.st.stm32cube.ide.mcu.debug.launch.GDBExtendedJtagDSFFinalLaunchSequence_7_12.stepStartGDBServer(GDBExtendedJtagDSFFinalLaunchSequence_7_12.java:104)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.cdt.dsf.concurrent.ReflectionSequence$ReflectionStep.execute(ReflectionSequence.java:160)
	at org.eclipse.cdt.dsf.concurrent.Sequence.executeStep(Sequence.java:475)
	at org.eclipse.cdt.dsf.concurrent.Sequence$2.handleSuccess(Sequence.java:437)
	at org.eclipse.cdt.dsf.concurrent.RequestMonitor.handleCompleted(RequestMonitor.java:391)
	at org.eclipse.cdt.dsf.concurrent.RequestMonitor$2.run(RequestMonitor.java:317)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
!SUBENTRY 1 com.st.stm32cube.ide.mcu.debug.launch 4 0 2025-07-08 02:23:30.938
!MESSAGE Failed to start GDB server
!STACK 1
org.eclipse.core.runtime.CoreException: ST-Link设备进行初始化时出错。
原因：（11) 未知原因。请检查连接到目标机的电源和电缆。
	at com.st.stm32cube.ide.mcu.debug.stlink.StLinkDebugHardware.verifyServer(StLinkDebugHardware.java:1934)
	at com.st.stm32cube.ide.mcu.debug.launch.export.HardwareDebugUtil.startServer(HardwareDebugUtil.java:199)
	at com.st.stm32cube.ide.mcu.debug.launch.export.HardwareDebugUtil.startServer(HardwareDebugUtil.java:93)
	at com.st.stm32cube.ide.mcu.debug.launch.LaunchSequenceUtil.stepStartGDBServer(LaunchSequenceUtil.java:206)
	at com.st.stm32cube.ide.mcu.debug.launch.GDBExtendedJtagDSFFinalLaunchSequence_7_12.stepStartGDBServer(GDBExtendedJtagDSFFinalLaunchSequence_7_12.java:104)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.eclipse.cdt.dsf.concurrent.ReflectionSequence$ReflectionStep.execute(ReflectionSequence.java:160)
	at org.eclipse.cdt.dsf.concurrent.Sequence.executeStep(Sequence.java:475)
	at org.eclipse.cdt.dsf.concurrent.Sequence$2.handleSuccess(Sequence.java:437)
	at org.eclipse.cdt.dsf.concurrent.RequestMonitor.handleCompleted(RequestMonitor.java:391)
	at org.eclipse.cdt.dsf.concurrent.RequestMonitor$2.run(RequestMonitor.java:317)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
!SUBENTRY 2 com.st.stm32cube.ide.mcu.toolchain 4 0 2025-07-08 02:23:30.938
!MESSAGE ST-Link设备进行初始化时出错。
原因：（11) 未知原因。请检查连接到目标机的电源和电缆。

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 02:27:59.994
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 02:27:59.994
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 02:27:59.994
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 02:34:06.575
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Caused by: java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 02:34:06.576
!MESSAGE Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 0
java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 02:35:02.718
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:336)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Caused by: java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 02:35:02.718
!MESSAGE Failed to execute MI command:
target remote localhost:61234

Error message from debugger back end:
Could not read registers; remote failure reply 'E31'
!STACK 0
java.lang.Exception: Could not read registers; remote failure reply 'E31'
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY com.st.stm32cube.ide.mcu.debug 4 0 2025-07-08 02:56:50.886
!MESSAGE Error - No active DSF-Session. tmpSessionId = 49 (contextManager row 121)

!ENTRY com.st.stm32cube.ide.mcu.debug 4 0 2025-07-08 02:56:50.887
!MESSAGE Existing sessions are:

!ENTRY com.st.stm32cube.ide.mcu.debug 4 0 2025-07-08 02:56:50.887
!MESSAGE Error - No active DSF-Session. tmpSessionId = 49 (contextManager row 121)

!ENTRY com.st.stm32cube.ide.mcu.debug 4 0 2025-07-08 02:56:50.888
!MESSAGE Existing sessions are:

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 03:14:34.691
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 03:14:34.691
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 03:14:34.691
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 03:18:52.055
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 03:18:52.055
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 03:18:52.055
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 03:35:04.940
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 03:35:04.940
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 03:35:04.940
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.core 1 0 2025-07-08 03:52:15.796
!MESSAGE Indexed 'MotorCubeMx' (686 sources, 267 headers) in 6.5 sec: 44,373 declarations; 398,824 references; 46 unresolved inclusions; 230 syntax errors; 15,039 unresolved names (3.3%)
!SESSION 2025-07-08 12:00:29.482 -----------------------------------------------
eclipse.buildId=Version 1.19.0
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-07-08 12:00:33.519
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-07-08 12:00:33.520
!MESSAGE Log4j2 initialized with config file C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\.metadata\.log4j2.xml

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-07-08 12:00:37.607
!MESSAGE Started RMI Server, listening on port 41337

!ENTRY com.st.stm32cube.ide.mcu.debug.launch 2 0 2025-07-08 12:13:10.448
!MESSAGE Won't launch debug session since we failed to locate the binary.

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 12:19:44.037
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 12:19:44.038
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 12:19:44.038
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 12:32:33.726
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 12:32:33.726
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 12:32:33.726
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 12:44:50.958
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 12:44:50.958
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 12:44:50.958
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 12:49:58.265
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 12:49:58.265
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 12:49:58.265
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 12:52:02.244
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:334)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 12:52:02.244
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 12:52:02.244
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-08 15:05:50.820
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:336)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-08 15:05:50.820
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-08 15:05:50.820
!MESSAGE Failed to execute MI command:
load "C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\ZKSD Motor\\Debug\\ZKSD Motor.elf" 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.core.jobs 4 2 2025-07-08 15:51:42.811
!MESSAGE An internal error occurred during: "Suspend monitor job.".
!STACK 0
java.util.concurrent.RejectedExecutionException: Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@29684574[Not completed, task = java.util.concurrent.Executors$RunnableAdapter@4d90e16f[Wrapped task = org.eclipse.cdt.dsf.gdb.service.GDBRunControl_7_12$MonitorSuspendJob$2@2789f1d4]] rejected from org.eclipse.cdt.dsf.concurrent.DefaultDsfExecutor@13d2cbf8[Terminated, pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 16965]
	at java.base/java.util.concurrent.ThreadPoolExecutor$AbortPolicy.rejectedExecution(ThreadPoolExecutor.java:2081)
	at java.base/java.util.concurrent.ThreadPoolExecutor.reject(ThreadPoolExecutor.java:841)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.delayedExecute(ScheduledThreadPoolExecutor.java:340)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.schedule(ScheduledThreadPoolExecutor.java:562)
	at org.eclipse.cdt.dsf.concurrent.DefaultDsfExecutor.schedule(DefaultDsfExecutor.java:450)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor.submit(ScheduledThreadPoolExecutor.java:715)
	at org.eclipse.cdt.dsf.concurrent.DefaultDsfExecutor.submit(DefaultDsfExecutor.java:482)
	at org.eclipse.cdt.dsf.gdb.service.GDBRunControl_7_12$MonitorSuspendJob.run(GDBRunControl_7_12.java:358)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)

!ENTRY org.eclipse.cdt.core 1 0 2025-07-08 21:23:28.901
!MESSAGE Indexed 'STMH_CM4' (68 sources, 154 headers) in 4.22 sec: 18,775 declarations; 133,716 references; 10 unresolved inclusions; 2,304 syntax errors; 6,356 unresolved names (4%)

!ENTRY org.eclipse.cdt.core 1 0 2025-07-08 21:23:31.865
!MESSAGE Indexed 'STMH_CM7' (24 sources, 85 headers) in 1.23 sec: 7,874 declarations; 58,814 references; 0 unresolved inclusions; 1 syntax errors; 2 unresolved names (0.003%)

!ENTRY org.eclipse.cdt.core 1 0 2025-07-08 21:59:21.621
!MESSAGE Indexed 'STMH_CM4' (67 sources, 155 headers) in 2.3 sec: 18,962 declarations; 142,270 references; 0 unresolved inclusions; 1 syntax errors; 2 unresolved names (0.0012%)
!SESSION 2025-07-08 22:10:38.282 -----------------------------------------------
eclipse.buildId=Version 1.19.0
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-07-08 22:10:42.001
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-07-08 22:10:42.002
!MESSAGE Log4j2 initialized with config file C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\.metadata\.log4j2.xml

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-07-08 22:10:46.428
!MESSAGE Started RMI Server, listening on port 41337
!SESSION 2025-07-09 14:05:28.611 -----------------------------------------------
eclipse.buildId=Version 1.19.0
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-07-09 14:05:32.039
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-07-09 14:05:32.040
!MESSAGE Log4j2 initialized with config file C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\.metadata\.log4j2.xml

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-07-09 14:05:36.045
!MESSAGE Started RMI Server, listening on port 41337

!ENTRY org.eclipse.cdt.core 1 0 2025-07-09 14:50:39.004
!MESSAGE Indexed 'WDHW' (19 sources, 82 headers) in 1.29 sec: 4,895 declarations; 26,004 references; 0 unresolved inclusions; 1 syntax errors; 2 unresolved names (0.0065%)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-09 20:57:28.158
!MESSAGE Error in final launch sequence:

Failed to execute MI command:
load C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\WDHW\\Debug\\WDHW.elf 

Error message from debugger back end:
Error finishing flash operation
!STACK 1
org.eclipse.core.runtime.CoreException: Failed to execute MI command:
load C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\WDHW\\Debug\\WDHW.elf 

Error message from debugger back end:
Error finishing flash operation
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:336)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
Contains: Failed to execute MI command:
load C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\WDHW\\Debug\\WDHW.elf 

Error message from debugger back end:
Error finishing flash operation
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)
!SUBENTRY 1 org.eclipse.cdt.dsf 4 10004 2025-07-09 20:57:28.160
!MESSAGE Failed to execute MI command:
load C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\WDHW\\Debug\\WDHW.elf 

Error message from debugger back end:
Error finishing flash operation
!SUBENTRY 2 org.eclipse.cdt.dsf.gdb 4 10004 2025-07-09 20:57:28.160
!MESSAGE Failed to execute MI command:
load C:\\Users\\<USER>\\STM32CubeIDE\\workspace_1.19.0\\WDHW\\Debug\\WDHW.elf 

Error message from debugger back end:
Error finishing flash operation
!STACK 0
java.lang.Exception: Error finishing flash operation
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.processMIOutput(AbstractMIControl.java:1024)
	at org.eclipse.cdt.dsf.mi.service.command.AbstractMIControl$RxThread.run(AbstractMIControl.java:853)

!ENTRY org.eclipse.cdt.dsf.gdb 4 5012 2025-07-09 21:13:58.224
!MESSAGE Error in final launch sequence:

Setup exceptions
!STACK 1
org.eclipse.core.runtime.CoreException: Setup exceptions
	at org.eclipse.cdt.dsf.concurrent.Query.get(Query.java:112)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugSession(GdbLaunchDelegate.java:252)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launchDebugger(GdbLaunchDelegate.java:109)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunchDelegate.launch(GdbLaunchDelegate.java:97)
	at com.st.stm32cube.ide.mcu.debug.launch.DSFDelegate.launch(DSFDelegate.java:336)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:805)
	at org.eclipse.debug.internal.core.LaunchConfiguration.launch(LaunchConfiguration.java:716)
	at org.eclipse.debug.internal.ui.DebugUIPlugin.buildAndLaunch(DebugUIPlugin.java:1054)
	at org.eclipse.debug.internal.ui.DebugUIPlugin$1.run(DebugUIPlugin.java:1257)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
!SUBENTRY 1 com.st.stm32cube.ide.mcu.debug.launch 4 0 2025-07-09 21:13:58.224
!MESSAGE Setup exceptions

!ENTRY org.eclipse.core.jobs 4 2 2025-07-09 21:17:47.692
!MESSAGE An internal error occurred during: "Execute Debug Command".
!STACK 0
org.eclipse.core.runtime.AssertionFailedException: assertion failed: The application has not been initialized.
	at org.eclipse.core.runtime.Assert.isTrue(Assert.java:119)
	at org.eclipse.core.internal.runtime.InternalPlatform.assertInitialized(InternalPlatform.java:185)
	at org.eclipse.core.internal.runtime.InternalPlatform.getAdapterManager(InternalPlatform.java:206)
	at org.eclipse.core.runtime.Platform.getAdapterManager(Platform.java:661)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunch.getAdapter(GdbLaunch.java:454)
	at org.eclipse.cdt.dsf.gdb.launching.GdbLaunch.disconnect(GdbLaunch.java:338)
	at org.eclipse.debug.internal.core.commands.DisconnectCommand.execute(DisconnectCommand.java:35)
	at org.eclipse.debug.internal.core.commands.ForEachCommand.doExecute(ForEachCommand.java:36)
	at org.eclipse.debug.core.commands.AbstractDebugCommand$1.run(AbstractDebugCommand.java:215)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)

!ENTRY org.eclipse.core.jobs 2 2 2025-07-09 21:17:48.005
!MESSAGE Job found still running after platform shutdown.  Jobs should be canceled by the plugin that scheduled them during shutdown: org.eclipse.cdt.dsf.gdb.internal.ui.console.GdbBasicCliConsole$InputReadJob RUNNING
	 at java.base/java.lang.Object.wait0(Native Method)
	 at java.base/java.lang.Object.wait(Object.java:366)
	 at java.base/java.lang.Object.wait(Object.java:339)
	 at org.eclipse.ui.console.IOConsoleInputStream.waitForData(IOConsoleInputStream.java:140)
	 at org.eclipse.ui.console.IOConsoleInputStream.read(IOConsoleInputStream.java:91)
	 at org.eclipse.ui.console.IOConsoleInputStream.read(IOConsoleInputStream.java:113)
	 at org.eclipse.cdt.dsf.gdb.internal.ui.console.GdbBasicCliConsole$InputReadJob.run(GdbBasicCliConsole.java:248)
	 at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
!SESSION 2025-07-10 03:20:38.352 -----------------------------------------------
eclipse.buildId=Version 1.19.0
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-07-10 03:20:42.205
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-07-10 03:20:42.206
!MESSAGE Log4j2 initialized with config file C:\Users\<USER>\STM32CubeIDE\workspace_1.19.0\.metadata\.log4j2.xml

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-07-10 03:20:47.519
!MESSAGE Started RMI Server, listening on port 41337
