#include "audio_process.h"

/**
 * @brief 初始化音频处理器
 */
void AudioProcess_Init(void)
{
    /* 初始化代码，如果需要的话 */
}

/**
 * @brief 应用增益
 * @param buffer: 音频缓冲区
 * @param length: 数据长度
 * @param gain: 增益值
 */
void AudioProcess_ApplyGain(uint16_t* buffer, uint16_t length, float gain)
{
    uint16_t i;
    
    for(i = 0; i < length; i++) {
        uint32_t sample = (uint32_t)((float)buffer[i] * gain);
        if(sample > 4095) sample = 4095; // 限制最大值
        buffer[i] = (uint16_t)sample;
    }
}

/**
 * @brief 应用噪声门限
 * @param buffer: 音频缓冲区
 * @param length: 数据长度
 * @param threshold: 噪声门限
 */
void AudioProcess_ApplyNoiseGate(uint16_t* buffer, uint16_t length, uint16_t threshold)
{
    uint16_t i;
    
    for(i = 0; i < length; i++) {
        if(buffer[i] < threshold) {
            buffer[i] = 0; // 静音
        }
    }
}

/**
 * @brief 音频处理主函数
 * @param input: 输入音频数据
 * @param output: 输出音频数据
 * @param length: 数据长度
 */
void AudioProcess_Process(uint16_t* input, uint16_t* output, uint16_t length)
{
    uint16_t i;
    
    /* 复制输入数据到输出缓冲区 */
    for(i = 0; i < length; i++) {
        output[i] = input[i];
    }
    
    /* 应用噪声门限 */
    AudioProcess_ApplyNoiseGate(output, length, AUDIO_NOISE_GATE);
    
    /* 应用增益 */
    AudioProcess_ApplyGain(output, length, AUDIO_GAIN_DEFAULT);
} 